{"prettier.enable": true, "eslint.enable": true, "editor.formatOnSave": true, "editor.defaultFormatter": "biomejs.biome", "editor.codeActionsOnSave": {"source.organizeImports": "explicit", "source.sortImports": "explicit", "source.fixAll": "explicit", "source.fixAll.eslint": "explicit", "source.organizeImports.biome": "explicit", "source.fixAll.biome": "explicit", "quickfix.biome": "explicit"}, "editor.tabSize": 2, "editor.insertSpaces": true, "[javascriptreact]": {"editor.defaultFormatter": "biomejs.biome"}, "[typescriptreact]": {"editor.defaultFormatter": "vscode.typescript-language-features"}, "[javascript]": {"editor.defaultFormatter": "biomejs.biome"}, "[typescript]": {"editor.defaultFormatter": "biomejs.biome"}, "[json]": {"editor.defaultFormatter": "biomejs.biome"}, "[html]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "typescript.tsdk": "node_modules/typescript/lib", "typescript.preferences.autoImportFileExcludePatterns": ["next/router.d.ts", "next/dist/client/router.d.ts"], "cSpell.words": ["<PERSON><PERSON>", "NEXTAUTH", "redirections", "ttplatform"]}