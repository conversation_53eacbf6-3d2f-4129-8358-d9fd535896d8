import type { Schema, Struct } from '@strapi/strapi'

export interface CardsGlobeCard extends Struct.ComponentSchema {
  collectionName: 'components_cards_globe_cards'
  info: {
    description: ''
    displayName: 'Globe_Card'
    icon: 'dashboard'
  }
  attributes: {
    description: Schema.Attribute.String
    span: Schema.Attribute.Enumeration<['one', 'two', 'three']>
    title: Schema.Attribute.String
  }
}

export interface CardsGraphCard extends Struct.ComponentSchema {
  collectionName: 'components_cards_graph_cards'
  info: {
    description: ''
    displayName: 'Graph_Card'
    icon: 'dashboard'
  }
  attributes: {
    description: Schema.Attribute.String
    highlighted_text: Schema.Attribute.String
    span: Schema.Attribute.Enumeration<['one', 'two', 'three']>
    title: Schema.Attribute.String
    top_items: Schema.Attribute.Component<'items.graph-card-top-items', true>
  }
}

export interface CardsHoverExpandCard extends Struct.ComponentSchema {
  collectionName: 'components_cards_hover_expand_cards'
  info: {
    displayName: 'Hover_Expand_Card'
  }
  attributes: {
    adminLabel: Schema.Attribute.String & Schema.Attribute.Private
    background: Schema.Attribute.Component<'styles.background-styles', false>
    content: Schema.Attribute.Component<'elementals.heading', false>
  }
}

export interface CardsHoverOverlayCard extends Struct.ComponentSchema {
  collectionName: 'components_cards_hover_overlay_cards'
  info: {
    displayName: 'Hover_Overlay_Card'
  }
  attributes: {
    adminLabel: Schema.Attribute.String & Schema.Attribute.Private
    background: Schema.Attribute.Component<'styles.background-styles', false>
    hover_content: Schema.Attribute.Component<'elementals.heading', false>
    opacity: Schema.Attribute.Decimal &
      Schema.Attribute.SetMinMax<
        {
          max: 1
          min: 0
        },
        number
      >
    overlay_color: Schema.Attribute.String &
      Schema.Attribute.CustomField<'plugin::color-picker.color'>
    text: Schema.Attribute.String
  }
}

export interface CardsPopupCard extends Struct.ComponentSchema {
  collectionName: 'components_cards_popup_cards'
  info: {
    displayName: 'Popup_Card'
  }
  attributes: {
    buttons: Schema.Attribute.Component<'shared.button', true>
    content: Schema.Attribute.Blocks
    image: Schema.Attribute.Media<'images'>
    sub_title: Schema.Attribute.Text
    title: Schema.Attribute.String
  }
}

export interface CardsRayCard extends Struct.ComponentSchema {
  collectionName: 'components_cards_ray_cards'
  info: {
    description: ''
    displayName: 'Ray_Card'
    icon: 'dashboard'
  }
  attributes: {
    after_ray_items: Schema.Attribute.Component<'items.ray-items', false>
    before_ray_items: Schema.Attribute.Component<'items.ray-items', false>
    description: Schema.Attribute.String
    span: Schema.Attribute.Enumeration<['one', 'two', 'three']>
    title: Schema.Attribute.String
  }
}

export interface CardsSocialMediaCard extends Struct.ComponentSchema {
  collectionName: 'components_cards_social_media_cards'
  info: {
    description: ''
    displayName: 'Social_Media_Card'
    icon: 'dashboard'
  }
  attributes: {
    Description: Schema.Attribute.String
    span: Schema.Attribute.Enumeration<['one', 'two', 'three']>
    Title: Schema.Attribute.String
  }
}

export interface DynamicZoneAccordionSection extends Struct.ComponentSchema {
  collectionName: 'components_dynamic_zone_accordion_sections'
  info: {
    displayName: 'Accordion_Section'
  }
  attributes: {
    accordion: Schema.Attribute.Component<'elementals.accordion', false>
    adminLabel: Schema.Attribute.String & Schema.Attribute.Private
    heading: Schema.Attribute.Component<'elementals.heading', false>
    styles: Schema.Attribute.Component<'styles.section-styles', false>
  }
}

export interface DynamicZoneContactUsSection extends Struct.ComponentSchema {
  collectionName: 'components_dynamic_zone_contact_us_sections'
  info: {
    displayName: 'Contact_Us_Section'
  }
  attributes: {
    adminLabel: Schema.Attribute.String & Schema.Attribute.Private
    heading: Schema.Attribute.Component<'elementals.heading', false>
    styles: Schema.Attribute.Component<'styles.section-styles', false>
  }
}

export interface DynamicZoneDistributedBrands extends Struct.ComponentSchema {
  collectionName: 'components_dynamic_zone_distributed_brands'
  info: {
    displayName: 'Distributed_Brands'
  }
  attributes: {
    adminLabel: Schema.Attribute.String & Schema.Attribute.Private
    brands: Schema.Attribute.Relation<'oneToMany', 'api::brand.brand'>
    heading: Schema.Attribute.Component<'elementals.heading', false>
    styles: Schema.Attribute.Component<'styles.section-styles', false>
  }
}

export interface DynamicZoneExecutiveTeamSection
  extends Struct.ComponentSchema {
  collectionName: 'components_dynamic_zone_executive_team_sections'
  info: {
    displayName: 'Executive_Team_Section'
  }
  attributes: {
    adminLabel: Schema.Attribute.String & Schema.Attribute.Private
    heading: Schema.Attribute.Component<'elementals.heading', false>
    items: Schema.Attribute.Relation<
      'oneToMany',
      'api::team-member.team-member'
    >
    styles: Schema.Attribute.Component<'styles.section-styles', false>
  }
}

export interface DynamicZoneFeaturedPromotions extends Struct.ComponentSchema {
  collectionName: 'components_dynamic_zone_featured_promotions'
  info: {
    displayName: 'Featured_Promotions'
  }
  attributes: {
    adminLabel: Schema.Attribute.String & Schema.Attribute.Private
    heading: Schema.Attribute.Component<'elementals.heading', false>
    media_image: Schema.Attribute.Media<'images'>
    styles: Schema.Attribute.Component<'styles.section-styles', false>
  }
}

export interface DynamicZoneFeatures extends Struct.ComponentSchema {
  collectionName: 'components_dynamic_zone_features'
  info: {
    description: ''
    displayName: 'Features'
    icon: 'bulletList'
  }
  attributes: {
    globe_card: Schema.Attribute.Component<'cards.globe-card', false>
    graph_card: Schema.Attribute.Component<'cards.graph-card', false>
    heading: Schema.Attribute.String
    ray_card: Schema.Attribute.Component<'cards.ray-card', false>
    social_media_card: Schema.Attribute.Component<
      'cards.social-media-card',
      false
    >
    sub_heading: Schema.Attribute.String
  }
}

export interface DynamicZoneFeaturesSection extends Struct.ComponentSchema {
  collectionName: 'components_dynamic_zone_features_sections'
  info: {
    displayName: 'Features_Section'
  }
  attributes: {
    adminLabel: Schema.Attribute.String & Schema.Attribute.Private
    features: Schema.Attribute.Component<'elementals.icon-box-list', false>
    image: Schema.Attribute.Media<'images'>
    styles: Schema.Attribute.Component<'styles.section-styles', false>
  }
}

export interface DynamicZoneFormSubscribe extends Struct.ComponentSchema {
  collectionName: 'components_dynamic_zone_form_subscribes'
  info: {
    displayName: 'Form_Subscribe'
  }
  attributes: {
    adminLabel: Schema.Attribute.String & Schema.Attribute.Private
    heading: Schema.Attribute.Component<'elementals.heading', false>
    social_items: Schema.Attribute.Component<'shared.social-link', true>
    social_text: Schema.Attribute.String &
      Schema.Attribute.DefaultTo<'K\u1EBFt n\u1ED1i v\u1EDBi Ph\u00FA Th\u00E1i Cat'>
    styles: Schema.Attribute.Component<'styles.section-styles', false>
  }
}

export interface DynamicZoneHeroSlideSection extends Struct.ComponentSchema {
  collectionName: 'components_dynamic_zone_hero_slide_sections'
  info: {
    displayName: 'Hero_Slide_Section'
  }
  attributes: {
    adminLabel: Schema.Attribute.String & Schema.Attribute.Private
    items: Schema.Attribute.Component<'items.hero-slide-item', true>
  }
}

export interface DynamicZoneHoverExpandCardCollection
  extends Struct.ComponentSchema {
  collectionName: 'components_dynamic_zone_hover_expand_card_collections'
  info: {
    displayName: 'Hover_Expand_Card_Collection'
  }
  attributes: {
    adminLabel: Schema.Attribute.String & Schema.Attribute.Private
    heading: Schema.Attribute.Component<'elementals.heading', false>
    items: Schema.Attribute.Component<'cards.hover-expand-card', true>
    styles: Schema.Attribute.Component<'styles.section-styles', false>
  }
}

export interface DynamicZoneHoverOverlayCardCollection
  extends Struct.ComponentSchema {
  collectionName: 'components_dynamic_zone_hover_overlay_card_collections'
  info: {
    displayName: 'Hover_Overlay_Card_Collection'
    icon: 'landscape'
  }
  attributes: {
    adminLabel: Schema.Attribute.String & Schema.Attribute.Private
    heading: Schema.Attribute.Component<'elementals.heading', false>
    items: Schema.Attribute.Component<'cards.hover-overlay-card', true>
    styles: Schema.Attribute.Component<'styles.section-styles', false>
  }
}

export interface DynamicZoneHtml extends Struct.ComponentSchema {
  collectionName: 'components_dynamic_zone_htmls'
  info: {
    displayName: 'HTML'
  }
  attributes: {
    adminLabel: Schema.Attribute.String & Schema.Attribute.Private
    is_open: Schema.Attribute.Boolean & Schema.Attribute.DefaultTo<true>
    json: Schema.Attribute.JSON &
      Schema.Attribute.DefaultTo<{
        class: ''
        style: {
          background: 'transparent'
        }
      }>
    styles: Schema.Attribute.Component<'styles.section-styles', false>
  }
}

export interface DynamicZoneIconBoxListSection extends Struct.ComponentSchema {
  collectionName: 'components_dynamic_zone_icon_box_list_sections'
  info: {
    displayName: 'Icon_Box_List_Section'
  }
  attributes: {
    adminLabel: Schema.Attribute.String & Schema.Attribute.Private
    heading: Schema.Attribute.Component<'elementals.heading', false>
    icon_box_list: Schema.Attribute.Component<'elementals.icon-box-list', false>
    styles: Schema.Attribute.Component<'styles.section-styles', false>
  }
}

export interface DynamicZoneImageBoxSection extends Struct.ComponentSchema {
  collectionName: 'components_dynamic_zone_image_box_sections'
  info: {
    displayName: 'Image_Box_Section'
    icon: 'picture'
  }
  attributes: {
    adminLabel: Schema.Attribute.String & Schema.Attribute.Private
    heading: Schema.Attribute.Component<'elementals.heading', false>
    items: Schema.Attribute.Component<'elementals.image-box', true>
    styles: Schema.Attribute.Component<'styles.section-styles', false>
    type: Schema.Attribute.Enumeration<['timeline', 'grid', 'slide']>
  }
}

export interface DynamicZoneImageSliderSection extends Struct.ComponentSchema {
  collectionName: 'components_dynamic_zone_image_slider_sections'
  info: {
    displayName: 'Image_Slider_Section'
  }
  attributes: {
    adminLabel: Schema.Attribute.String & Schema.Attribute.Private
    heading: Schema.Attribute.Component<'elementals.heading', false>
    slider: Schema.Attribute.Component<'elementals.image-slider', false>
    styles: Schema.Attribute.Component<'styles.section-styles', false>
  }
}

export interface DynamicZoneInfoBlock extends Struct.ComponentSchema {
  collectionName: 'components_dynamic_zone_info_blocks'
  info: {
    displayName: 'Info_Block'
  }
  attributes: {
    adminLabel: Schema.Attribute.String & Schema.Attribute.Private
    image_position: Schema.Attribute.Enumeration<['left', 'right']> &
      Schema.Attribute.DefaultTo<'right'>
    info_list: Schema.Attribute.Component<'items.info-item', true>
    media_image: Schema.Attribute.Component<'elementals.media-image', false>
    styles: Schema.Attribute.Component<'styles.section-styles', false>
  }
}

export interface DynamicZoneJobListingsSection extends Struct.ComponentSchema {
  collectionName: 'components_dynamic_zone_job_listings_sections'
  info: {
    displayName: 'Job_Listings_Section'
  }
  attributes: {
    adminLabel: Schema.Attribute.String & Schema.Attribute.Private
    heading: Schema.Attribute.Component<'elementals.heading', false>
    pagination: Schema.Attribute.Component<'shared.pagination', false>
    styles: Schema.Attribute.Component<'styles.section-styles', false>
  }
}

export interface DynamicZoneLatestNewsSection extends Struct.ComponentSchema {
  collectionName: 'components_dynamic_zone_latest_news_sections'
  info: {
    displayName: 'Latest_News_Section'
  }
  attributes: {
    adminLabel: Schema.Attribute.String & Schema.Attribute.Private
    heading: Schema.Attribute.Component<'elementals.heading', false>
    styles: Schema.Attribute.Component<'styles.section-styles', false>
  }
}

export interface DynamicZoneLatestPromotionsSection
  extends Struct.ComponentSchema {
  collectionName: 'components_dynamic_zone_latest_promotions_sections'
  info: {
    displayName: 'Latest_Promotions_Section'
  }
  attributes: {
    adminLabel: Schema.Attribute.String & Schema.Attribute.Private
    heading: Schema.Attribute.Component<'elementals.heading', false>
    styles: Schema.Attribute.Component<'styles.section-styles', false>
  }
}

export interface DynamicZoneLocationsSection extends Struct.ComponentSchema {
  collectionName: 'components_dynamic_zone_locations_sections'
  info: {
    displayName: 'Locations_Section'
    icon: 'globe'
  }
  attributes: {
    adminLabel: Schema.Attribute.String & Schema.Attribute.Private
    heading: Schema.Attribute.Component<'elementals.heading', false>
    items: Schema.Attribute.Relation<'oneToMany', 'api::branch.branch'>
    styles: Schema.Attribute.Component<'styles.section-styles', false>
  }
}

export interface DynamicZoneMediaTextSection extends Struct.ComponentSchema {
  collectionName: 'components_dynamic_zone_media_text_sections'
  info: {
    displayName: 'Media_Text_Section'
  }
  attributes: {
    adminLabel: Schema.Attribute.String & Schema.Attribute.Private
    buttons: Schema.Attribute.Component<'shared.button', true>
    container_styles: Schema.Attribute.Component<
      'styles.background-styles',
      false
    >
    description: Schema.Attribute.Blocks
    heading: Schema.Attribute.Component<'items.text-item', false>
    image: Schema.Attribute.Component<'elementals.media-image', false>
    image_position: Schema.Attribute.Enumeration<['left', 'right']> &
      Schema.Attribute.DefaultTo<'right'>
    layout: Schema.Attribute.Enumeration<['split', 'large-media']> &
      Schema.Attribute.DefaultTo<'split'>
    styles: Schema.Attribute.Component<'styles.section-styles', false>
    sub_heading: Schema.Attribute.Component<'items.text-item', false>
  }
}

export interface DynamicZoneNewsFeaturedSection extends Struct.ComponentSchema {
  collectionName: 'components_dynamic_zone_news_featured_sections'
  info: {
    displayName: 'News_Featured_Section'
  }
  attributes: {
    custom_news: Schema.Attribute.Relation<'oneToMany', 'api::article.article'>
    limit: Schema.Attribute.Integer & Schema.Attribute.DefaultTo<3>
  }
}

export interface DynamicZoneNewsListingSection extends Struct.ComponentSchema {
  collectionName: 'components_dynamic_zone_news_listing_sections'
  info: {
    displayName: 'News_Listing_Section'
  }
  attributes: {
    heading: Schema.Attribute.Component<'elementals.heading', false>
    pagination: Schema.Attribute.Component<'shared.pagination', false>
  }
}

export interface DynamicZoneNewsRelated extends Struct.ComponentSchema {
  collectionName: 'components_dynamic_zone_news_relateds'
  info: {
    displayName: 'News_Related'
  }
  attributes: {
    adminLabel: Schema.Attribute.String & Schema.Attribute.Private
    heading: Schema.Attribute.Component<'elementals.heading', false>
    news: Schema.Attribute.Relation<'oneToMany', 'api::article.article'>
    styles: Schema.Attribute.Component<'styles.section-styles', false>
  }
}

export interface DynamicZoneOtherServicesSection
  extends Struct.ComponentSchema {
  collectionName: 'components_dynamic_zone_other_services_sections'
  info: {
    displayName: 'Other_Services_Section'
  }
  attributes: {
    adminLabel: Schema.Attribute.String & Schema.Attribute.Private
    heading: Schema.Attribute.Component<'elementals.heading', false>
    styles: Schema.Attribute.Component<'styles.section-styles', false>
  }
}

export interface DynamicZonePageHeroSection extends Struct.ComponentSchema {
  collectionName: 'components_dynamic_zone_page_hero_sections'
  info: {
    displayName: 'Page_Hero_Section'
  }
  attributes: {
    adminLabel: Schema.Attribute.String & Schema.Attribute.Private
    heading: Schema.Attribute.Component<'elementals.heading', false>
    image: Schema.Attribute.Component<'elementals.media-image', false>
    styles: Schema.Attribute.Component<'styles.section-styles', false>
  }
}

export interface DynamicZonePartnersCommitmentSection
  extends Struct.ComponentSchema {
  collectionName: 'components_dynamic_zone_partners_commitment_sections'
  info: {
    displayName: 'Partners_Commitment_Section'
  }
  attributes: {
    adminLabel: Schema.Attribute.String & Schema.Attribute.Private
    styles: Schema.Attribute.Component<'styles.section-styles', false>
  }
}

export interface DynamicZoneProductCategoriesSection
  extends Struct.ComponentSchema {
  collectionName: 'components_dynamic_zone_product_categories_sections'
  info: {
    displayName: 'Product_Categories_Section'
  }
  attributes: {
    adminLabel: Schema.Attribute.String & Schema.Attribute.Private
    heading: Schema.Attribute.Component<'elementals.heading', false>
    styles: Schema.Attribute.Component<'styles.section-styles', false>
  }
}

export interface DynamicZonePromotionsFeaturedSection
  extends Struct.ComponentSchema {
  collectionName: 'components_dynamic_zone_promotions_featured_sections'
  info: {
    displayName: 'Promotions_Featured_Section'
  }
  attributes: {
    custom_promotions: Schema.Attribute.Relation<
      'oneToMany',
      'api::promotion.promotion'
    >
    limit: Schema.Attribute.Integer & Schema.Attribute.DefaultTo<3>
  }
}

export interface DynamicZonePromotionsListingSection
  extends Struct.ComponentSchema {
  collectionName: 'components_dynamic_zone_promotions_listing_sections'
  info: {
    displayName: 'Promotions_Listing_Section'
  }
  attributes: {
    heading: Schema.Attribute.Component<'elementals.heading', false>
    pagination: Schema.Attribute.Component<'shared.pagination', false>
  }
}

export interface DynamicZoneRelatedArticles extends Struct.ComponentSchema {
  collectionName: 'components_dynamic_zone_related_articles'
  info: {
    description: ''
    displayName: 'Related_Articles_Section'
    icon: 'bulletList'
  }
  attributes: {
    adminLabel: Schema.Attribute.String & Schema.Attribute.Private
    articles: Schema.Attribute.Relation<'oneToMany', 'api::article.article'>
    heading: Schema.Attribute.Component<'elementals.heading', false>
    styles: Schema.Attribute.Component<'styles.section-styles', false>
  }
}

export interface DynamicZoneRelatedIndustries extends Struct.ComponentSchema {
  collectionName: 'components_dynamic_zone_related_industries'
  info: {
    displayName: 'Related_Industries'
  }
  attributes: {
    adminLabel: Schema.Attribute.String & Schema.Attribute.Private
    heading: Schema.Attribute.Component<'elementals.heading', false>
    styles: Schema.Attribute.Component<'styles.section-styles', false>
  }
}

export interface DynamicZoneRelatedProductCategories
  extends Struct.ComponentSchema {
  collectionName: 'components_dynamic_zone_related_product_categories'
  info: {
    displayName: 'Related_Product_Categories'
    icon: 'bulletList'
  }
  attributes: {
    adminLabel: Schema.Attribute.String & Schema.Attribute.Private
    heading: Schema.Attribute.Component<'elementals.heading', false>
    product_categories: Schema.Attribute.Relation<
      'oneToMany',
      'api::product-category.product-category'
    >
    styles: Schema.Attribute.Component<'styles.section-styles', false>
  }
}

export interface DynamicZoneRelatedProducts extends Struct.ComponentSchema {
  collectionName: 'components_dynamic_zone_related_products'
  info: {
    displayName: 'Related_Products_Section'
    icon: 'stack'
  }
  attributes: {
    heading: Schema.Attribute.Component<'elementals.heading', false>
    products: Schema.Attribute.Relation<'oneToMany', 'api::product.product'>
    styles: Schema.Attribute.Component<'styles.section-styles', false>
  }
}

export interface DynamicZoneRelatedSolutionsSection
  extends Struct.ComponentSchema {
  collectionName: 'components_dynamic_zone_related_solutions_sections'
  info: {
    displayName: 'Related_Solutions_Section'
    icon: 'lightbulb'
  }
  attributes: {
    adminLabel: Schema.Attribute.String & Schema.Attribute.Private
    heading: Schema.Attribute.Component<'elementals.heading', false>
    solutions: Schema.Attribute.Relation<
      'oneToMany',
      'api::industrial-solution-category.industrial-solution-category'
    >
    styles: Schema.Attribute.Component<'styles.section-styles', false>
  }
}

export interface DynamicZoneServiceListSection extends Struct.ComponentSchema {
  collectionName: 'components_dynamic_zone_service_list_sections'
  info: {
    displayName: 'Service_List_Section'
  }
  attributes: {
    adminLabel: Schema.Attribute.String & Schema.Attribute.Private
    heading: Schema.Attribute.Component<'elementals.heading', false>
    styles: Schema.Attribute.Component<'styles.section-styles', false>
  }
}

export interface DynamicZoneSolutionApplicationsSection
  extends Struct.ComponentSchema {
  collectionName: 'components_dynamic_zone_solution_applications_sections'
  info: {
    displayName: 'Solution_Applications_Section'
    icon: 'lightbulb'
  }
  attributes: {
    adminLabel: Schema.Attribute.String & Schema.Attribute.Private
    heading: Schema.Attribute.Component<'elementals.heading', false>
    styles: Schema.Attribute.Component<'styles.section-styles', false>
  }
}

export interface DynamicZoneSolutionCategoriesSection
  extends Struct.ComponentSchema {
  collectionName: 'components_dynamic_zone_solution_categories_sections'
  info: {
    displayName: 'Solution_Categories_Section'
  }
  attributes: {
    adminLabel: Schema.Attribute.String & Schema.Attribute.Private
    heading: Schema.Attribute.Component<'elementals.heading', false>
    styles: Schema.Attribute.Component<'styles.section-styles', false>
  }
}

export interface DynamicZoneSolutionMediaGallery
  extends Struct.ComponentSchema {
  collectionName: 'components_dynamic_zone_solution_media_galleries'
  info: {
    displayName: 'Solution_Media_Gallery'
  }
  attributes: {
    adminLabel: Schema.Attribute.String & Schema.Attribute.Private
    heading: Schema.Attribute.Component<'elementals.heading', false>
    styles: Schema.Attribute.Component<'styles.section-styles', false>
  }
}

export interface DynamicZoneSolutionPortfolioSection
  extends Struct.ComponentSchema {
  collectionName: 'components_dynamic_zone_solution_portfolio_sections'
  info: {
    displayName: 'Solution_Portfolio_Section'
  }
  attributes: {
    adminLabel: Schema.Attribute.String & Schema.Attribute.Private
    heading: Schema.Attribute.Component<'elementals.heading', false>
    items: Schema.Attribute.Relation<
      'oneToMany',
      'api::industrial-solution.industrial-solution'
    >
    styles: Schema.Attribute.Component<'styles.section-styles', false>
  }
}

export interface DynamicZoneSolutionsByApplication
  extends Struct.ComponentSchema {
  collectionName: 'components_dynamic_zone_solutions_by_applications'
  info: {
    displayName: 'Solutions_By_Category'
  }
  attributes: {
    adminLabel: Schema.Attribute.String & Schema.Attribute.Private
    styles: Schema.Attribute.Component<'styles.section-styles', false>
  }
}

export interface DynamicZoneStatsSection extends Struct.ComponentSchema {
  collectionName: 'components_dynamic_zone_stats_sections'
  info: {
    displayName: 'Stats_Section'
  }
  attributes: {
    adminLabel: Schema.Attribute.String & Schema.Attribute.Private
    container_styles: Schema.Attribute.Component<
      'styles.background-styles',
      false
    >
    heading: Schema.Attribute.Component<'elementals.heading', false>
    image_overlay: Schema.Attribute.Media<'images'>
    items: Schema.Attribute.Component<'items.stat', true>
    styles: Schema.Attribute.Component<'styles.section-styles', false>
  }
}

export interface DynamicZoneStepGuideSection extends Struct.ComponentSchema {
  collectionName: 'components_dynamic_zone_step_guide_sections'
  info: {
    displayName: 'Step_Guide_Section'
  }
  attributes: {
    adminLabel: Schema.Attribute.String & Schema.Attribute.Private
    heading: Schema.Attribute.Component<'elementals.heading', false>
    steps: Schema.Attribute.Component<'cards.popup-card', true>
    styles: Schema.Attribute.Component<'styles.section-styles', false>
  }
}

export interface DynamicZoneSupportCenterSection
  extends Struct.ComponentSchema {
  collectionName: 'components_dynamic_zone_support_center_sections'
  info: {
    displayName: 'Support_Center_Section'
  }
  attributes: {
    adminLabel: Schema.Attribute.String & Schema.Attribute.Private
    heading: Schema.Attribute.Component<'elementals.heading', false>
    pagination: Schema.Attribute.Component<'shared.pagination', false>
    styles: Schema.Attribute.Component<'styles.section-styles', false>
  }
}

export interface DynamicZoneTestimonialsSection extends Struct.ComponentSchema {
  collectionName: 'components_dynamic_zone_testimonials_sections'
  info: {
    displayName: 'Testimonials_Section'
  }
  attributes: {
    adminLabel: Schema.Attribute.String & Schema.Attribute.Private
    heading: Schema.Attribute.Component<'elementals.heading', false>
    items: Schema.Attribute.Relation<
      'oneToMany',
      'api::testimonial.testimonial'
    >
    styles: Schema.Attribute.Component<'styles.section-styles', false>
  }
}

export interface DynamicZoneTimelineSection extends Struct.ComponentSchema {
  collectionName: 'components_dynamic_zone_timeline_sections'
  info: {
    displayName: 'Timeline_Section'
  }
  attributes: {
    adminLabel: Schema.Attribute.String & Schema.Attribute.Private
    heading: Schema.Attribute.Component<'elementals.heading', false>
    styles: Schema.Attribute.Component<'styles.section-styles', false>
    timeline: Schema.Attribute.Component<'elementals.timeline', false>
  }
}

export interface DynamicZoneVideoSection extends Struct.ComponentSchema {
  collectionName: 'components_dynamic_zone_video_sections'
  info: {
    displayName: 'Video_Section'
  }
  attributes: {
    adminLabel: Schema.Attribute.String & Schema.Attribute.Private
    heading: Schema.Attribute.Component<'elementals.heading', false>
    styles: Schema.Attribute.Component<'styles.section-styles', false>
    video_gallery: Schema.Attribute.Component<'elementals.video-gallery', false>
  }
}

export interface DynamicZoneWidget extends Struct.ComponentSchema {
  collectionName: 'components_dynamic_zone_widgets'
  info: {
    displayName: 'widget'
    icon: 'code'
  }
  attributes: {
    adminLabel: Schema.Attribute.String & Schema.Attribute.Private
    widget: Schema.Attribute.Relation<'oneToOne', 'api::widget.widget'>
  }
}

export interface ElementalsAccordion extends Struct.ComponentSchema {
  collectionName: 'components_elementals_accordions'
  info: {
    displayName: 'accordion'
  }
  attributes: {
    background_color: Schema.Attribute.String &
      Schema.Attribute.CustomField<'plugin::color-picker.color'>
    image: Schema.Attribute.Media<'images'>
    item_border_radius: Schema.Attribute.Integer
    items: Schema.Attribute.Component<'items.accordion-item', true>
    show_order: Schema.Attribute.Boolean & Schema.Attribute.DefaultTo<false>
    text_color: Schema.Attribute.String &
      Schema.Attribute.CustomField<'plugin::color-picker.color'>
  }
}

export interface ElementalsBannerAds extends Struct.ComponentSchema {
  collectionName: 'components_elementals_banner_ads'
  info: {
    displayName: 'Banner-Ads'
  }
  attributes: {
    adminLabel: Schema.Attribute.String & Schema.Attribute.Private
    image: Schema.Attribute.Media<'images'>
    link: Schema.Attribute.Component<'shared.link', false>
  }
}

export interface ElementalsCompareImages extends Struct.ComponentSchema {
  collectionName: 'components_elementals_compare_images'
  info: {
    displayName: 'Compare_Images'
  }
  attributes: {
    adminLabel: Schema.Attribute.String & Schema.Attribute.Private
    border_radius: Schema.Attribute.Integer
    first_image: Schema.Attribute.Media<'images'>
    second_image: Schema.Attribute.Media<'images'>
  }
}

export interface ElementalsEditor extends Struct.ComponentSchema {
  collectionName: 'components_elementals_editors'
  info: {
    displayName: 'Editor'
  }
  attributes: {
    adminLabel: Schema.Attribute.String & Schema.Attribute.Private
    ckeidtor: Schema.Attribute.RichText &
      Schema.Attribute.CustomField<
        'plugin::ckeditor.CKEditor',
        {
          licenseKey: 'eyJhbGciOiJFUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************.DMOArIydvMbb7FAHz3rR7_52QrG2i15MkqBhASZma63OAOsRQuUQhN-a5PFpMgOkpBMk85wXpcUzTZzJcCNUFg'
          output: 'HTML'
          preset: 'rich'
        }
      >
    editor: Schema.Attribute.Blocks
  }
}

export interface ElementalsFooterContact extends Struct.ComponentSchema {
  collectionName: 'components_elementals_footer_contacts'
  info: {
    displayName: 'footer_contact'
  }
  attributes: {
    adminLabel: Schema.Attribute.String & Schema.Attribute.Private
    items: Schema.Attribute.Component<'items.contact-item', true>
    title: Schema.Attribute.String
  }
}

export interface ElementalsGenerator extends Struct.ComponentSchema {
  collectionName: 'components_elementals_generators'
  info: {
    displayName: 'Generator'
  }
  attributes: {
    generator_capacity_types: Schema.Attribute.String
    generator_list: Schema.Attribute.Component<'items.generator-item', true>
    generator_type: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.DefaultTo<'M\u00E1y ph\u00E1t \u0111i\u1EC7n diels'>
  }
}

export interface ElementalsHeading extends Struct.ComponentSchema {
  collectionName: 'components_elementals_headings'
  info: {
    displayName: 'Heading'
  }
  attributes: {
    adminLabel: Schema.Attribute.String & Schema.Attribute.Private
    buttons: Schema.Attribute.Component<'shared.button', true>
    heading: Schema.Attribute.Component<'items.text-item', false>
    sub_heading: Schema.Attribute.Component<'items.text-item', false>
    type: Schema.Attribute.Enumeration<['default', 'grid']> &
      Schema.Attribute.DefaultTo<'default'>
  }
}

export interface ElementalsIconBox extends Struct.ComponentSchema {
  collectionName: 'components_elementals_icon_boxes'
  info: {
    displayName: 'Icon_Box'
  }
  attributes: {
    adminLabel: Schema.Attribute.String & Schema.Attribute.Private
    icon: Schema.Attribute.Media<'images'>
    link: Schema.Attribute.Component<'shared.link', false>
    sub_title: Schema.Attribute.Text
    title: Schema.Attribute.String
  }
}

export interface ElementalsIconBoxList extends Struct.ComponentSchema {
  collectionName: 'components_elementals_icon_box_lists'
  info: {
    displayName: 'Icon_Box_List'
    icon: 'apps'
  }
  attributes: {
    desktop_item_count: Schema.Attribute.Integer
    gap: Schema.Attribute.Integer &
      Schema.Attribute.SetMinMax<
        {
          min: 0
        },
        number
      > &
      Schema.Attribute.DefaultTo<0>
    icon_position: Schema.Attribute.Enumeration<['top', 'center', 'left']> &
      Schema.Attribute.DefaultTo<'top'>
    item_background: Schema.Attribute.Component<
      'styles.background-styles',
      false
    >
    item_border_radius: Schema.Attribute.Integer &
      Schema.Attribute.SetMinMax<
        {
          min: 0
        },
        number
      > &
      Schema.Attribute.DefaultTo<0>
    items: Schema.Attribute.Component<'items.icon-box', true>
    mobile_item_count: Schema.Attribute.Integer
    tablet_item_count: Schema.Attribute.Integer
    text_align: Schema.Attribute.Enumeration<
      ['left', 'center', 'right', 'justify']
    > &
      Schema.Attribute.DefaultTo<'center'>
  }
}

export interface ElementalsImageBox extends Struct.ComponentSchema {
  collectionName: 'components_elementals_image_boxes'
  info: {
    displayName: 'Image_Box'
  }
  attributes: {
    adminLabel: Schema.Attribute.String & Schema.Attribute.Private
    image: Schema.Attribute.Media<'images'>
    sub_title: Schema.Attribute.Text
    text_align: Schema.Attribute.Enumeration<
      ['left', 'center', 'right', 'justify']
    > &
      Schema.Attribute.DefaultTo<'center'>
    title: Schema.Attribute.Text
  }
}

export interface ElementalsImageSlider extends Struct.ComponentSchema {
  collectionName: 'components_elementals_image_sliders'
  info: {
    displayName: 'Image_Slider'
  }
  attributes: {
    adminLabel: Schema.Attribute.String & Schema.Attribute.Private
    direction: Schema.Attribute.Enumeration<['left', 'right']> &
      Schema.Attribute.DefaultTo<'left'>
    images: Schema.Attribute.Media<'images', true>
    pause_on_hover: Schema.Attribute.Boolean & Schema.Attribute.DefaultTo<false>
    speed: Schema.Attribute.Enumeration<['fast', 'normal', 'slow']> &
      Schema.Attribute.DefaultTo<'normal'>
    type: Schema.Attribute.Enumeration<['default', 'auto-width']> &
      Schema.Attribute.DefaultTo<'default'>
  }
}

export interface ElementalsMediaGallery extends Struct.ComponentSchema {
  collectionName: 'components_elementals_media_galleries'
  info: {
    displayName: 'Media_Gallery'
  }
  attributes: {
    documents: Schema.Attribute.Media<'files', true>
    gallery_videos: Schema.Attribute.Component<
      'elementals.video-gallery',
      false
    >
    images: Schema.Attribute.Media<'images', true>
  }
}

export interface ElementalsMediaImage extends Struct.ComponentSchema {
  collectionName: 'components_elementals_media_images'
  info: {
    displayName: 'Media_Image'
  }
  attributes: {
    adminLabel: Schema.Attribute.String & Schema.Attribute.Private
    alt: Schema.Attribute.String
    border_radius: Schema.Attribute.Integer &
      Schema.Attribute.SetMinMax<
        {
          min: 0
        },
        number
      >
    image: Schema.Attribute.Media<'images'>
  }
}

export interface ElementalsMediaVideo extends Struct.ComponentSchema {
  collectionName: 'components_elementals_media_videos'
  info: {
    displayName: 'Media_Video'
  }
  attributes: {
    adminLabel: Schema.Attribute.String & Schema.Attribute.Private
    autoplay: Schema.Attribute.Boolean & Schema.Attribute.DefaultTo<false>
    border_radius: Schema.Attribute.Integer &
      Schema.Attribute.SetMinMax<
        {
          min: 0
        },
        number
      >
    embed: Schema.Attribute.Text
    image_poster: Schema.Attribute.Media<'images'>
    loop: Schema.Attribute.Boolean & Schema.Attribute.DefaultTo<true>
    open_type: Schema.Attribute.Enumeration<['playin', 'dialog', 'new_tab']>
    video: Schema.Attribute.Media<'videos'>
  }
}

export interface ElementalsPointerEditor extends Struct.ComponentSchema {
  collectionName: 'components_elementals_pointer_editors'
  info: {
    displayName: 'pointerEditor'
  }
  attributes: {
    image_pointer: Schema.Attribute.JSON &
      Schema.Attribute.CustomField<'plugin::image-point-editor.image-point-editor'>
  }
}

export interface ElementalsTimeline extends Struct.ComponentSchema {
  collectionName: 'components_elementals_timelines'
  info: {
    displayName: 'Timeline'
  }
  attributes: {
    item_background: Schema.Attribute.Component<
      'styles.background-styles',
      false
    >
    item_border_radius: Schema.Attribute.Integer
    items: Schema.Attribute.Component<'items.timeline-item', true>
    text_color: Schema.Attribute.String &
      Schema.Attribute.CustomField<'plugin::color-picker.color'>
  }
}

export interface ElementalsVideoGallery extends Struct.ComponentSchema {
  collectionName: 'components_elementals_video_galleries'
  info: {
    displayName: 'Video_Gallery'
  }
  attributes: {
    autoplay: Schema.Attribute.Boolean & Schema.Attribute.DefaultTo<false>
    border_radius: Schema.Attribute.Integer
    loop: Schema.Attribute.Boolean & Schema.Attribute.DefaultTo<false>
    open_type: Schema.Attribute.Enumeration<['playin', 'dialog']> &
      Schema.Attribute.DefaultTo<'playin'>
    videos: Schema.Attribute.Component<'items.video-gallery-item', true>
  }
}

export interface GlobalFooter extends Struct.ComponentSchema {
  collectionName: 'components_global_footers'
  info: {
    description: ''
    displayName: 'Footer'
    icon: 'apps'
  }
  attributes: {
    attached_images: Schema.Attribute.Media<'images', true>
    built_with: Schema.Attribute.String
    copyright: Schema.Attribute.String
    description: Schema.Attribute.Text
    footer_contact: Schema.Attribute.Component<
      'elementals.footer-contact',
      false
    >
    footer_nav: Schema.Attribute.Component<'items.footer-nav-item', true>
    logo: Schema.Attribute.Relation<'oneToOne', 'api::logo.logo'>
    policy_links: Schema.Attribute.Component<'shared.link', true>
    social_media_links: Schema.Attribute.Component<'shared.link', true>
  }
}

export interface GlobalHeader extends Struct.ComponentSchema {
  collectionName: 'components_global_headers'
  info: {
    displayName: 'Header'
  }
  attributes: {
    header_buttons: Schema.Attribute.Component<'shared.button', true>
    logo: Schema.Attribute.Relation<'oneToOne', 'api::logo.logo'>
  }
}

export interface GlobalNavbar extends Struct.ComponentSchema {
  collectionName: 'components_global_navbars'
  info: {
    displayName: 'Navbar'
    icon: 'bold'
  }
  attributes: {
    left_navbar_items: Schema.Attribute.Component<'shared.link', true>
    logo: Schema.Attribute.Relation<'oneToOne', 'api::logo.logo'>
    right_navbar_items: Schema.Attribute.Component<'shared.link', true>
  }
}

export interface ItemsAccordionItem extends Struct.ComponentSchema {
  collectionName: 'components_items_accordion_items'
  info: {
    displayName: 'Accordion_Item'
  }
  attributes: {
    adminLabel: Schema.Attribute.String & Schema.Attribute.Private
    buttons: Schema.Attribute.Component<'shared.button', true>
    description: Schema.Attribute.Blocks
    icon: Schema.Attribute.Media<'images'>
    title: Schema.Attribute.String
  }
}

export interface ItemsContactItem extends Struct.ComponentSchema {
  collectionName: 'components_items_contact_items'
  info: {
    displayName: 'Contact_Item'
  }
  attributes: {
    adminLabel: Schema.Attribute.String & Schema.Attribute.Private
    description: Schema.Attribute.Text
    icon: Schema.Attribute.Media<'images'>
    title: Schema.Attribute.String & Schema.Attribute.Required
    url: Schema.Attribute.String
  }
}

export interface ItemsFooterNavItem extends Struct.ComponentSchema {
  collectionName: 'components_items_footer_nav_items'
  info: {
    displayName: 'Footer_Nav_Item'
  }
  attributes: {
    adminLabel: Schema.Attribute.String & Schema.Attribute.Private
    links: Schema.Attribute.Component<'shared.link', true>
    title: Schema.Attribute.String
  }
}

export interface ItemsGeneratorItem extends Struct.ComponentSchema {
  collectionName: 'components_items_generator_items'
  info: {
    displayName: 'Generator_Item'
  }
  attributes: {
    module: Schema.Attribute.String & Schema.Attribute.Required
    power: Schema.Attribute.String
  }
}

export interface ItemsGraphCardTopItems extends Struct.ComponentSchema {
  collectionName: 'components_items_graph_card_top_items'
  info: {
    displayName: 'Graph_Card_Top_Items'
    icon: 'bulletList'
  }
  attributes: {
    number: Schema.Attribute.String
    text: Schema.Attribute.String
  }
}

export interface ItemsHeroSlideItem extends Struct.ComponentSchema {
  collectionName: 'components_items_hero_slide_items'
  info: {
    displayName: 'Hero_Slide_Item'
  }
  attributes: {
    adminLabel: Schema.Attribute.String & Schema.Attribute.Private
    heading: Schema.Attribute.Component<'elementals.heading', false>
    media: Schema.Attribute.Media<'images' | 'videos'>
    thumbnail_image: Schema.Attribute.Media<'images'>
  }
}

export interface ItemsIconBox extends Struct.ComponentSchema {
  collectionName: 'components_items_icon_boxes'
  info: {
    displayName: 'Icon_Box'
  }
  attributes: {
    icon: Schema.Attribute.Media<'images'>
    link: Schema.Attribute.Component<'shared.link', false>
    sub_title: Schema.Attribute.Text
    title: Schema.Attribute.String & Schema.Attribute.Required
  }
}

export interface ItemsInfoItem extends Struct.ComponentSchema {
  collectionName: 'components_items_info_items'
  info: {
    displayName: 'Info_Item'
  }
  attributes: {
    description: Schema.Attribute.Blocks
    heading: Schema.Attribute.Component<'elementals.heading', false>
  }
}

export interface ItemsInput extends Struct.ComponentSchema {
  collectionName: 'components_items_inputs'
  info: {
    description: ''
    displayName: 'Input'
    icon: 'apps'
  }
  attributes: {
    name: Schema.Attribute.String
    placeholder: Schema.Attribute.String
    type: Schema.Attribute.Enumeration<
      [
        'text',
        'email',
        'password',
        'submit',
        'textarea',
        'button',
        'checkbox',
        'color',
        'date',
        'datetime-local',
        'file',
        'hidden',
        'image',
        'month',
        'number',
        'radio',
        'range',
        'reset',
        'search',
        'tel',
        'time',
        'url',
        'week',
      ]
    > &
      Schema.Attribute.DefaultTo<'text'>
  }
}

export interface ItemsJobInfo extends Struct.ComponentSchema {
  collectionName: 'components_items_job_infos'
  info: {
    displayName: 'Job_Info'
  }
  attributes: {
    content: Schema.Attribute.Blocks
    title: Schema.Attribute.Text
  }
}

export interface ItemsLeftNavbarItems extends Struct.ComponentSchema {
  collectionName: 'components_items_left_navbar_items'
  info: {
    displayName: 'Left_Navbar_Items'
    icon: 'bulletList'
  }
  attributes: {
    name: Schema.Attribute.String
    URL: Schema.Attribute.String
  }
}

export interface ItemsRayItems extends Struct.ComponentSchema {
  collectionName: 'components_items_ray_items'
  info: {
    description: ''
    displayName: 'Ray_Card_Items'
    icon: 'bulletList'
  }
  attributes: {
    item_1: Schema.Attribute.String
    item_2: Schema.Attribute.String
    item_3: Schema.Attribute.String
  }
}

export interface ItemsStat extends Struct.ComponentSchema {
  collectionName: 'components_items_stats'
  info: {
    displayName: 'Stat'
  }
  attributes: {
    adminLabel: Schema.Attribute.String & Schema.Attribute.Private
    description: Schema.Attribute.Text
    number: Schema.Attribute.Integer
    number_suffix: Schema.Attribute.String
  }
}

export interface ItemsTextItem extends Struct.ComponentSchema {
  collectionName: 'components_items_text_items'
  info: {
    displayName: 'Text_Item'
    icon: 'bold'
  }
  attributes: {
    adminLabel: Schema.Attribute.String & Schema.Attribute.Private
    styles: Schema.Attribute.Component<'styles.text-styles', false>
    text: Schema.Attribute.Text
  }
}

export interface ItemsTimelineItem extends Struct.ComponentSchema {
  collectionName: 'components_items_timeline_items'
  info: {
    displayName: 'Timeline_Item'
  }
  attributes: {
    adminLabel: Schema.Attribute.String & Schema.Attribute.Private
    description: Schema.Attribute.Text
    image: Schema.Attribute.Media<'images'>
    sub_title: Schema.Attribute.Text
    title: Schema.Attribute.String & Schema.Attribute.Required
  }
}

export interface ItemsVideoGalleryItem extends Struct.ComponentSchema {
  collectionName: 'components_items_video_gallery_items'
  info: {
    displayName: 'Video_Gallery_Item'
  }
  attributes: {
    adminLabel: Schema.Attribute.String & Schema.Attribute.Private
    embed: Schema.Attribute.Text
    image_poster: Schema.Attribute.Media<'images'>
    video: Schema.Attribute.Media<'videos'>
  }
}

export interface ProductPartsPartHeroSection extends Struct.ComponentSchema {
  collectionName: 'components_product_parts_part_hero_sections'
  info: {
    displayName: 'Part_Hero_Section'
  }
  attributes: {
    heading: Schema.Attribute.Component<'elementals.heading', false>
    styles: Schema.Attribute.Component<'styles.section-styles', false>
  }
}

export interface ProductPartsPartSpecifications extends Struct.ComponentSchema {
  collectionName: 'components_product_parts_part_specifications'
  info: {
    displayName: 'Part_Specifications'
  }
  attributes: {
    heading: Schema.Attribute.Component<'elementals.heading', false>
    styles: Schema.Attribute.Component<'styles.section-styles', false>
  }
}

export interface ProductPartsPartsByCategory extends Struct.ComponentSchema {
  collectionName: 'components_product_parts_parts_by_categories'
  info: {
    displayName: 'Parts_By_Category'
    icon: 'apps'
  }
  attributes: {
    heading: Schema.Attribute.Component<'elementals.heading', false>
    styles: Schema.Attribute.Component<'styles.section-styles', false>
  }
}

export interface ProductPartsProductPartCategories
  extends Struct.ComponentSchema {
  collectionName: 'components_product_parts_product_part_categories'
  info: {
    displayName: 'Product_Part_Categories'
  }
  attributes: {
    heading: Schema.Attribute.Component<'elementals.heading', false>
    styles: Schema.Attribute.Component<'styles.section-styles', false>
  }
}

export interface ProductPartsRelatedParts extends Struct.ComponentSchema {
  collectionName: 'components_product_parts_related_parts'
  info: {
    displayName: 'Related_Parts'
  }
  attributes: {
    heading: Schema.Attribute.Component<'elementals.heading', false>
    styles: Schema.Attribute.Component<'styles.section-styles', false>
  }
}

export interface ProductsGeneratorSelectionGuide
  extends Struct.ComponentSchema {
  collectionName: 'components_products_generator_selection_guides'
  info: {
    displayName: 'Generator_Selection_Guide'
  }
  attributes: {
    adminLabel: Schema.Attribute.String & Schema.Attribute.Private
    generator_types: Schema.Attribute.Component<'elementals.generator', true>
    heading: Schema.Attribute.Component<'elementals.heading', false>
    styles: Schema.Attribute.Component<'styles.section-styles', false>
  }
}

export interface ProductsProductCategories extends Struct.ComponentSchema {
  collectionName: 'components_products_product_categories'
  info: {
    displayName: 'Product-Categories'
  }
  attributes: {
    heading: Schema.Attribute.Component<'elementals.heading', false>
    pagination: Schema.Attribute.Component<'shared.pagination', false>
  }
}

export interface ProductsProductComparison extends Struct.ComponentSchema {
  collectionName: 'components_products_product_comparisons'
  info: {
    displayName: 'Product_Comparison'
  }
  attributes: {
    heading: Schema.Attribute.Component<'elementals.heading', false>
    styles: Schema.Attribute.Component<'styles.section-styles', false>
  }
}

export interface ProductsProductDetailsSection extends Struct.ComponentSchema {
  collectionName: 'components_products_product_details_sections'
  info: {
    displayName: 'Product_Details_Section'
  }
  attributes: {
    heading: Schema.Attribute.Component<'elementals.heading', false>
    styles: Schema.Attribute.Component<'styles.section-styles', false>
  }
}

export interface ProductsProductMediaGallery extends Struct.ComponentSchema {
  collectionName: 'components_products_product_media_galleries'
  info: {
    displayName: 'Product_Media_Gallery'
  }
  attributes: {
    adminLabel: Schema.Attribute.String & Schema.Attribute.Private
    heading: Schema.Attribute.Component<'elementals.heading', false>
    styles: Schema.Attribute.Component<'styles.section-styles', false>
  }
}

export interface ProductsProductRelatedParts extends Struct.ComponentSchema {
  collectionName: 'components_products_product_related_parts'
  info: {
    displayName: 'Product_Related_Parts'
    icon: 'book'
  }
  attributes: {
    heading: Schema.Attribute.Component<'elementals.heading', false>
    styles: Schema.Attribute.Component<'styles.section-styles', false>
  }
}

export interface ProductsProductRelatedServices extends Struct.ComponentSchema {
  collectionName: 'components_products_product_related_services'
  info: {
    displayName: 'Product_Related_Services'
  }
  attributes: {
    adminLabel: Schema.Attribute.String & Schema.Attribute.Private
    heading: Schema.Attribute.Component<'elementals.heading', false>
    styles: Schema.Attribute.Component<'styles.section-styles', false>
  }
}

export interface ProductsProductSubcategoriesSection
  extends Struct.ComponentSchema {
  collectionName: 'components_products_product_subcategories_sections'
  info: {
    displayName: 'Product_Subcategories_Section'
    icon: 'apps'
  }
  attributes: {
    adminLabel: Schema.Attribute.String & Schema.Attribute.Private
    heading: Schema.Attribute.Component<'elementals.heading', false>
    styles: Schema.Attribute.Component<'styles.section-styles', false>
  }
}

export interface ProductsProductsByCategory extends Struct.ComponentSchema {
  collectionName: 'components_products_products_by_categories'
  info: {
    displayName: 'Products_By_Category'
    icon: 'filter'
  }
  attributes: {
    adminLabel: Schema.Attribute.String & Schema.Attribute.Private
    heading: Schema.Attribute.Component<'elementals.heading', false>
    styles: Schema.Attribute.Component<'styles.section-styles', false>
  }
}

export interface ProductsProductsRecentView extends Struct.ComponentSchema {
  collectionName: 'components_products_products_recent_views'
  info: {
    displayName: 'Products-Recent-View'
  }
  attributes: {
    heading: Schema.Attribute.Component<'elementals.heading', false>
    pagination: Schema.Attribute.Component<'shared.pagination', false>
  }
}

export interface ProductsRecentlyViewedProducts extends Struct.ComponentSchema {
  collectionName: 'components_products_recently_viewed_products'
  info: {
    displayName: 'Recently_Viewed_Products'
  }
  attributes: {
    adminLabel: Schema.Attribute.String & Schema.Attribute.Private
    heading: Schema.Attribute.Component<'elementals.heading', false>
    styles: Schema.Attribute.Component<'styles.section-styles', false>
  }
}

export interface ProductsSpecificationItem extends Struct.ComponentSchema {
  collectionName: 'components_products_specification_items'
  info: {
    displayName: 'Specification Item'
    icon: 'play'
  }
  attributes: {
    adminLabel: Schema.Attribute.String & Schema.Attribute.Private
    product_specification: Schema.Attribute.Relation<
      'oneToOne',
      'api::product-specification.product-specification'
    >
    rank: Schema.Attribute.Integer
    unit: Schema.Attribute.String
    value: Schema.Attribute.String
    value_in_us: Schema.Attribute.String
  }
}

export interface SharedButton extends Struct.ComponentSchema {
  collectionName: 'components_shared_buttons'
  info: {
    description: ''
    displayName: 'Button'
    icon: 'cursor'
  }
  attributes: {
    target: Schema.Attribute.Enumeration<['_blank', '_self', '_parent', '_top']>
    text: Schema.Attribute.String
    URL: Schema.Attribute.String
    variant: Schema.Attribute.Enumeration<
      ['simple', 'primary', 'secondary', 'outline', 'muted']
    > &
      Schema.Attribute.DefaultTo<'primary'>
  }
}

export interface SharedForm extends Struct.ComponentSchema {
  collectionName: 'components_shared_forms'
  info: {
    description: ''
    displayName: 'Form'
    icon: 'paperPlane'
  }
  attributes: {
    inputs: Schema.Attribute.Component<'items.input', true>
  }
}

export interface SharedLaunches extends Struct.ComponentSchema {
  collectionName: 'components_shared_launches'
  info: {
    description: ''
    displayName: 'Launches'
    icon: 'rocket'
  }
  attributes: {
    description: Schema.Attribute.String
    mission_number: Schema.Attribute.String
    title: Schema.Attribute.String
  }
}

export interface SharedLink extends Struct.ComponentSchema {
  collectionName: 'components_shared_links'
  info: {
    displayName: 'Link'
    icon: 'link'
  }
  attributes: {
    icon: Schema.Attribute.Media<'images'>
    target: Schema.Attribute.Enumeration<['_blank', '_self', '_parent', '_top']>
    text: Schema.Attribute.String
    url: Schema.Attribute.String
  }
}

export interface SharedPagination extends Struct.ComponentSchema {
  collectionName: 'components_shared_paginations'
  info: {
    displayName: 'Pagination'
  }
  attributes: {
    limit: Schema.Attribute.Integer & Schema.Attribute.DefaultTo<12>
    start: Schema.Attribute.Integer & Schema.Attribute.DefaultTo<0>
    withCount: Schema.Attribute.Boolean & Schema.Attribute.DefaultTo<false>
  }
}

export interface SharedSection extends Struct.ComponentSchema {
  collectionName: 'components_shared_sections'
  info: {
    displayName: 'Section'
    icon: 'cursor'
  }
  attributes: {
    heading: Schema.Attribute.String
    sub_heading: Schema.Attribute.String
    users: Schema.Attribute.Component<'shared.user', true>
  }
}

export interface SharedSeo extends Struct.ComponentSchema {
  collectionName: 'components_shared_seos'
  info: {
    displayName: 'Seo'
    icon: 'search'
  }
  attributes: {
    canonicalURL: Schema.Attribute.String
    keywords: Schema.Attribute.Text
    metaDescription: Schema.Attribute.Text &
      Schema.Attribute.SetMinMaxLength<{
        minLength: 50
      }>
    metaImage: Schema.Attribute.Media<'images' | 'files' | 'videos'>
    metaRobots: Schema.Attribute.String
    metaTitle: Schema.Attribute.String &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMaxLength<{
        maxLength: 60
      }>
    metaViewport: Schema.Attribute.String
    structuredData: Schema.Attribute.JSON
  }
}

export interface SharedSocialLink extends Struct.ComponentSchema {
  collectionName: 'components_shared_social_links'
  info: {
    displayName: 'Social_Link'
  }
  attributes: {
    adminLabel: Schema.Attribute.String & Schema.Attribute.Private
    custom_icon: Schema.Attribute.Media<
      'images' | 'files' | 'videos' | 'audios'
    >
    name: Schema.Attribute.Enumeration<
      [
        'facebook',
        'instagram',
        'linkedin',
        'youtube',
        'tiktok',
        'email',
        'phone',
        'custom',
      ]
    >
    target: Schema.Attribute.Enumeration<['_blank', '_self', '_parent']> &
      Schema.Attribute.DefaultTo<'_blank'>
    url: Schema.Attribute.String
  }
}

export interface SharedSocialMediaIconLinks extends Struct.ComponentSchema {
  collectionName: 'components_shared_social_media_icon_links'
  info: {
    description: ''
    displayName: 'Social_Media_Icon_Links'
    icon: 'expand'
  }
  attributes: {
    image: Schema.Attribute.Media<'images' | 'files' | 'videos' | 'audios'>
    link: Schema.Attribute.Component<'shared.link', true>
  }
}

export interface SharedSteps extends Struct.ComponentSchema {
  collectionName: 'components_shared_steps'
  info: {
    description: ''
    displayName: 'Steps'
    icon: 'bulletList'
  }
  attributes: {
    description: Schema.Attribute.String
    title: Schema.Attribute.String
  }
}

export interface SharedUser extends Struct.ComponentSchema {
  collectionName: 'components_shared_users'
  info: {
    description: ''
    displayName: 'User'
    icon: 'user'
  }
  attributes: {
    image: Schema.Attribute.Media<'images'>
    job: Schema.Attribute.String
    name: Schema.Attribute.String
  }
}

export interface StylesBackgroundStyles extends Struct.ComponentSchema {
  collectionName: 'components_styles_background_styles'
  info: {
    displayName: 'Background_Styles'
  }
  attributes: {
    adminLabel: Schema.Attribute.String & Schema.Attribute.Private
    color: Schema.Attribute.String &
      Schema.Attribute.CustomField<'plugin::color-picker.color'>
    image: Schema.Attribute.Media<'images'>
    opacity: Schema.Attribute.Decimal &
      Schema.Attribute.SetMinMax<
        {
          max: 1
          min: 0
        },
        number
      > &
      Schema.Attribute.DefaultTo<1>
    repeat: Schema.Attribute.Enumeration<
      ['no-repeat', 'repeat', 'repeat-x', 'repeat-y']
    > &
      Schema.Attribute.DefaultTo<'no-repeat'>
    size: Schema.Attribute.Enumeration<['cover', 'contain', 'auto']> &
      Schema.Attribute.DefaultTo<'cover'>
  }
}

export interface StylesSectionStyles extends Struct.ComponentSchema {
  collectionName: 'components_styles_section_styles'
  info: {
    displayName: 'Section_Styles'
  }
  attributes: {
    adminLabel: Schema.Attribute.String & Schema.Attribute.Private
    background: Schema.Attribute.Component<'styles.background-styles', false>
    content_max_width: Schema.Attribute.Enumeration<
      ['full_width', 'xl', 'lg', 'md', 'sm', 'xs']
    > &
      Schema.Attribute.DefaultTo<'xl'>
    margin: Schema.Attribute.Component<'styles.spacing', false>
    padding: Schema.Attribute.Component<'styles.spacing', false>
  }
}

export interface StylesSpacing extends Struct.ComponentSchema {
  collectionName: 'components_styles_spacings'
  info: {
    displayName: 'spacing'
  }
  attributes: {
    bottom: Schema.Attribute.Integer
    left: Schema.Attribute.Integer
    right: Schema.Attribute.Integer
    top: Schema.Attribute.Integer
    unit: Schema.Attribute.Enumeration<['px', 'percent_%', 'rem', 'em']> &
      Schema.Attribute.DefaultTo<'px'>
  }
}

export interface StylesTextStyles extends Struct.ComponentSchema {
  collectionName: 'components_styles_text_styles'
  info: {
    displayName: 'Text_Styles'
  }
  attributes: {
    adminLabel: Schema.Attribute.String & Schema.Attribute.Private
    color: Schema.Attribute.String &
      Schema.Attribute.CustomField<'plugin::color-picker.color'>
    font_weight: Schema.Attribute.Enumeration<
      [
        'font-black',
        'font-extrabold',
        'font-bold',
        'font-semibold',
        'font-medium',
        'font-normal',
        'font-light',
        'font-extralight',
        'font-thin',
      ]
    > &
      Schema.Attribute.DefaultTo<'font-normal'>
    line_height: Schema.Attribute.Decimal &
      Schema.Attribute.SetMinMax<
        {
          min: 1
        },
        number
      > &
      Schema.Attribute.DefaultTo<1>
    text_align: Schema.Attribute.Enumeration<
      ['text-left', 'text-center', 'text-right', 'text-justify']
    >
    variant: Schema.Attribute.Enumeration<
      ['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'body1', 'body2', 'caption']
    >
  }
}

declare module '@strapi/strapi' {
  export namespace Public {
    export interface ComponentSchemas {
      'cards.globe-card': CardsGlobeCard
      'cards.graph-card': CardsGraphCard
      'cards.hover-expand-card': CardsHoverExpandCard
      'cards.hover-overlay-card': CardsHoverOverlayCard
      'cards.popup-card': CardsPopupCard
      'cards.ray-card': CardsRayCard
      'cards.social-media-card': CardsSocialMediaCard
      'dynamic-zone.accordion-section': DynamicZoneAccordionSection
      'dynamic-zone.contact-us-section': DynamicZoneContactUsSection
      'dynamic-zone.distributed-brands': DynamicZoneDistributedBrands
      'dynamic-zone.executive-team-section': DynamicZoneExecutiveTeamSection
      'dynamic-zone.featured-promotions': DynamicZoneFeaturedPromotions
      'dynamic-zone.features': DynamicZoneFeatures
      'dynamic-zone.features-section': DynamicZoneFeaturesSection
      'dynamic-zone.form-subscribe': DynamicZoneFormSubscribe
      'dynamic-zone.hero-slide-section': DynamicZoneHeroSlideSection
      'dynamic-zone.hover-expand-card-collection': DynamicZoneHoverExpandCardCollection
      'dynamic-zone.hover-overlay-card-collection': DynamicZoneHoverOverlayCardCollection
      'dynamic-zone.html': DynamicZoneHtml
      'dynamic-zone.icon-box-list-section': DynamicZoneIconBoxListSection
      'dynamic-zone.image-box-section': DynamicZoneImageBoxSection
      'dynamic-zone.image-slider-section': DynamicZoneImageSliderSection
      'dynamic-zone.info-block': DynamicZoneInfoBlock
      'dynamic-zone.job-listings-section': DynamicZoneJobListingsSection
      'dynamic-zone.latest-news-section': DynamicZoneLatestNewsSection
      'dynamic-zone.latest-promotions-section': DynamicZoneLatestPromotionsSection
      'dynamic-zone.locations-section': DynamicZoneLocationsSection
      'dynamic-zone.media-text-section': DynamicZoneMediaTextSection
      'dynamic-zone.news-featured-section': DynamicZoneNewsFeaturedSection
      'dynamic-zone.news-listing-section': DynamicZoneNewsListingSection
      'dynamic-zone.news-related': DynamicZoneNewsRelated
      'dynamic-zone.other-services-section': DynamicZoneOtherServicesSection
      'dynamic-zone.page-hero-section': DynamicZonePageHeroSection
      'dynamic-zone.partners-commitment-section': DynamicZonePartnersCommitmentSection
      'dynamic-zone.product-categories-section': DynamicZoneProductCategoriesSection
      'dynamic-zone.promotions-featured-section': DynamicZonePromotionsFeaturedSection
      'dynamic-zone.promotions-listing-section': DynamicZonePromotionsListingSection
      'dynamic-zone.related-articles': DynamicZoneRelatedArticles
      'dynamic-zone.related-industries': DynamicZoneRelatedIndustries
      'dynamic-zone.related-product-categories': DynamicZoneRelatedProductCategories
      'dynamic-zone.related-products': DynamicZoneRelatedProducts
      'dynamic-zone.related-solutions-section': DynamicZoneRelatedSolutionsSection
      'dynamic-zone.service-list-section': DynamicZoneServiceListSection
      'dynamic-zone.solution-applications-section': DynamicZoneSolutionApplicationsSection
      'dynamic-zone.solution-categories-section': DynamicZoneSolutionCategoriesSection
      'dynamic-zone.solution-media-gallery': DynamicZoneSolutionMediaGallery
      'dynamic-zone.solution-portfolio-section': DynamicZoneSolutionPortfolioSection
      'dynamic-zone.solutions-by-application': DynamicZoneSolutionsByApplication
      'dynamic-zone.stats-section': DynamicZoneStatsSection
      'dynamic-zone.step-guide-section': DynamicZoneStepGuideSection
      'dynamic-zone.support-center-section': DynamicZoneSupportCenterSection
      'dynamic-zone.testimonials-section': DynamicZoneTestimonialsSection
      'dynamic-zone.timeline-section': DynamicZoneTimelineSection
      'dynamic-zone.video-section': DynamicZoneVideoSection
      'dynamic-zone.widget': DynamicZoneWidget
      'elementals.accordion': ElementalsAccordion
      'elementals.banner-ads': ElementalsBannerAds
      'elementals.compare-images': ElementalsCompareImages
      'elementals.editor': ElementalsEditor
      'elementals.footer-contact': ElementalsFooterContact
      'elementals.generator': ElementalsGenerator
      'elementals.heading': ElementalsHeading
      'elementals.icon-box': ElementalsIconBox
      'elementals.icon-box-list': ElementalsIconBoxList
      'elementals.image-box': ElementalsImageBox
      'elementals.image-slider': ElementalsImageSlider
      'elementals.media-gallery': ElementalsMediaGallery
      'elementals.media-image': ElementalsMediaImage
      'elementals.media-video': ElementalsMediaVideo
      'elementals.pointer-editor': ElementalsPointerEditor
      'elementals.timeline': ElementalsTimeline
      'elementals.video-gallery': ElementalsVideoGallery
      'global.footer': GlobalFooter
      'global.header': GlobalHeader
      'global.navbar': GlobalNavbar
      'items.accordion-item': ItemsAccordionItem
      'items.contact-item': ItemsContactItem
      'items.footer-nav-item': ItemsFooterNavItem
      'items.generator-item': ItemsGeneratorItem
      'items.graph-card-top-items': ItemsGraphCardTopItems
      'items.hero-slide-item': ItemsHeroSlideItem
      'items.icon-box': ItemsIconBox
      'items.info-item': ItemsInfoItem
      'items.input': ItemsInput
      'items.job-info': ItemsJobInfo
      'items.left-navbar-items': ItemsLeftNavbarItems
      'items.ray-items': ItemsRayItems
      'items.stat': ItemsStat
      'items.text-item': ItemsTextItem
      'items.timeline-item': ItemsTimelineItem
      'items.video-gallery-item': ItemsVideoGalleryItem
      'product-parts.part-hero-section': ProductPartsPartHeroSection
      'product-parts.part-specifications': ProductPartsPartSpecifications
      'product-parts.parts-by-category': ProductPartsPartsByCategory
      'product-parts.product-part-categories': ProductPartsProductPartCategories
      'product-parts.related-parts': ProductPartsRelatedParts
      'products.generator-selection-guide': ProductsGeneratorSelectionGuide
      'products.product-categories': ProductsProductCategories
      'products.product-comparison': ProductsProductComparison
      'products.product-details-section': ProductsProductDetailsSection
      'products.product-media-gallery': ProductsProductMediaGallery
      'products.product-related-parts': ProductsProductRelatedParts
      'products.product-related-services': ProductsProductRelatedServices
      'products.product-subcategories-section': ProductsProductSubcategoriesSection
      'products.products-by-category': ProductsProductsByCategory
      'products.products-recent-view': ProductsProductsRecentView
      'products.recently-viewed-products': ProductsRecentlyViewedProducts
      'products.specification-item': ProductsSpecificationItem
      'shared.button': SharedButton
      'shared.form': SharedForm
      'shared.launches': SharedLaunches
      'shared.link': SharedLink
      'shared.pagination': SharedPagination
      'shared.section': SharedSection
      'shared.seo': SharedSeo
      'shared.social-link': SharedSocialLink
      'shared.social-media-icon-links': SharedSocialMediaIconLinks
      'shared.steps': SharedSteps
      'shared.user': SharedUser
      'styles.background-styles': StylesBackgroundStyles
      'styles.section-styles': StylesSectionStyles
      'styles.spacing': StylesSpacing
      'styles.text-styles': StylesTextStyles
    }
  }
}
