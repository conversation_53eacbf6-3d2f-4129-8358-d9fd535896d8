/**
 * `deepPopulate` middleware
 */

import type { Core } from '@strapi/strapi'
import { UID } from '@strapi/types'
import { contentTypes } from '@strapi/utils'
import pluralize from 'pluralize'

interface Options {
  /**
   * Fields to select when populating relations
   */
  relationalFields?: string[]
}

const { CREATED_BY_ATTRIBUTE, UPDATED_BY_ATTRIBUTE } = contentTypes.constants

const extractPathSegment = (url: string) =>
  url.match(/\/([^/?]+)(?:\?|$)/)?.[1] || ''

const getDeepPopulate = (uid: UID.Schema, opts: Options = {}) => {
  try {
    const model = strapi.getModel(uid)
    if (!model) {
      return {}
    }
    const attributes = Object.entries(model.attributes)

    return attributes.reduce((acc: any, [attributeName, attribute]) => {
      // console.log('\n\n🚀🚀🚀🚀🚀🚀:', { attributeName, attribute })

      switch (attribute.type) {
        case 'relation': {
          const isMorphRelation = attribute.relation
            .toLowerCase()
            .startsWith('morph')
          if (isMorphRelation) {
            break
          }

          // Ignore not visible fields other than createdBy and updatedBy
          const isVisible = contentTypes.isVisibleAttribute(
            model,
            attributeName,
          )
          const _isCreatorField = [
            CREATED_BY_ATTRIBUTE,
            UPDATED_BY_ATTRIBUTE,
          ].includes(attributeName)

          if (isVisible) {
            if (attributeName === 'testimonials') {
              acc[attributeName] = { populate: 'user.image' }
            } else {
              acc[attributeName] = { populate: '*' }
            }
          }

          break
        }

        case 'media': {
          acc[attributeName] = { populate: '*' }
          break
        }

        case 'component': {
          const populate = getDeepPopulate(attribute.component, opts)
          acc[attributeName] = { populate }
          break
        }

        case 'dynamiczone': {
          // Use fragments to populate the dynamic zone components
          const populatedComponents = (attribute.components || []).reduce(
            (acc: any, componentUID: UID.Component) => {
              acc[componentUID] = {
                populate: getDeepPopulate(componentUID, opts),
              }

              return acc
            },
            {},
          )

          acc[attributeName] = { on: populatedComponents }
          break
        }
        default:
          break
      }

      return acc
    }, {})
  } catch (error) {
    strapi.log.error('Error in deepPopulate middleware:', error)
    throw error
  }
}

const apiExcludes = [
  '/api/users',
  '/api/seo',
  '/api/navigation',
  '/api/form-subscribes',
  '/api/form-submissions',
  '/api/form-job-applications',
  '/api/article-comments',
  // plugins
  '/api/api::map-pointer.map-pointer',
]

export default (_config, { strapi }: { strapi: Core.Strapi }) => {
  return async (ctx, next) => {
    try {
      if (
        ctx.request.url.startsWith('/api/') &&
        ctx.request.method === 'GET' &&
        !ctx.query.populate &&
        !apiExcludes.some((exclude) => ctx.request.url.includes(exclude))
      ) {
        strapi.log.info('Using custom Dynamic-Zone population Middleware...')

        const contentType = extractPathSegment(ctx.request.url)
        const singular = pluralize.singular(contentType)
        const uid = `api::${singular}.${singular}`

        const deepPopulate = getDeepPopulate(uid as UID.Schema) || {}
        const localizations = {
          ...(!ctx.request.url.includes('products') && {
            localizations: { populate: {} },
          }),
        }

        ctx.query.populate = {
          // @ts-ignores
          ...deepPopulate,
          ...localizations,
        }
      }
      await next()
    } catch (error) {
      strapi.log.error('\n\nError in deepPopulate middleware:', error, '\n\n')
      throw error
    }
  }
}
