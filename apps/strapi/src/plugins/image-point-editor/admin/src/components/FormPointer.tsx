import { Button, Dialog, Field, Flex, TextInput } from '@strapi/design-system'
import type { Schema } from '@strapi/types'
import { useEffect, useState } from 'react'
import MediaLib from './MediaLib'

type TProps = {
  children: React.ReactNode
  values: Record<string, any>
  onSave: (data: any) => void
  className?: string
  onDelete: VoidFunction
}

const FormPointer = ({ children, values, onSave, onDelete }: TProps) => {
  const [mediaLibVisible, setMediaLibVisible] = useState(false)
  const [form, setForm] = useState(values)

  useEffect(() => {
    setForm(values)
  }, [values])

  const handleToggleMediaLib = () => setMediaLibVisible((prev) => !prev)
  const handleChangeAssets = (assets: Schema.Attribute.MediaValue<true>) => {
    const image = assets[0]
    setForm({ ...form, image })
    handleToggleMediaLib()
  }

  return (
    <Dialog.Root>
      <Dialog.Trigger>{children}</Dialog.Trigger>
      <Dialog.Content>
        <Dialog.Header>{form.title || 'Point'}</Dialog.Header>
        <Dialog.Body>
          <Field.Root name="id" width="100%">
            <Field.Label>TargetID</Field.Label>
            <TextInput
              name="id"
              defaultValue={form.id}
              required
              onChange={(e: any) => {
                setForm({ ...form, id: e.target.value })
              }}
            />
          </Field.Root>
          <Field.Root name="title" width="100%">
            <Field.Label>Title</Field.Label>
            <TextInput
              name="title"
              defaultValue={form.title}
              required
              onChange={(e: any) => {
                setForm({ ...form, title: e.target.value })
              }}
            />
          </Field.Root>
          <Field.Root name="content" width="100%">
            <Field.Label>Content</Field.Label>
            <TextInput
              name="content"
              defaultValue={form.content}
              required
              onChange={(e: any) => {
                setForm({ ...form, content: e.target.value })
              }}
            />
          </Field.Root>
          <Field.Root name="image" width="100%">
            <Flex
              direction="row"
              gap={2}
              alignItems="center"
              justifyContent="space-between"
            >
              <Field.Label>Image</Field.Label>
              <Button
                onClick={handleToggleMediaLib}
                style={{ width: 'fit-content' }}
              >
                Add Image
              </Button>
            </Flex>

            {form.image && (
              <img
                src={form.image.url}
                alt={form.image.alt}
                style={{
                  maxWidth: 200,
                  margin: '0 auto',
                  width: '100%',
                  height: '100%',
                  objectFit: 'cover',
                }}
              />
            )}

            <MediaLib
              allowedTypes={['images']}
              isOpen={mediaLibVisible}
              onChange={handleChangeAssets}
              onToggle={handleToggleMediaLib}
            />
          </Field.Root>

          <Field.Root name="buttonText" width="100%">
            <Field.Label>Button Text</Field.Label>
            <TextInput
              name="buttonText"
              defaultValue={form.buttonText}
              required
              onChange={(e: any) => {
                setForm({ ...form, buttonText: e.target.value })
              }}
            />
          </Field.Root>
          <Field.Root name="buttonLink" width="100%">
            <Field.Label>Button Link</Field.Label>
            <TextInput
              name="buttonLink"
              defaultValue={form.buttonLink}
              required
              onChange={(e: any) => {
                setForm({ ...form, buttonLink: e.target.value })
              }}
            />
          </Field.Root>
        </Dialog.Body>
        <Dialog.Footer>
          <Dialog.Cancel>
            <Button fullWidth variant="danger-light" onClick={onDelete}>
              Delete
            </Button>
          </Dialog.Cancel>
          <Dialog.Action>
            <Button
              fullWidth
              variant="success-light"
              onClick={() => onSave(form)}
            >
              Save
            </Button>
          </Dialog.Action>
        </Dialog.Footer>
      </Dialog.Content>
    </Dialog.Root>
  )
}

export default FormPointer
