{"kind": "collectionType", "collectionName": "services", "info": {"singularName": "service", "pluralName": "services", "displayName": "Service List"}, "options": {"draftAndPublish": true}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"adminLabel": {"type": "string", "private": true, "pluginOptions": {"i18n": {"localized": true}}}, "name": {"type": "string", "pluginOptions": {"i18n": {"localized": true}}, "required": true}, "slug": {"type": "uid", "targetField": "name"}, "description": {"type": "text", "pluginOptions": {"i18n": {"localized": true}}}, "dynamic_zone": {"type": "dynamiczone", "pluginOptions": {"i18n": {"localized": true}}, "components": ["dynamic-zone.video-section", "dynamic-zone.solution-portfolio-section", "dynamic-zone.media-text-section", "dynamic-zone.page-hero-section", "dynamic-zone.related-products", "dynamic-zone.image-slider-section", "dynamic-zone.image-box-section", "elementals.media-image", "dynamic-zone.other-services-section", "dynamic-zone.related-solutions-section", "dynamic-zone.info-block", "dynamic-zone.news-related"]}, "seo": {"type": "component", "pluginOptions": {"i18n": {"localized": true}}, "component": "shared.seo", "repeatable": false}, "banner": {"type": "media", "pluginOptions": {"i18n": {"localized": true}}, "multiple": false, "allowedTypes": ["images"]}, "related_products": {"type": "relation", "relation": "manyToMany", "target": "api::product.product", "mappedBy": "related_services"}}}