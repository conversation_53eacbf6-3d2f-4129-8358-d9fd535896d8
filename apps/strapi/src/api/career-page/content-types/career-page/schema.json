{"kind": "singleType", "collectionName": "career_pages", "info": {"singularName": "career-page", "pluralName": "career-pages", "displayName": "/careers"}, "options": {"draftAndPublish": true}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"title": {"type": "string", "pluginOptions": {"i18n": {"localized": true}}, "required": true}, "dynamic_zone": {"type": "dynamiczone", "pluginOptions": {"i18n": {"localized": true}}, "components": ["dynamic-zone.video-section", "dynamic-zone.image-slider-section", "dynamic-zone.image-box-section", "dynamic-zone.contact-us-section", "dynamic-zone.form-subscribe", "dynamic-zone.page-hero-section", "dynamic-zone.job-listings-section", "dynamic-zone.info-block", "dynamic-zone.widget", "dynamic-zone.features-section", "dynamic-zone.related-articles"]}, "seo": {"type": "component", "pluginOptions": {"i18n": {"localized": true}}, "component": "shared.seo", "repeatable": false}, "slug": {"type": "uid", "pluginOptions": {"i18n": {"localized": true}}, "targetField": "title"}}}