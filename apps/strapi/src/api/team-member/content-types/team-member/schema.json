{"kind": "collectionType", "collectionName": "team_members", "info": {"singularName": "team-member", "pluralName": "team-members", "displayName": "Executive Team"}, "options": {"draftAndPublish": true}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"adminLabel": {"type": "string", "private": true, "pluginOptions": {"i18n": {"localized": true}}}, "image": {"type": "media", "pluginOptions": {"i18n": {"localized": true}}, "multiple": false, "allowedTypes": ["images"]}, "name_prefix": {"type": "string", "pluginOptions": {"i18n": {"localized": true}}}, "name": {"type": "string", "pluginOptions": {"i18n": {"localized": true}}}, "position": {"type": "text", "pluginOptions": {"i18n": {"localized": true}}}, "rank": {"type": "integer", "pluginOptions": {"i18n": {"localized": true}}}}}