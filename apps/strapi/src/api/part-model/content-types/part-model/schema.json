{"kind": "collectionType", "collectionName": "part_models", "info": {"singularName": "part-model", "pluralName": "part-models", "displayName": "Product-Part Model"}, "options": {"draftAndPublish": true}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"adminLabel": {"type": "string", "private": true, "pluginOptions": {"i18n": {"localized": true}}}, "name": {"type": "string", "pluginOptions": {"i18n": {"localized": true}}}, "slug": {"type": "uid", "targetField": "name"}, "image": {"type": "media", "pluginOptions": {"i18n": {"localized": true}}, "multiple": false, "allowedTypes": ["images"]}, "description": {"type": "text", "pluginOptions": {"i18n": {"localized": true}}}, "ad_image": {"type": "media", "pluginOptions": {"i18n": {"localized": true}}, "multiple": false, "allowedTypes": ["images"]}, "image_gallery": {"type": "media", "pluginOptions": {"i18n": {"localized": true}}, "multiple": true, "allowedTypes": ["images"]}, "brand": {"type": "relation", "relation": "manyToOne", "target": "api::brand.brand", "inversedBy": "part_models"}, "part_specification_values": {"type": "relation", "relation": "oneToMany", "target": "api::product-specification-value.product-specification-value", "mappedBy": "part_model"}, "part": {"type": "relation", "relation": "manyToOne", "target": "api::part.part", "inversedBy": "models"}, "dynamic_zone": {"type": "dynamiczone", "pluginOptions": {"i18n": {"localized": true}}, "components": ["dynamic-zone.info-block", "product-parts.part-specifications", "product-parts.related-parts", "product-parts.compatible-products"]}}}