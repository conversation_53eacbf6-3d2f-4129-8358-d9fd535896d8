{"kind": "collectionType", "collectionName": "industrial_solutions", "info": {"singularName": "industrial-solution", "pluralName": "industrial-solutions", "displayName": "Industrial Solution"}, "options": {"draftAndPublish": true}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"adminLabel": {"type": "string", "private": true, "pluginOptions": {"i18n": {"localized": true}}}, "name": {"type": "string", "pluginOptions": {"i18n": {"localized": true}}, "required": true}, "description": {"type": "text", "pluginOptions": {"i18n": {"localized": true}}}, "category": {"type": "relation", "relation": "manyToOne", "target": "api::industrial-solution-category.industrial-solution-category", "inversedBy": "industrial_solutions"}, "dynamic_zone": {"type": "dynamiczone", "pluginOptions": {"i18n": {"localized": true}}, "components": ["dynamic-zone.latest-news-section", "dynamic-zone.form-subscribe", "dynamic-zone.features-section", "dynamic-zone.featured-promotions", "dynamic-zone.accordion-section", "dynamic-zone.testimonials-section", "dynamic-zone.video-section", "dynamic-zone.related-products", "dynamic-zone.media-text-section", "dynamic-zone.image-slider-section", "dynamic-zone.image-box-section", "dynamic-zone.hover-overlay-card-collection", "dynamic-zone.hover-expand-card-collection", "dynamic-zone.related-product-categories", "elementals.media-video", "elementals.media-image", "dynamic-zone.info-block", "dynamic-zone.page-hero-section", "dynamic-zone.news-related", "dynamic-zone.solution-media-gallery"]}, "banner": {"type": "media", "pluginOptions": {"i18n": {"localized": true}}, "multiple": false, "allowedTypes": ["images"]}, "slug": {"type": "uid", "pluginOptions": {"i18n": {"localized": true}}, "targetField": "name"}, "media_gallery": {"type": "component", "pluginOptions": {"i18n": {"localized": true}}, "component": "elementals.media-gallery", "repeatable": false}}}