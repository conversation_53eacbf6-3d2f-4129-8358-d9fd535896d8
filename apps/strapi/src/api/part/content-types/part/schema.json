{"kind": "collectionType", "collectionName": "parts", "info": {"singularName": "part", "pluralName": "parts", "displayName": "Product-Parts"}, "options": {"draftAndPublish": true}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"adminLabel": {"type": "string", "private": true, "pluginOptions": {"i18n": {"localized": true}}}, "name": {"type": "text", "pluginOptions": {"i18n": {"localized": true}}}, "slug": {"type": "uid", "pluginOptions": {"i18n": {"localized": true}}, "targetField": "name"}, "description": {"type": "text", "pluginOptions": {"i18n": {"localized": true}}}, "category": {"type": "relation", "relation": "manyToOne", "target": "api::part-category.part-category", "inversedBy": "parts"}, "models": {"type": "relation", "relation": "oneToMany", "target": "api::part-model.part-model", "mappedBy": "part"}}}