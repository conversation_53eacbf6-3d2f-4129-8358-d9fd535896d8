{"kind": "collectionType", "collectionName": "product_categories", "info": {"singularName": "product-category", "pluralName": "product-categories", "displayName": "Product Categories"}, "options": {"draftAndPublish": true}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"adminLabel": {"type": "string", "private": true, "pluginOptions": {"i18n": {"localized": true}}}, "name": {"type": "string", "pluginOptions": {"i18n": {"localized": true}}, "required": true}, "slug": {"type": "uid", "pluginOptions": {"i18n": {"localized": true}}, "targetField": "name", "required": true}, "excerpt": {"type": "text", "pluginOptions": {"i18n": {"localized": true}}}, "image": {"type": "media", "pluginOptions": {"i18n": {"localized": false}}, "multiple": false, "allowedTypes": ["videos", "images"]}, "content": {"type": "blocks", "pluginOptions": {"i18n": {"localized": true}}}, "rank": {"type": "integer", "pluginOptions": {"i18n": {"localized": true}}}, "children": {"type": "relation", "relation": "manyToOne", "target": "api::product-category.product-category", "inversedBy": "parent"}, "parent": {"type": "relation", "relation": "oneToMany", "target": "api::product-category.product-category", "mappedBy": "children"}, "products": {"type": "relation", "relation": "oneToMany", "target": "api::product.product", "mappedBy": "product_category"}, "dynamic_zone": {"type": "dynamiczone", "pluginOptions": {"i18n": {"localized": true}}, "components": ["dynamic-zone.media-text-section", "dynamic-zone.video-section", "dynamic-zone.page-hero-section", "dynamic-zone.info-block", "products.generator-selection-guide", "dynamic-zone.solution-portfolio-section", "products.recently-viewed-products", "products.product-subcategories-section", "products.products-by-category"]}, "seo": {"type": "component", "pluginOptions": {"i18n": {"localized": true}}, "component": "shared.seo", "repeatable": false}}}