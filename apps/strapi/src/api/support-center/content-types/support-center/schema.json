{"kind": "collectionType", "collectionName": "support_centers", "info": {"singularName": "support-center", "pluralName": "support-centers", "displayName": "Support-Center"}, "options": {"draftAndPublish": true}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"name": {"type": "string", "pluginOptions": {"i18n": {"localized": true}}, "required": true}, "description": {"type": "string", "pluginOptions": {"i18n": {"localized": true}}}, "content": {"type": "blocks", "pluginOptions": {"i18n": {"localized": true}}}, "video": {"type": "media", "pluginOptions": {"i18n": {"localized": true}}, "multiple": false, "allowedTypes": ["videos"]}, "video_embed": {"type": "text", "pluginOptions": {"i18n": {"localized": true}}}, "document_files": {"type": "media", "pluginOptions": {"i18n": {"localized": true}}, "multiple": true, "allowedTypes": ["files"]}, "document_link": {"type": "string", "pluginOptions": {"i18n": {"localized": true}}}, "slug": {"type": "uid", "pluginOptions": {"i18n": {"localized": true}}, "targetField": "name"}, "support_center_category": {"type": "relation", "relation": "manyToOne", "target": "api::support-center-category.support-center-category", "inversedBy": "support_centers"}, "rank": {"type": "integer", "pluginOptions": {"i18n": {"localized": true}}}, "video_thumbnail": {"type": "media", "pluginOptions": {"i18n": {"localized": true}}, "multiple": false, "allowedTypes": ["images", "files", "videos", "audios"]}}}