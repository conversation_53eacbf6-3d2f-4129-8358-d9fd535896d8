{"kind": "collectionType", "collectionName": "article_categories", "info": {"singularName": "article-category", "pluralName": "article-categories", "displayName": "Article-Category"}, "options": {"draftAndPublish": true}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"title": {"type": "text", "pluginOptions": {"i18n": {"localized": true}}, "required": true}, "slug": {"type": "uid", "targetField": "title"}, "children": {"type": "relation", "relation": "manyToOne", "target": "api::article-category.article-category", "inversedBy": "parent"}, "parent": {"type": "relation", "relation": "oneToMany", "target": "api::article-category.article-category", "mappedBy": "children"}, "articles": {"type": "relation", "relation": "oneToMany", "target": "api::article.article", "mappedBy": "category"}}}