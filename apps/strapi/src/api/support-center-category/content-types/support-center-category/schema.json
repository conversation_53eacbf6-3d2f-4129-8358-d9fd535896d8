{"kind": "collectionType", "collectionName": "support_center_categories", "info": {"singularName": "support-center-category", "pluralName": "support-center-categories", "displayName": "Support-Center-Category"}, "options": {"draftAndPublish": true}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"name": {"type": "string", "pluginOptions": {"i18n": {"localized": true}}, "required": true}, "description": {"type": "text", "pluginOptions": {"i18n": {"localized": true}}}, "rank": {"type": "integer", "pluginOptions": {"i18n": {"localized": true}}}, "support_centers": {"type": "relation", "relation": "oneToMany", "target": "api::support-center.support-center", "mappedBy": "support_center_category"}, "type": {"type": "enumeration", "pluginOptions": {"i18n": {"localized": true}}, "default": "CONTENT", "enum": ["CONTENT", "VIDEO", "DOCUMENT"]}}}