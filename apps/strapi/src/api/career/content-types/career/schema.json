{"kind": "collectionType", "collectionName": "careers", "info": {"singularName": "career", "pluralName": "careers", "displayName": "Career"}, "options": {"draftAndPublish": true}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"adminLabel": {"type": "string", "pluginOptions": {"i18n": {"localized": true}}}, "job_title": {"type": "text", "pluginOptions": {"i18n": {"localized": true}}, "required": true}, "job_type": {"type": "enumeration", "pluginOptions": {"i18n": {"localized": true}}, "enum": ["full-time", "part-time"]}, "hiring_quantity": {"type": "integer", "pluginOptions": {"i18n": {"localized": true}}}, "job_location": {"type": "string", "pluginOptions": {"i18n": {"localized": true}}}, "closing_date": {"type": "date", "pluginOptions": {"i18n": {"localized": true}}}, "main_tasks": {"type": "component", "pluginOptions": {"i18n": {"localized": true}}, "component": "items.job-info", "repeatable": true}, "requirements": {"type": "component", "pluginOptions": {"i18n": {"localized": true}}, "component": "items.job-info", "repeatable": true}, "job_department": {"type": "relation", "relation": "manyToOne", "target": "api::career-category.career-category", "inversedBy": "jobs"}, "slug": {"type": "uid", "pluginOptions": {"i18n": {"localized": true}}, "targetField": "job_title"}, "seo": {"type": "component", "pluginOptions": {"i18n": {"localized": true}}, "component": "shared.seo", "repeatable": false}, "form_job_applications": {"type": "relation", "relation": "oneToMany", "target": "api::form-job-application.form-job-application", "mappedBy": "career"}}}