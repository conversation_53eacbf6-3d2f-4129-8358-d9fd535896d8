{"kind": "collectionType", "collectionName": "product_specifications", "info": {"singularName": "product-specification", "pluralName": "product-specifications", "displayName": "Product Specification"}, "options": {"draftAndPublish": true}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"adminLabel": {"type": "string", "private": true, "pluginOptions": {"i18n": {"localized": true}}}, "name": {"type": "string", "pluginOptions": {"i18n": {"localized": true}}, "required": true}, "unit": {"type": "string", "pluginOptions": {"i18n": {"localized": true}}}, "unit_in_us": {"type": "string", "pluginOptions": {"i18n": {"localized": true}}}, "rank": {"type": "integer", "pluginOptions": {"i18n": {"localized": true}}}, "parent": {"type": "relation", "relation": "manyToOne", "target": "api::product-specification.product-specification", "inversedBy": "children"}, "children": {"type": "relation", "relation": "oneToMany", "target": "api::product-specification.product-specification", "mappedBy": "parent"}, "is_root": {"type": "boolean", "pluginOptions": {"i18n": {"localized": true}}, "default": false}, "product_specification_values": {"type": "relation", "relation": "oneToMany", "target": "api::product-specification-value.product-specification-value", "mappedBy": "product_specification"}, "type": {"type": "enumeration", "pluginOptions": {"i18n": {"localized": false}}, "default": "product", "enum": ["product", "part"]}, "slug": {"type": "uid", "pluginOptions": {"i18n": {"localized": true}}, "targetField": "name"}}}