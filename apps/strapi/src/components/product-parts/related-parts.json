{"collectionName": "components_product_parts_related_parts", "info": {"displayName": "Related_Parts"}, "options": {}, "attributes": {"styles": {"type": "component", "component": "styles.section-styles", "repeatable": false}, "heading": {"type": "component", "component": "elementals.heading", "repeatable": false}, "part_models": {"type": "relation", "relation": "oneToMany", "target": "api::part-model.part-model"}}, "config": {}}