{"collectionName": "components_products_specification_items", "info": {"displayName": "Specification Item", "icon": "play"}, "options": {}, "attributes": {"adminLabel": {"type": "string", "private": true}, "value": {"type": "string"}, "value_in_us": {"type": "string"}, "unit": {"type": "string"}, "product_specification": {"type": "relation", "relation": "oneToOne", "target": "api::product-specification.product-specification"}, "rank": {"type": "integer"}}, "config": {}}