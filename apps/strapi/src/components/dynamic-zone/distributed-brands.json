{"collectionName": "components_dynamic_zone_distributed_brands", "info": {"displayName": "Distributed_Brands"}, "options": {}, "attributes": {"adminLabel": {"type": "string", "private": true}, "styles": {"type": "component", "component": "styles.section-styles", "repeatable": false}, "heading": {"type": "component", "component": "elementals.heading", "repeatable": false}, "brands": {"type": "relation", "relation": "oneToMany", "target": "api::brand.brand"}}, "config": {}}