{"collectionName": "components_dynamic_zone_related_articles", "info": {"displayName": "Related_Articles_Section", "icon": "bulletList", "description": ""}, "options": {}, "attributes": {"adminLabel": {"type": "string", "private": true}, "styles": {"type": "component", "component": "styles.section-styles", "repeatable": false}, "heading": {"type": "component", "component": "elementals.heading", "repeatable": false}, "articles": {"type": "relation", "relation": "oneToMany", "target": "api::article.article"}}}