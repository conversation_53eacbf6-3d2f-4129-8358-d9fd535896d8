{"collectionName": "components_dynamic_zone_locations_sections", "info": {"displayName": "Locations_Section", "icon": "globe"}, "options": {}, "attributes": {"adminLabel": {"type": "string", "private": true}, "styles": {"type": "component", "component": "styles.section-styles", "repeatable": false}, "heading": {"type": "component", "component": "elementals.heading", "repeatable": false}, "items": {"type": "relation", "relation": "oneToMany", "target": "api::branch.branch"}}, "config": {}}