{"collectionName": "components_dynamic_zone_policies_sections", "info": {"displayName": "Policies_Section", "icon": "calendar"}, "options": {}, "attributes": {"styles": {"type": "component", "component": "styles.section-styles", "repeatable": false}, "heading": {"type": "component", "component": "elementals.heading", "repeatable": false}, "policies": {"type": "relation", "relation": "oneToMany", "target": "api::policy.policy"}}, "config": {}}