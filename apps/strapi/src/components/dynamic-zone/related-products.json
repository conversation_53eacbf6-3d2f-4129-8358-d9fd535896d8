{"collectionName": "components_dynamic_zone_related_products", "info": {"displayName": "Related_Products_Section", "icon": "stack"}, "options": {}, "attributes": {"styles": {"type": "component", "component": "styles.section-styles", "repeatable": false}, "heading": {"type": "component", "component": "elementals.heading", "repeatable": false}, "products": {"type": "relation", "relation": "oneToMany", "target": "api::product.product"}}}