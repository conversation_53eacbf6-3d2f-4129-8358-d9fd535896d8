{"collectionName": "components_global_footers", "info": {"displayName": "Footer", "icon": "apps", "description": ""}, "options": {}, "attributes": {"logo": {"type": "relation", "relation": "oneToOne", "target": "api::logo.logo"}, "description": {"type": "text"}, "copyright": {"type": "string"}, "built_with": {"type": "string"}, "policy_links": {"type": "component", "component": "shared.link", "repeatable": true}, "social_media_links": {"type": "component", "component": "shared.link", "repeatable": true}, "footer_nav": {"type": "component", "component": "items.footer-nav-item", "repeatable": true}, "attached_images": {"type": "media", "multiple": true, "allowedTypes": ["images"]}, "footer_contact": {"type": "component", "component": "elementals.footer-contact", "repeatable": false}}}