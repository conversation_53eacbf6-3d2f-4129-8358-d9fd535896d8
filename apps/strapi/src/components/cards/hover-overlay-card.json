{"collectionName": "components_cards_hover_overlay_cards", "info": {"displayName": "Hover_Overlay_Card"}, "options": {}, "attributes": {"adminLabel": {"type": "string", "private": true}, "text": {"type": "string"}, "hover_content": {"type": "component", "component": "elementals.heading", "repeatable": false}, "overlay_color": {"type": "customField", "customField": "plugin::color-picker.color", "regex": "^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$"}, "opacity": {"type": "decimal", "min": 0, "max": 1}, "background": {"type": "component", "component": "styles.background-styles", "repeatable": false}}, "config": {}}