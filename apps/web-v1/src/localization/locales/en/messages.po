msgid ""
msgstr ""
"POT-Creation-Date: 2025-06-24 13:06+0700\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: @lingui/cli\n"
"Language: en\n"
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: \n"
"Last-Translator: \n"
"Language-Team: \n"
"Plural-Forms: \n"

#. js-lingui-explicit-id
#: src/modules/product/components/filter-results.tsx:207
msgid "Display Mode:"
msgstr "Display Mode:"

#. js-lingui-explicit-id
#: src/modules/product/components/filter-results.tsx:219
msgid "PRODUCT MODELS"
msgstr "PRODUCT MODELS"

#. js-lingui-explicit-id
#: src/modules/product/components/filter-results.tsx:228
#: src/modules/product/components/filter-results.tsx:275
msgid "Loading products..."
msgstr "Loading products..."

#. js-lingui-explicit-id
#: src/modules/product/components/filter-results.tsx:234
#: src/modules/product/components/filter-results.tsx:286
msgid "No products found"
msgstr "No products found"

#. js-lingui-explicit-id
#: src/modules/product/components/filter-bar.tsx:168
msgid "Category"
msgstr "Category"

#. js-lingui-explicit-id
#: src/modules/product/components/filter-bar.tsx:174
#: src/modules/product/components/filter-bar.tsx:257
#: src/components/dynamic-zone/products-by-category.tsx:70
msgid "Loading..."
msgstr "Loading..."

#. js-lingui-explicit-id
#: src/modules/product/components/filter-bar.tsx:178
msgid "Error loading categories. Please try again."
msgstr "Error loading categories. Please try again."

#. js-lingui-explicit-id
#: src/modules/product/components/filter-bar.tsx:182
msgid "No categories available"
msgstr "No categories available"

#. js-lingui-explicit-id
#: src/modules/product/components/filter-bar.tsx:349
msgid "FILTER PRODUCTS"
msgstr "FILTER PRODUCTS"

#. js-lingui-explicit-id
#: src/modules/product/components/filter-bar.tsx:368
msgid "Specifications"
msgstr "Specifications"

#. js-lingui-explicit-id
#: src/modules/product/components/filter-bar.tsx:374
msgid "Units of measure"
msgstr "Units of measure"

#. js-lingui-explicit-id
#: src/modules/product/components/filter-bar.tsx:397
msgid "Metric"
msgstr "Metric"

#. js-lingui-explicit-id
#: src/modules/product/components/filter-bar.tsx:406
msgid "Loading specifications..."
msgstr "Loading specifications..."

#. js-lingui-explicit-id
#: src/modules/layout/page-breadcrumb.tsx:56
msgid "Home"
msgstr "Home"

#. js-lingui-explicit-id
#: src/components/ui/search-bar.tsx:120
msgid "Search"
msgstr "Search"

#. js-lingui-explicit-id
#: src/components/ui/search-bar.tsx:122
msgid "Enter your search query below to find what you're looking for."
msgstr "Enter your search query below to find what you're looking for."

#. js-lingui-explicit-id
#: src/components/ui/search-bar.tsx:132
msgid "Search products, news, promotions, careers..."
msgstr "Search products, news, promotions, careers..."

#. js-lingui-explicit-id
#: src/components/ui/search-bar.tsx:150
msgid "Loading search data..."
msgstr "Loading search data..."

#. js-lingui-explicit-id
#: src/components/ui/search-bar.tsx:157
msgid "Failed to load search data"
msgstr "Failed to load search data"

#. js-lingui-explicit-id
#: src/components/ui/search-bar.tsx:232
msgid "No results found for \"{query}\""
msgstr "No results found for \"{query}\""

#. js-lingui-explicit-id
#: src/components/ui/search-bar.tsx:238
msgid "Try different keywords or check spelling"
msgstr "Try different keywords or check spelling"

#. js-lingui-explicit-id
#: src/components/ui/search-bar.tsx:249
msgid "Start typing to search across the website"
msgstr "Start typing to search across the website"

#. js-lingui-explicit-id
#: src/components/ui/search-bar.tsx:251
msgid "Products, news, promotions, careers, services and more..."
msgstr "Products, news, promotions, careers, services and more..."

#. js-lingui-explicit-id
#: src/components/ui/search-bar.tsx:259
msgid "Press"
msgstr "Press"

#. js-lingui-explicit-id
#: src/components/ui/search-bar.tsx:263
msgid "to open this dialog again."
msgstr "to open this dialog again."

#. js-lingui-explicit-id
#: src/components/media-gallery/index.tsx:110
msgid "images"
msgstr "images"

#. js-lingui-explicit-id
#: src/components/media-gallery/index.tsx:113
msgid "videos"
msgstr "videos"

#. js-lingui-explicit-id
#: src/components/media-gallery/index.tsx:116
msgid "documents"
msgstr "documents"

#. js-lingui-explicit-id
#: src/components/dynamic-zone/recently-viewed-models-section.tsx:85
#: src/app/(locale)/(web)/promotions/[slug]/page.tsx:62
#: src/app/(locale)/(web)/news/[slug]/page.tsx:61
msgid "No data"
msgstr "No data"

#. js-lingui-explicit-id
#: src/components/dynamic-zone/products-by-category.tsx:71
msgid "View more"
msgstr "View more"

#. js-lingui-explicit-id
#: src/components/dynamic-zone/policies-section.tsx:89
msgid "learn more"
msgstr "learn more"

#. js-lingui-explicit-id
#: src/app/(locale)/(web)/services/page.tsx:55
#: src/app/(locale)/(web)/services/[slug]/page.tsx:74
msgid "Services"
msgstr "Services"

#. js-lingui-explicit-id
#: src/app/(locale)/(web)/promotions/page.tsx:58
#: src/app/(locale)/(web)/promotions/[slug]/page.tsx:71
msgid "Promotions"
msgstr "Promotions"

#. js-lingui-explicit-id
#: src/app/(locale)/(web)/news/page.tsx:56
#: src/app/(locale)/(web)/news/[slug]/page.tsx:70
msgid "News"
msgstr "News"

#. js-lingui-explicit-id
#: src/app/(locale)/(web)/careers/page.tsx:56
#: src/app/(locale)/(web)/careers/[slug]/page.tsx:80
msgid "Careers"
msgstr "Careers"

#. js-lingui-explicit-id
#: src/app/(locale)/(web)/(product-part-pages)/parts/page.tsx:53
#: src/app/(locale)/(web)/(product-part-pages)/parts/[categorySlug]/page.tsx:65
#: src/app/(locale)/(web)/(product-part-pages)/parts/[categorySlug]/[partModelSlug]/page.tsx:65
msgid "Parts"
msgstr "Parts"

#. js-lingui-explicit-id
#: src/app/(locale)/(web)/(product-pages)/products/[slug]/page.tsx:89
#: src/app/(locale)/(web)/(product-pages)/products/[slug]/page.tsx:158
msgid "Products"
msgstr "Products"

#. js-lingui-explicit-id
#: src/app/(locale)/(web)/(product-pages)/product-categories/[slug]/page.tsx:78
msgid "Product Categories"
msgstr "Product Categories"

#: src/modules/contact/components/contact-details.tsx:42
msgid "14th & 16th Floor, Plaschem Building, No 562 Nguyen Van Cu Str, Long Bien Dist, Ha Noi"
msgstr "14th & 16th Floor, Plaschem Building, No 562 Nguyen Van Cu Str, Long Bien Dist, Ha Noi"

#: src/app/(locale)/(protected)/(auth)/sign-up/_form.tsx:123
msgid "Account created successfully!"
msgstr "Account created successfully!"

#: src/app/(locale)/(protected)/(auth)/sign-in/_form.tsx:120
msgid "account."
msgstr "account."

#: src/modules/contact/components/contact-details.tsx:40
msgid "Address"
msgstr "Address"

#: src/modules/news-promotions/components/news-promotions-filter-bar.tsx:170
#: src/modules/career/components/filter-bar.tsx:201
msgid "All"
msgstr "All"

#: src/app/(locale)/(protected)/(auth)/sign-up/_form.tsx:295
msgid "Already have an account?"
msgstr "Already have an account?"

#: src/modules/news-promotions/news-prmotions-detail.tsx:392
msgid "bài viết mới nhất"
msgstr "latest news"

#: src/components/dynamic-zone/form/form-subscriber.tsx:97
msgid "Cảm ơn bạn đã đăng ký!"
msgstr "Cảm ơn bạn đã đăng ký!"

#: src/modules/news-promotions/news-promotions-featured.tsx:78
#: src/modules/news-promotions/components/news-promotions-filter-bar.tsx:152
msgid "Category"
msgstr "Category"

#: src/modules/career/components/filter-bar.tsx:309
msgid "Chỉ hiển thị tin tuyển dụng còn hiệu lực"
msgstr "Only show valid news"

#: src/modules/career/components/filter-bar.tsx:285
msgid "Chọn tỉnh/thành phố"
msgstr "Select province/city"

#: src/modules/contact/components/contact-form.tsx:103
#: src/components/dynamic-zone/form/form-subscriber.tsx:101
msgid "Có lỗi xảy ra. Vui lòng thử lại sau."
msgstr "Có lỗi xảy ra. Vui lòng thử lại sau."

#: src/app/(locale)/(protected)/(auth)/sign-up/_form.tsx:233
msgid "Confirm password"
msgstr "Confirm password"

#: src/app/(locale)/(protected)/(auth)/sign-up/_form.tsx:231
msgid "Confirm Password"
msgstr "Confirm Password"

#: src/app/(locale)/(protected)/(auth)/sign-up/_form.tsx:24
msgid "Confirm password must be at least 8 characters"
msgstr "Confirm password must be at least 8 characters"

#: src/modules/contact/components/contact-form.tsx:138
#: src/modules/contact/components/contact-form.tsx:141
msgid "Công ty"
msgstr "Company"

#: src/app/(locale)/(protected)/(auth)/sign-in/_form.tsx:258
msgid "Create account"
msgstr "Create account"

#: src/app/(locale)/(protected)/(auth)/sign-up/_form.tsx:290
msgid "Create Account"
msgstr "Create Account"

#: src/app/(locale)/(protected)/(auth)/sign-up/_form.tsx:219
msgid "Create password"
msgstr "Create password"

#: src/modules/contact/components/contact-form.tsx:257
#: src/modules/contact/components/contact-form.tsx:261
msgid "Đang gửi..."
msgstr "Sending..."

#: src/app/(locale)/(protected)/(auth)/sign-in/_form.tsx:256
msgid "Don’t have an account?"
msgstr "Don’t have an account?"

#: src/app/(locale)/(protected)/(auth)/sign-up/_form.tsx:208
#: src/app/(locale)/(protected)/(auth)/sign-in/_form.tsx:134
msgid "Email"
msgstr "Email"

#: src/components/dynamic-zone/form/form-subscriber.tsx:43
msgid "Email is required"
msgstr "Email is required"

#: src/modules/contact/components/contact-form.tsx:27
msgid "Email không hợp lệ"
msgstr "Invalid email"

#: src/app/(locale)/(protected)/(auth)/sign-up/_form.tsx:209
#: src/app/(locale)/(protected)/(auth)/sign-in/_form.tsx:140
msgid "Enter your email"
msgstr "Enter your email"

#: src/app/(locale)/(protected)/(auth)/sign-up/_form.tsx:197
msgid "Enter your name"
msgstr "Enter your name"

#: src/app/(locale)/(protected)/(auth)/sign-in/_form.tsx:165
msgid "Enter your password"
msgstr "Enter your password"

#: src/app/(locale)/(protected)/(auth)/sign-up/_form.tsx:176
msgid "Explore a range of features designed to elevate your business and drive growth."
msgstr "Explore a range of features designed to elevate your business and drive growth."

#: src/modules/product/components/deails-header.tsx:39
msgid "FEATURED FOR FAST PAYBACK"
msgstr "FEATURED FOR FAST PAYBACK"

#: src/modules/news-promotions/components/news-promotions-filter-bar.tsx:146
msgid "Filter"
msgstr "Filter"

#: src/app/(locale)/(protected)/(auth)/sign-in/_form.tsx:206
msgid "Forgot password?"
msgstr "Forgot password?"

#: src/modules/news-promotions/components/news-promotions-filter-bar.tsx:210
msgid "From"
msgstr "From"

#: src/app/(locale)/(protected)/(auth)/sign-up/_form.tsx:196
msgid "Full Name"
msgstr "Full Name"

#: src/modules/contact/components/contact-form.tsx:257
#: src/modules/contact/components/contact-form.tsx:261
msgid "Gửi yêu cầu"
msgstr "Send"

#: src/modules/contact/components/contact-form.tsx:98
msgid "Gửi yêu cầu thành công! Chúng tôi sẽ liên hệ với bạn trong thời gian sớm nhất."
msgstr "Request sent successfully! We will get in touch with you as soon as possible."

#: src/modules/career/components/filter-bar.tsx:250
msgid "Hình thức"
msgstr "Work type"

#: src/modules/contact/components/contact-form.tsx:117
#: src/modules/contact/components/contact-form.tsx:129
msgid "Họ và tên"
msgstr "Your name"

#: src/app/(locale)/(protected)/(auth)/sign-up/_form.tsx:260
msgid "I agree to the"
msgstr "I agree to the"

#: src/app/(locale)/(protected)/(auth)/sign-in/_form.tsx:66
msgid "Invalid credentials"
msgstr "Invalid credentials"

#: src/app/(locale)/(protected)/(auth)/sign-in/_form.tsx:56
msgid "Invalid credentials: email or password is invalid"
msgstr "Invalid credentials: email or password is invalid"

#: src/components/dynamic-zone/form/form-subscriber.tsx:44
#: src/app/(locale)/(protected)/(auth)/sign-up/_form.tsx:20
msgid "Invalid email format"
msgstr "Invalid email format"

#: src/app/(locale)/(protected)/(auth)/sign-up/_form.tsx:174
msgid "Join us today and unlock the full potential of"
msgstr "Join us today and unlock the full potential of"

#: src/app/(locale)/(protected)/(auth)/sign-in/_form.tsx:199
msgid "Keep me sign-in"
msgstr "Keep me sign-in"

#: src/components/dynamic-zone/form/form-subscriber.tsx:240
msgid "Kết nối với Phú Thái Cat"
msgstr "Connect with Phu Thai Cat"

#: src/modules/contact/components/contact-form.tsx:23
#: src/modules/contact/components/contact-form.tsx:26
#: src/modules/contact/components/contact-form.tsx:28
#: src/modules/contact/components/contact-form.tsx:30
msgid "Không được để trống"
msgstr "This field is required"

#: src/modules/career/components/filter-results.tsx:175
#: src/modules/career/components/filter-bar.tsx:210
msgid "Không tìm thấy dữ liệu"
msgstr "No data"

#: src/modules/news-promotions/news-prmotions-detail.tsx:399
msgid "khuyến mại mới nhất"
msgstr "latest promotions"

#: src/modules/product/components/deails-header.tsx:32
msgid "Live chat"
msgstr "Live chat"

#: src/modules/career/components/filter-bar.tsx:323
msgid "Lọc vị trí tuyển dụng"
msgstr "Filter hiring position"

#: src/app/(locale)/(protected)/(auth)/sign-in/_form.tsx:222
msgid "Login now"
msgstr "Login now"

#: src/app/(locale)/(protected)/(auth)/sign-in/_form.tsx:60
msgid "Login Successful"
msgstr "Login Successful"

#: src/app/(locale)/(protected)/(auth)/sign-in/_form.tsx:118
msgid "Login with your email and password you have been created before, or you can create an account if you don’t have a"
msgstr "Login with your email and password you have been created before, or you can create an account if you don’t have a"

#: src/modules/product/b-generator-selection-guide.tsx:105
msgid "Module"
msgstr "Module"

#: src/modules/news-promotions/news-prmotions-detail.tsx:256
msgid "Mua Ngay"
msgstr "Buy now"

#: src/modules/news-promotions/news-prmotions-detail.tsx:292
msgid "Mục lục"
msgstr "Table of content"

#: src/components/dynamic-zone/form/form-subscriber.tsx:40
#: src/app/(locale)/(protected)/(auth)/sign-up/_form.tsx:19
msgid "Name is required"
msgstr "Name is required"

#: src/modules/news-promotions/components/news-promotions-search-panel.tsx:74
#: src/modules/news-promotions/components/news-promotions-search-panel.tsx:80
#: src/modules/career/components/filter-results.tsx:150
#: src/modules/career/components/filter-results.tsx:156
msgid "Newest"
msgstr "Newest"

#: src/modules/news-promotions/news-promotions-featured.tsx:37
#: src/modules/news-promotions/news-promotions-featured.tsx:103
#: src/modules/news-promotions/components/news-promotions-filter-bar.tsx:146
#: src/modules/news-promotions/components/news-promotions-filter-bar.tsx:170
#: src/modules/news-promotions/components/news-promotions-filter-bar.tsx:278
msgid "News"
msgstr "News"

#: src/modules/contact/components/contact-form.tsx:245
msgid "Nhập nội dung yêu cầu"
msgstr "Please enter the content"

#: src/modules/news-promotions/news-promotions-featured.tsx:68
#: src/modules/news-promotions/news-promotions-featured.tsx:192
#: src/modules/news-promotions/news-promotion-listing.tsx:104
#: src/modules/news-promotions/components/post-compact-list.tsx:39
#: src/components/dynamic-zone/product-subcategories-section.tsx:35
#: src/components/dynamic-zone/news-promotions/latest-promotions.tsx:57
#: src/components/dynamic-zone/news-promotions/latest-news.tsx:56
#: src/components/dynamic-zone/cms-sections/contact-branches-section.tsx:193
#: src/components/card/card-product.tsx:121
msgid "No data"
msgstr "No data"

#: src/modules/contact/components/contact-form.tsx:233
msgid "Nội dung yêu cầu"
msgstr "Requested content"

#: src/modules/news-promotions/components/news-promotions-search-panel.tsx:74
#: src/modules/news-promotions/components/news-promotions-search-panel.tsx:83
#: src/modules/career/components/filter-results.tsx:150
#: src/modules/career/components/filter-results.tsx:159
msgid "Oldest"
msgstr "Oldest"

#: src/modules/news-promotions/components/news-promotions-filter-bar.tsx:278
msgid "Only show"
msgstr "Only show"

#: src/app/(locale)/(protected)/(auth)/sign-up/_form.tsx:217
#: src/app/(locale)/(protected)/(auth)/sign-in/_form.tsx:157
msgid "Password"
msgstr "Password"

#: src/app/(locale)/(protected)/(auth)/sign-up/_form.tsx:21
msgid "Password must be at least 8 characters"
msgstr "Password must be at least 8 characters"

#: src/app/(locale)/(protected)/(auth)/sign-up/_form.tsx:31
msgid "Passwords must match"
msgstr "Passwords must match"

#: src/modules/career/components/filter-bar.tsx:182
msgid "Phòng ban/bộ phận"
msgstr "Department/division"

#: src/modules/news-promotions/news-promotions-featured.tsx:42
#: src/modules/news-promotions/news-promotions-featured.tsx:115
#: src/modules/news-promotions/components/news-promotions-filter-bar.tsx:146
#: src/modules/news-promotions/components/news-promotions-filter-bar.tsx:170
#: src/modules/news-promotions/components/news-promotions-filter-bar.tsx:278
msgid "Promotions"
msgstr "Promotions"

#: src/components/dynamic-zone/cms-sections/contact-branches-section.tsx:159
#: src/components/dynamic-zone/cms-sections/contact-branches-section.tsx:162
msgid "Province/City"
msgstr "Province/City"

#: src/modules/news-promotions/components/news-promotions-filter-bar.tsx:204
msgid "Published Date"
msgstr "Published Date"

#: src/modules/career/components/filter-results.tsx:122
msgid "Search by job title"
msgstr "Search by job title"

#: src/modules/news-promotions/components/news-promotions-search-panel.tsx:56
msgid "Search by title or content"
msgstr "Search by title or content"

#: src/components/dynamic-zone/cms-sections/contact-branches-section.tsx:135
msgid "Search for a branch"
msgstr "Search for a branch"

#: src/app/(locale)/(protected)/(auth)/sign-up/_form.tsx:300
msgid "Sign in"
msgstr "Sign in"

#: src/app/(locale)/(protected)/(auth)/sign-up/_form.tsx:128
msgid "Sign-up failed"
msgstr "Sign-up failed"

#: src/modules/contact/components/contact-form.tsx:154
msgid "Số điện thoại"
msgstr "Phone"

#: src/app/(locale)/(protected)/(auth)/sign-up/_form.tsx:130
msgid "Something went wrong"
msgstr "Something went wrong"

#: src/modules/news-promotions/components/news-promotions-search-panel.tsx:73
#: src/modules/career/components/filter-results.tsx:149
msgid "Sort"
msgstr "Sort"

#: src/app/(locale)/(protected)/(auth)/sign-up/_form.tsx:262
msgid "Terms & Conditions"
msgstr "Terms & Conditions"

#: src/modules/news-promotions/components/news-promotions-filter-bar.tsx:239
msgid "To"
msgstr "To"

#: src/modules/news-promotions/news-promotions-featured.tsx:75
#: src/modules/news-promotions/news-prmotions-detail.tsx:245
#: src/modules/news-promotions/components/post-compact-card.tsx:60
msgid "Valid until"
msgstr "Valid until"

#: src/modules/career/components/filter-bar.tsx:281
msgid "Vị trí tuyển dụng"
msgstr "Hiring position"

#: src/modules/product/b-product-categories.tsx:72
#: src/components/dynamic-zone/product-categories-section.tsx:101
msgid "View Details"
msgstr "View Details"

#: src/modules/news-promotions/news-promotion-listing.tsx:138
msgid "View more"
msgstr "View more"

#: src/components/card/card-solution-category.tsx:53
msgid "View More"
msgstr "View More"

#: src/app/(locale)/(protected)/(auth)/sign-in/_form.tsx:105
msgid "Welcome back to"
msgstr "Welcome back to"

#: src/app/(locale)/(protected)/(auth)/sign-up/_form.tsx:161
msgid "Welcome to"
msgstr "Welcome to"

#: src/modules/news-promotions/components/news-promotions-filter-bar.tsx:279
msgid "with effective date"
msgstr "with effective date"

#: src/modules/product/components/deails-header.tsx:31
msgid "Yêu cầu báo giá"
msgstr "Yêu cầu báo giá"

#: src/app/(locale)/(protected)/(auth)/sign-up/_form.tsx:26
#: src/app/(locale)/(protected)/(auth)/sign-up/_form.tsx:272
msgid "You must agree to the terms and conditions"
msgstr "You must agree to the terms and conditions"
