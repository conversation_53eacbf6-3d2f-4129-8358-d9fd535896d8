import { Messages } from '@lingui/core'
import linguiConfig from '../../lingui.config'

const { locales } = linguiConfig
type SupportedLocales = string

export const loadAppCatalog = async (
  locale: SupportedLocales,
): Promise<{ [k: string]: Messages }> => {
  try {
    const module = await import(`./locales/${locale}/messages.po`)
    const messages = module.messages || module.default?.messages || {}
    console.log(`Loaded messages for ${locale}:`, messages)
    return {
      [locale]: messages,
    }
  } catch (error) {
    console.error(`Failed to load catalog for ${locale}:`, error)
    return { [locale]: {} }
  }
}

export const getAppAllMessages = async () => {
  const catalogs = await Promise.all(locales.map(loadAppCatalog))
  const allMessages = catalogs.reduce((acc, oneCatalog) => {
    return { ...acc, ...oneCatalog }
  }, {})

  if (!allMessages['en']) {
    console.error('No messages loaded for fallback locale "en"')
  }

  return allMessages
}

export const getAppMessages = async (locale: SupportedLocales) => {
  const messages = await loadAppCatalog(locale)
  return messages
}
