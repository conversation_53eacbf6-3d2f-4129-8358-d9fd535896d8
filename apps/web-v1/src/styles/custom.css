::selection {
  background-color: #5e99ec;
  color: #fff;
}

::-moz-selection {
  background-color: #5e99ec;
  color: #fff;
}

.dark ::selection {
  background-color: #5e99ec;
  color: #fff;
}

/* Style scrollbar cho Firefox */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #d1d5db transparent;
}

/* Style scrollbar cho Webkit (Chrome, Safari) */
.custom-scrollbar::-webkit-scrollbar {
  width: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: #d1d5db;
  border-radius: 9999px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: #9ca3af;
}

/* Filter bar scrollbar - thinner and more elegant */
.filter-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #e5e7eb transparent;
  overscroll-behavior: contain;
}

.filter-scrollbar::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.filter-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.filter-scrollbar::-webkit-scrollbar-thumb {
  background-color: #e5e7eb;
  border-radius: 3px;
  border: none;
}

.filter-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: #d1d5db;
}

.filter-scrollbar::-webkit-scrollbar-thumb:active {
  background-color: #9ca3af;
}

.filter-scrollbar::-webkit-scrollbar-corner {
  background: transparent;
}
