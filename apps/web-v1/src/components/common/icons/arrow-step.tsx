import React from 'react'

interface IconProps extends React.SVGProps<SVGSVGElement> {
  className: string

  color?: string
}

export default function StepArrowIcon({
  color = '#FFCC00',

  className,
}: IconProps) {
  return (
    <svg
      width={109}
      height={78}
      viewBox="0 0 109 78"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M69.8384 0.617543C69.154 0.904012 68.7074 1.5725 68.7074 2.31432L68.7074 22.4369L1.83019 22.4369C0.819956 22.4369 -1.33214e-06 23.2596 -1.28784e-06 24.2733L-3.52752e-09 53.6548C4.07811e-08 54.6685 0.819957 55.4912 1.83019 55.4912L68.7076 55.4912L68.7076 75.691C68.7076 76.4328 69.1542 77.1013 69.835 77.3877C70.5194 77.6706 71.3064 77.5163 71.8299 76.9911L108.462 40.3561C108.806 40.0109 109 39.5443 109 39.0559C109 38.5675 108.806 38.101 108.465 37.7558L71.8335 1.0142C71.3099 0.488942 70.523 0.331074 69.8384 0.617543Z"
        fill={color}
        fillOpacity="0.1"
      />
    </svg>
  )
}
