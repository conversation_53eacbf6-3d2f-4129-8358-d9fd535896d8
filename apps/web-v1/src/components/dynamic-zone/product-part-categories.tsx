'use client'

import { useGetPartCategories } from '~/src/libs/cms/strapi/use-parts'
import { useLocale } from '~/src/libs/data/use-locale'
import EmptyContent from '../../../../../packages/ui/src/templates/empty/empty-content'
import Card<PERSON><PERSON>Zoom from '../card/card-hover-zoom'
import HeadingSection from '../renderer/heading-section'
import StylesSection from '../renderer/styles-section'
//----------------------------------------------------------------------------------

type TProps = {
  styles: any
  heading: any
}
//----------------------------------------------------------------------------------
export default function ProductPartCategories({ styles, heading }: TProps) {
  const locale = useLocale()

  const { data, isLoading, error } = useGetPartCategories({
    locale,
  })
  if (isLoading) {
    return (
      <div className="w-12 h-12 border-4 border-gray-50 border-t-transparent rounded-full animate-spin"></div>
    )
  }

  const notFound = !isLoading && error && !data?.data.length

  if (notFound) {
    return <EmptyContent title="Đang cập nhật..." />
  }

  const renderContent = (
    <div className="flex flex-col gap-y-10">
      {heading ? <HeadingSection heading={heading} /> : null}
      <div
        className="flex flex-col p-5 sm:p-10 lg:p-20 gap-8 md:gap-10 lg:gap-16 rounded-2xl bg-[#FAFAFA]"
        style={{
          boxShadow:
            '0px 4px 8px -2px rgba(16, 24, 40, 0.10), 0px 2px 4px -2px rgba(16, 24, 40, 0.06)',
        }}
      >
        {data?.data?.map((category: any, idx: number) => (
          <CardHoverZoom
            key={idx}
            name={category?.title}
            description={category?.description}
            image={category?.image}
            href={`/parts/${category?.slug}`}
          />
        ))}
      </div>
    </div>
  )
  return <StylesSection styles={styles}>{renderContent}</StylesSection>
}
