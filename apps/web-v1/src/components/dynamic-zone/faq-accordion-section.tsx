import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
  CmsMedia,
  MainButton,
} from '@ttplatform/core-page-builder/components'
import {
  TButtonStylePreset,
  TFAQFields,
  TFAQItem,
  TFAQItemFields,
} from '@ttplatform/core-page-builder/libs'
import { Typography } from '@ttplatform/ui/components'
import { cn } from '@ttplatform/ui/lib'
import { motion, useInView } from 'framer-motion'
import { useRef } from 'react'
import BlockEditor from '../renderer/block-editor'
import HeadingSection from '../renderer/heading-section'
import StylesSection from '../renderer/styles-section'

const BFAQSection: React.FC<TFAQItemFields> = ({
  items,
  image,
  text_color,
  item_border_radius = 8,
  show_order,
  background_color,
}) => {
  const ref = useRef(null)
  const isInView = useInView(ref, { once: true, margin: '-100px' })

  const accordionVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: (index: number) => ({
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
        delay: index * 0.2,
        ease: 'easeOut',
      },
    }),
  }

  return (
    <div className="flex flex-col gap-12 2xl:gap-16 3xl:gap-28">
      <section>
        <div className="mx-auto">
          <div
            className={cn('flex flex-col lg:items-center gap-8 lg:flex-row')}
            ref={ref}
          >
            <div
              className={cn(
                'space-y-4 lg:w-1/2',
                image && image?.url ? 'lg:w-1/2' : 'lg:w-full',
              )}
            >
              {items && items?.length > 0 ? (
                <Accordion
                  type="single"
                  collapsible
                  defaultValue={items[0].id?.toString() || ''}
                  className="flex flex-col gap-3"
                >
                  {items.map((item, index) => (
                    <motion.div
                      key={item.id}
                      variants={accordionVariants as any}
                      initial="hidden"
                      animate={isInView ? 'visible' : 'hidden'}
                      custom={index}
                    >
                      <PartnerAccordionItem
                        id={item.id?.toString() || ''}
                        title={item.title || ''}
                        description={item.description || ''}
                        buttons={item.buttons}
                        icon={item.icon}
                        textColor={text_color}
                        backgroundColor={background_color}
                        showOrder={show_order}
                        index={index}
                        borderRadius={item_border_radius}
                      />
                    </motion.div>
                  ))}
                </Accordion>
              ) : (
                <p className="text-center text-gray-500">
                  No questions available
                </p>
              )}
            </div>

            {image && image?.url && (
              <div className="relative h-[400px] sm:h-[500px] overflow-hidden rounded-lg lg:w-1/2">
                <CmsMedia
                  media={image}
                  className={cn('h-full object-cover w-full')}
                  contentClassName="h-full"
                  width={500}
                  height={500}
                  priority
                />
              </div>
            )}
          </div>
        </div>
      </section>
    </div>
  )
}

interface PartnerAccordionItemProps extends TFAQItem {
  textColor?: string
  backgroundColor?: string
  borderRadius?: number
  showOrder?: boolean
  index?: number
}

function PartnerAccordionItem({
  id,
  title,
  description,
  buttons,
  icon,
  textColor,
  backgroundColor,
  borderRadius,
  showOrder,
  index,
}: PartnerAccordionItemProps) {
  return (
    <AccordionItem
      value={id || ''}
      className={cn('mb-4  border-none last:mb-0')}
      style={{
        borderRadius: borderRadius ? `${borderRadius}px ` : '0px',
        backgroundColor: backgroundColor || '#FFFFFF',
      }}
    >
      <AccordionTrigger
        style={{
          color: textColor || '#FFFFFF',
        }}
        className="h-full px-5 pl-6 py-3 hover:no-underline cursor-pointer"
      >
        <div className="flex gap-2 items-center w-full">
          {showOrder ? (
            <Typography variant="h6" className="text-left">
              {index ? index + 1 : 1}.
            </Typography>
          ) : icon && icon?.url ? (
            <div className="w-8 h-8 sm:w-10 sm:h-10 p-[4px]">
              <CmsMedia media={icon} className="object-cover w-full h-full" />
            </div>
          ) : null}
          <Typography variant="h6" className="text-left">
            {title}
          </Typography>
        </div>
      </AccordionTrigger>
      <AccordionContent
        style={{
          color: textColor || '#FFFFFF',
        }}
        className="px-5 pl-6 pb-4 text-gray-300"
      >
        <div className="mb-4">
          <BlockEditor content={description} />
        </div>
        {buttons &&
          buttons.map((button, index) => (
            <MainButton
              key={index}
              label={button.text}
              url={button.URL}
              variant={(button.variant as TButtonStylePreset) || 'secondary'}
            />
          ))}
      </AccordionContent>
    </AccordionItem>
  )
}

const FAQAccordionSection = ({ heading, accordion, styles }: TFAQFields) => {
  const renderContent = (
    <div className="flex flex-col gap-10">
      <HeadingSection heading={heading || {}}></HeadingSection>
      <BFAQSection {...accordion} />
    </div>
  )
  return <StylesSection styles={styles}>{renderContent}</StylesSection>
}

export default FAQAccordionSection
