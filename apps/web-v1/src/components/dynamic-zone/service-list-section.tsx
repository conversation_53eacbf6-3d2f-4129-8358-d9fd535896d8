// import { useParams } from 'next/navigation';
import { useCallback, useEffect, useState } from 'react'
import fetchContentTypeClient from '~/src/libs/cms/strapi/fetchContentTypeClient'
import CardHoverZoom from '../card/card-hover-zoom'
import HeadingSection from '../renderer/heading-section'
import StylesSection from '../renderer/styles-section'
//----------------------------------------------------------------------------------

type TProps = {
  styles: any
  heading: any
}
//----------------------------------------------------------------------------------
export default function ServiceListSection({ styles, heading }: TProps) {
  // const cookieStore = await cookies()
  // const locale = cookieStore.get('NEXT_LOCALE')?.value ?? 'en'

  const [serviceListData, setServiceListData] = useState<any>(null)

  const fetchProductCategoriesData = useCallback(async () => {
    const data = await fetchContentTypeClient({
      contentType: 'services',
      params: {
        filters: {},
      },
    })
    setServiceListData(data?.data)
  }, [])
  useEffect(() => {
    fetchProductCategoriesData()
  }, [fetchProductCategoriesData])

  const renderContent = (
    <div className="flex flex-col gap-y-10">
      {heading ? <HeadingSection heading={heading} /> : null}
      <div
        className="p-5 md:p-10 lg:p-20 bg-[#FAFAFA] rounded-3xl flex flex-col gap-8 sm:gap-10 md:gap-16"
        style={{
          boxShadow:
            '0px 4px 8px -2px rgba(16, 24, 40, 0.10), 0px 2px 4px -2px rgba(16, 24, 40, 0.06)',
        }}
      >
        {serviceListData?.map((item: any, idx: number) => (
          <CardHoverZoom
            key={idx}
            name={item?.name}
            description={item?.description}
            image={item?.banner}
            href={`/services/${item?.slug}`}
          />
        ))}
      </div>
    </div>
  )
  return <StylesSection styles={styles}>{renderContent}</StylesSection>
}
