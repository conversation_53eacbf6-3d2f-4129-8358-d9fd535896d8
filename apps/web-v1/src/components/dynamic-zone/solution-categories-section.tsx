'use client'

import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from '@ttplatform/ui/components'
import Autoplay from 'embla-carousel-autoplay'
import { useCallback, useEffect, useRef, useState } from 'react'
import fetchContentTypeClient from '~/src/libs/cms/strapi/fetchContentTypeClient'
import { apiRoute } from '~/src/libs/constants/cms'
import { useLocale } from '~/src/libs/data/use-locale'
import CardContentExpand from '../card/card-content-expand'
import HeadingSection from '../renderer/heading-section'
import StylesSection from '../renderer/styles-section'
//----------------------------------------------------------------------------------

type TProps = {
  styles: any
  heading: any
}
//----------------------------------------------------------------------------------
export default function SolutionCategoriesSection({ styles, heading }: TProps) {
  const locale = useLocale()

  const plugin = useRef(Autoplay({ delay: 5000, stopOnInteraction: true }))

  const [solutionCategoriesData, setSolutionCategoriesData] =
    useState<any>(null)

  const fetchSolutionCategoriesData = useCallback(async () => {
    const categoriesData = await fetchContentTypeClient({
      contentType: apiRoute.industrialSolutionCategories,
      params: {
        filters: {
          locale,
        },
      },
    })
    setSolutionCategoriesData(categoriesData?.data)
  }, [locale])

  useEffect(() => {
    fetchSolutionCategoriesData()
  }, [fetchSolutionCategoriesData])

  const renderContent = (
    <div className="flex flex-col gap-y-10">
      {heading ? <HeadingSection heading={heading} /> : null}
      <div>
        <Carousel
          plugins={[plugin.current]}
          opts={{
            align: 'start',
            loop: true,
          }}
          className="w-full relative"
        >
          <CarouselContent>
            {solutionCategoriesData ? solutionCategoriesData?.map((item: any, idx: number) => (
              <CarouselItem
                key={idx}
                className="basis-1/1 sm:basis-1/2 md:basis-1/3"
              >
                <CardContentExpand
                  title={item?.name}
                  description={item?.description}
                  image={item?.image}
                  href={`industrial-solutions/${item?.slug}`}
                />
              </CarouselItem>
            )) : <LoadingSkeleton />}
          </CarouselContent>
        </Carousel>
      </div>
    </div>
  )

  return <StylesSection styles={styles}>{renderContent}</StylesSection>
}

function LoadingSkeleton() {
  return (
    <>
      {[1, 2, 3].map((item) => (
        <CarouselItem
          key={item}
          className="basis-1/1 sm:basis-1/2 md:basis-1/3"
        >
          <div className="relative w-full h-auto aspect-[6/9] flex items-end rounded-lg overflow-hidden group">
            {/* Image skeleton */}
            <div className="absolute inset-0 bg-zinc-100 rounded-xl" />

            {/* Content overlay */}
            <div className="absolute inset-0 flex flex-col justify-end p-8 rounded-xl z-50">
              {/* Title skeleton */}
              <div className="h-8 bg-zinc-200 rounded w-3/4 mb-1" />
              <div className="w-[55px] md:w-[68px] h-[5px] md:h-[8px] bg-zinc-200 rounded" />

              {/* Description skeleton */}
              <div className="my-4 space-y-2">
                <div className="h-4 bg-zinc-200 rounded w-full" />
                <div className="h-4 bg-zinc-200 rounded w-5/6" />
                <div className="h-4 bg-zinc-200 rounded w-4/6" />
              </div>

              {/* Button skeleton */}
              <div className="h-10 bg-zinc-200 rounded w-32" />
            </div>
          </div>
        </CarouselItem>
      ))}
    </>
  )
}
