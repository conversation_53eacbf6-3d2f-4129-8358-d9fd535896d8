'use client'

import { Icon } from '@iconify/react'
import {
  BgCommitment,
  CmsMedia,
  MainButton,
} from '@ttplatform/core-page-builder/components'
import {
  TButtonStylePreset,
  TCommitmentSchema,
  TImagePointMarkersSchema,
  TPartnersSchema,
  getTextClass,
} from '@ttplatform/core-page-builder/libs'
import { MarqueeContent, Typography } from '@ttplatform/ui/components'
import { cn } from '@ttplatform/ui/lib'
import gsap from 'gsap'
import { ScrollTrigger } from 'gsap/dist/ScrollTrigger'
import Image from 'next/image'
import { useEffect, useRef } from 'react'
import { useMediaQuery } from 'usehooks-ts'
import PointMarker from '../../image-point-marker/point-marker'
import HeadingSection from '../../renderer/heading-section'
import StylesSection from '../../renderer/styles-section'

gsap.registerPlugin(ScrollTrigger)

type TProps = {
  heading?: any
  styles?: any
  point_markers?: TImagePointMarkersSchema
  block_partners?: TPartnersSchema
  block_commitments?: TCommitmentSchema
}

const PartnerCommitmentSection = ({
  styles,
  point_markers,
  block_partners,
  block_commitments,
}: TProps) => {
  const containerRef = useRef(null)
  const partnersBannerRef = useRef(null)
  const commitmentSectionRef = useRef(null)
  const isDesktop = useMediaQuery('(min-width: 1280px)')
  const isSmScreen = useMediaQuery('(min-width: 640px)')

  useEffect(() => {
    if (typeof window === 'undefined') return

    // Disable parallax on mobile/tablet completely for SEO and performance
    if (!isDesktop) {
      ScrollTrigger.getAll().forEach((trigger) => trigger.kill())
      return
    }

    const container = containerRef.current
    const partnersBanner = partnersBannerRef.current
    const commitmentSection = commitmentSectionRef.current

    if (!container) return

    if (partnersBanner) {
      gsap.fromTo(
        partnersBanner,
        { y: 40 },
        {
          y: -40,
          ease: 'none',
          scrollTrigger: {
            trigger: container,
            start: 'top bottom',
            end: 'bottom top',
            scrub: 0.5,
            invalidateOnRefresh: true,
          },
        },
      )
    }

    if (commitmentSection) {
      gsap.fromTo(
        commitmentSection,
        { y: -40 },
        {
          y: 40,
          ease: 'none',
          scrollTrigger: {
            trigger: container,
            start: 'top bottom',
            end: 'bottom top',
            scrub: 0.5,
            invalidateOnRefresh: true,
          },
        },
      )
    }

    return () => {
      ScrollTrigger.getAll().forEach((trigger) => trigger.kill())
    }
  }, [isDesktop])

  const renderBlockPartners = (
    <div
      ref={partnersBannerRef}
      className="relative mx-auto w-full"
      style={isDesktop ? { willChange: 'transform' } : {}}
    >
      <div
        className="rounded-lg py-6 sm:px-8 w-full flex flex-col gap-4 sm:gap-10 bg-white sm:bg-transparent"
        style={{
          backgroundImage: isSmScreen
            ? 'linear-gradient(270deg, rgb(255, 222, 89) 0%, rgb(255, 211, 38) 16%, rgb(255, 204, 0) 42%)'
            : 'none',
        }}
      >
        {block_partners?.heading?.heading?.text && (
          <HeadingSection
            heading={{
              ...block_partners?.heading,
              heading: {
                ...block_partners?.heading?.heading,
                styles: {
                  ...block_partners?.heading?.heading?.styles,
                  color:
                    block_partners?.heading?.heading?.styles?.color ||
                    '#182230',
                  text_align:
                    block_partners?.heading?.heading?.styles?.text_align ||
                    'text-center',
                },
              },
            }}
            buttonsClassName="justify-center"
            underlineBackground="#182230"
          />
        )}

        {block_partners?.images && block_partners?.images?.length > 0 ? (
          <div className="flex items-center justify-between relative">
            <MarqueeContent
              speed={80}
              pauseOnHover={true}
              autoFill={true}
              direction="left"
              className="py-2"
            >
              {block_partners?.images?.map((partner: any, index: number) => (
                <CmsMedia
                  key={index}
                  media={partner}
                  className={cn(
                    'h-10 md:h-16 w-auto object-contain transition-transform duration-300 hover:scale-105',
                    'grayscale hover:grayscale-0 filter transition-all duration-300',
                    'mx-4',
                  )}
                />
              ))}
            </MarqueeContent>
          </div>
        ) : null}
      </div>
    </div>
  )

  const renderBlockCommitment = (
    <div
      ref={commitmentSectionRef}
      className="lg:absolute max-w-[720px] lg:ml-auto w-full lg:bottom-16 lg:right-16 mt-8 lg:mt-0"
      style={isDesktop ? { willChange: 'transform' } : {}}
    >
      <div className="relative rounded-xl w-full">
        <div className="h-auto w-1/5 cursor-pointer absolute -top-10 xs:-top-12 sm:-top-16 right-0 -translate-x-1/2 flex items-center justify-center z-10 hover:contrast-125">
          <Image
            src="/icons/cva.png"
            alt="cva"
            width={100}
            height={100}
            priority
            className="h-full w-full aspect-square object-cover "
          />
        </div>
        <div className="opacity-80">
          <BgCommitment className="w-full h-full" />
          <div className="w-full bg-[#E3E3E3] h-40 xs:h-20 sm:h-0 -translate-y-6 rounded-bl-xl rounded-br-xl"></div>
        </div>

        <div className="absolute z-10 top-1/2 left-5 sm:left-8 -translate-y-1/2">
          {block_commitments?.heading?.heading?.text && (
            <HeadingSection
              heading={{
                ...block_commitments?.heading,
                heading: {
                  ...block_commitments?.heading?.heading,
                  styles: {
                    ...block_commitments?.heading?.heading?.styles,
                    color:
                      block_commitments?.heading?.heading?.styles?.color ||
                      '#182230',
                    text_align:
                      block_commitments?.heading?.heading?.styles?.text_align ||
                      'text-center',
                  },
                },
              }}
              buttonsClassName="justify-center"
              underlineBackground="#182230"
              headingClassName="max-w-md"
            />
          )}
          <ul className="space-y-4 my-6">
            {block_commitments?.content_items?.map(
              (item: any, index: number) => (
                <li key={index} className="flex items-start gap-3">
                  <div className="h-6 w-6 flex items-center justify-center bg-yellow-400 rounded-full">
                    <Icon
                      icon="material-symbols:check-rounded"
                      width="18"
                      height="18"
                      className="text-gray-700"
                    />
                  </div>
                  <Typography
                    variant="body2"
                    classNames={{
                      root: cn(
                        getTextClass('body'),
                        'text-base md:text-lg font-normal text-gray-900',
                      ),
                    }}
                  >
                    {item.text}
                  </Typography>
                </li>
              ),
            )}
          </ul>
          {block_commitments?.buttons &&
          block_commitments.buttons.length > 0 ? (
            <div className="flex items-start justify-start gap-4">
              {block_commitments?.buttons?.map((button: any, index: number) => (
                <MainButton
                  key={index}
                  label={button.text}
                  variant={
                    (button.variant || 'secondary') as TButtonStylePreset
                  }
                  url={button.URL}
                  target={button.target}
                />
              ))}
            </div>
          ) : null}
        </div>
      </div>
    </div>
  )

  const renderContent = (
    <StylesSection styles={styles} className="!bg-transparent">
      <div
        ref={containerRef}
        className="md:-[50dvh] xl:h-[100dvh] flex flex-col justify-between py-8 xl:py-10"
      >
        {/* Partners banner */}
        {block_partners && renderBlockPartners}
      </div>
    </StylesSection>
  )

  if (!point_markers) {
    return (
      <div className="space-y-8">
        {renderContent}
        {block_commitments && renderBlockCommitment}
      </div>
    )
  }

  // Desktop layout - overlapping with absolute positioning
  if (isDesktop) {
    return (
      <div>
        <PointMarker point_markers={point_markers || {}}>
          {renderContent}
          {block_commitments && renderBlockCommitment}
        </PointMarker>
      </div>
    )
  }

  // Mobile/Tablet layout - vertical stack
  return (
    <div className="space-y-8">
      {/* Partners banner first */}
      {renderContent}

      <div className="w-full">
        <PointMarker point_markers={point_markers || {}}>
          <div className="min-h-[50vh]" />{' '}
          {/* Spacer to maintain aspect ratio */}
        </PointMarker>
      </div>

      {/* Commitment section last */}
      {block_commitments && (
        <div className="w-full mt-20 md:mt-20 px-4">
          <div className="relative rounded-xl w-full max-w-2xl mx-auto">
            <div className="h-auto w-1/5 cursor-pointer absolute -top-10 xs:-top-12 sm:-top-16 right-0 -translate-x-1/2 flex items-center justify-center z-10 hover:contrast-125">
              <Image
                src="/icons/cva.png"
                alt="cva"
                width={100}
                height={100}
                priority
                className="h-full w-full aspect-square object-cover "
              />
            </div>
            <div className="opacity-80">
              <BgCommitment className="w-full h-full" />
              <div className="w-full bg-[#E3E3E3] h-40 xs:h-20 sm:h-0 -translate-y-6 rounded-bl-xl rounded-br-xl"></div>
            </div>

            <div className="absolute z-10 top-1/2 left-5 sm:left-8 -translate-y-1/2">
              {block_commitments?.heading?.heading?.text && (
                <HeadingSection
                  heading={{
                    ...block_commitments?.heading,
                    heading: {
                      ...block_commitments?.heading?.heading,
                      styles: {
                        ...block_commitments?.heading?.heading?.styles,
                        color:
                          block_commitments?.heading?.heading?.styles?.color ||
                          '#182230',
                        text_align:
                          block_commitments?.heading?.heading?.styles
                            ?.text_align,
                      },
                    },
                  }}
                  buttonsClassName="justify-center"
                  underlineBackground="#182230"
                />
              )}
              <ul className="space-y-4 my-6">
                {block_commitments?.content_items?.map(
                  (item: any, index: number) => (
                    <li key={index} className="flex items-start gap-3">
                      <div className="h-6 w-6 flex items-center justify-center bg-yellow-400 rounded-full">
                        <Icon
                          icon="material-symbols:check-rounded"
                          width="18"
                          height="18"
                          className="text-gray-700"
                        />
                      </div>
                      <Typography
                        variant="body2"
                        classNames={{
                          root: cn(
                            getTextClass('body'),
                            'text-base md:text-lg font-normal text-gray-900',
                          ),
                        }}
                      >
                        {item.text}
                      </Typography>
                    </li>
                  ),
                )}
              </ul>
              {block_commitments?.buttons &&
              block_commitments.buttons.length > 0 ? (
                <div className="flex items-start justify-start gap-4">
                  {block_commitments?.buttons?.map(
                    (button: any, index: number) => (
                      <MainButton
                        key={index}
                        label={button.text}
                        variant={
                          (button.variant || 'secondary') as TButtonStylePreset
                        }
                        url={button.URL}
                        target={button.target}
                      />
                    ),
                  )}
                </div>
              ) : null}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default PartnerCommitmentSection
