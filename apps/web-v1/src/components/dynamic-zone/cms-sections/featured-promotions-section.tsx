'use client'

import { ContainerSection } from '@ttplatform/core-page-builder/components'
import React, { useEffect, useState } from 'react'
import { LINGUI_CONFIG } from '~/src/config-global'
import { useGetPromotions } from '~/src/libs/cms/strapi/use-promotions'
import { SectionSkeleton } from '../../ui/skeleton'
import { LatestPromotions } from '../news-promotions'

interface PromotionsSectionProps {
  heading?: any
  locale?: string
  limit?: number
  skip?: number
  [key: string]: any
}

/**
 * Promotions Section component with CMS data fetching
 */
const FeaturedPromotionsSection: React.FC<PromotionsSectionProps> = ({
  heading,
  locale = LINGUI_CONFIG.defaultLocale,

  limit = 3,
  skip = 0,
  ...props
}) => {
  const [promotions, setPromotions] = useState<any[]>([])
  const { data, isLoading, error } = useGetPromotions({
    locale,
    filters: {
      featured: {
        $eq: true,
      },
    },
    pagination: {
      limit: 3,
      start: 0,
    },
    sort: 'publishedAt:desc',
  })

  useEffect(() => {
    if (data?.data) {
      setPromotions(data?.data)
    }
  }, [data])

  const notFound = !isLoading && (error || !promotions?.length)

  if (isLoading) {
    return (
      <ContainerSection styles={{}}>
        <SectionSkeleton items={limit} showHeading={!!heading} />
      </ContainerSection>
    )
  }

  return (
    <LatestPromotions
      promotions={promotions}
      heading={heading}
      locale={locale}
      notFound={notFound}
      {...props}
    />
  )
}

export default FeaturedPromotionsSection
