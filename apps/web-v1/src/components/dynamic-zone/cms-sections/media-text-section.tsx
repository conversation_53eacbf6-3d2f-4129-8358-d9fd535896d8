import { BMediaTextSection } from '@ttplatform/core-page-builder/components'
import { T_MediaTextSectionSchema } from '@ttplatform/core-page-builder/libs'
import { LINGUI_CONFIG } from '~/src/config-global'
import StylesSection from '../../renderer/styles-section'

export default function MediaTextSection(props: T_MediaTextSectionSchema) {
  const { styles } = props
  const { content_max_width } = styles || {}

  if (content_max_width !== 'full_width')
    return (
      <StylesSection
        styles={{
          ...props.styles,
          background: {
            ...props.styles?.background,
            backgroundPosition: 'top left',
          },
        }}
      >
        <BMediaTextSection
          {...props}
          locale={props.locale || LINGUI_CONFIG.defaultLocale}
        />
      </StylesSection>
    )

  return (
    <BMediaTextSection
      {...props}
      locale={props.locale || LINGUI_CONFIG.defaultLocale}
    />
  )
}
