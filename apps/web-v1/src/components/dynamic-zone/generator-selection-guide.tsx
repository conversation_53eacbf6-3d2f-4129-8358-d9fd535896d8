import { BGeneratorSelectionGuide } from '~/src/modules/product/b-generator-selection-guide'
import HeadingSection from '../renderer/heading-section'
import StylesSection from '../renderer/styles-section'

type Props = {
  styles?: any
  heading?: any
  generator_types?: Array<{
    id: number
    generator_type: string
    generator_capacity_types: string
    generator_list: Array<{
      id: number
      module: string
      power: string
    }>
  }>
}

export default function GeneratorSelectionGuide({
  styles,
  heading,
  generator_types,
}: Props) {
  return (
    <StylesSection styles={styles}>
      {heading && <HeadingSection heading={heading} />}
      <BGeneratorSelectionGuide
        title={heading?.heading?.text}
        generatorTypes={generator_types?.map((type) => ({
          type: type.generator_type,
          capacityType: type.generator_capacity_types,
          list: type.generator_list.map((item) => ({
            module: item.module,
            power: item.power,
          })),
        }))}
      />
    </StylesSection>
  )
}
