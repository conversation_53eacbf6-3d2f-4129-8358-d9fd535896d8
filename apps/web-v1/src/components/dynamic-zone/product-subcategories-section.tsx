'use client'
import { t } from '@lingui/core/macro'
import { EmptyContent } from '@ttplatform/ui/templates'
import { Loader2 } from 'lucide-react'
import { useParams } from 'next/navigation'
import { useGetProducts } from '~/src/libs/cms/strapi/use-products'
import { getLocaleClient } from '~/src/libs/data/cookies-client'
import { CardProductCategory } from '../card/card-product-category'
import HeadingSection from '../renderer/heading-section'
import StylesSection from '../renderer/styles-section'

type TProps = {
  styles: any
  heading: any
}

const ProductSubcategoriesSection = ({ styles, heading }: TProps) => {
  const params = useParams()
  const locale = getLocaleClient()
  const { data, isLoading, error } = useGetProducts({
    filters: {
      locale,
      // rank: { $eq: 2 },
      product_category: { slug: { $eq: params.slug } },
    },
    populate: '*',
  })
  const notFound = !isLoading && (!data || !data.data.length || error)
  const renderContent = (
    <div className="flex flex-col gap-y-10">
      {heading ? <HeadingSection heading={heading} /> : null}
      {isLoading ? (
        <Loader2 className="w-10 h-10 animate-spin mx-auto" />
      ) : notFound ? (
        <EmptyContent title={t`No data`} />
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {data.data.map((item: any) => (
            <CardProductCategory key={item.id} category={item} />
          ))}
        </div>
      )}
    </div>
  )
  return <StylesSection styles={styles}>{renderContent}</StylesSection>
}

export default ProductSubcategoriesSection
