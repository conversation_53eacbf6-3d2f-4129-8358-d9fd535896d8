import { Button } from '@ttplatform/ui/components'
import { useParams } from 'next/navigation'
import { useState } from 'react'
import { useGetProductsByCategory } from '~/src/libs/cms/strapi/use-products'
import { useLocale } from '~/src/libs/data/use-locale'
import { useLinguiClient } from '~/src/localization/i18n-client'
import { ProductIndustrialCard } from '~/src/modules/product/components/product-industrial-card'
import HeadingSection from '../renderer/heading-section'
import StylesSection from '../renderer/styles-section'

//----------------------------------------------------------------------------------

type TProps = {
  styles: any
  heading: any
}
//----------------------------------------------------------------------------------

const PAGE_SIZE = 12

export default function ProductsByCategory({ styles, heading }: TProps) {
  const params = useParams()
  const categorySlug = params?.slug as string
  const locale = useLocale()
  const [currentPage, setCurrentPage] = useState(1)
  const i18n = useLinguiClient()

  const { data, isLoading, pagination } = useGetProductsByCategory({
    categorySlug,
    locale,
    page: currentPage,
    pageSize: PAGE_SIZE,
  })

  const productsData = data?.data || []

  const handleShowMore = () => {
    setCurrentPage((prev) => prev + 1)
  }

  const renderContent = (
    <div className="flex flex-col gap-y-10">
      {heading ? <HeadingSection heading={heading} /> : null}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {isLoading && currentPage === 1
          ? Array.from({ length: PAGE_SIZE }).map((_, index) => (
              <div
                key={index}
                className="h-[400px] bg-gray-100 animate-pulse rounded-lg"
              />
            ))
          : productsData.map((product: any) => (
              <ProductIndustrialCard
                key={product.id}
                title={product.name}
                description={product.description}
                image={product.image}
                slug={`/products/${product.slug}`}
              />
            ))}
      </div>
      {pagination?.page < pagination?.pageCount && (
        <div className="flex justify-center mt-6">
          <Button
            className="px-8 py-2 font-semibold rounded"
            onClick={handleShowMore}
            disabled={isLoading}
          >
            {isLoading && currentPage > 1
              ? i18n._('Loading...')
              : i18n._('View more')}
          </Button>
        </div>
      )}
    </div>
  )
  return <StylesSection styles={styles}>{renderContent}</StylesSection>
}
