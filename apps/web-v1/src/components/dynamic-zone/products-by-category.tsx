// import { useParams } from 'next/navigation';
import { useCallback, useEffect, useState } from 'react'
import fetchContentTypeClient from '~/src/libs/cms/strapi/fetchContentTypeClient'
import HeadingSection from '../renderer/heading-section'
import StylesSection from '../renderer/styles-section'
//----------------------------------------------------------------------------------

type TProps = {
  styles: any
  heading: any
}
//----------------------------------------------------------------------------------

export default function ProductsByCategory({ styles, heading }: TProps) {
  const [productsData, setProductsData] = useState<any>(null)
  console.log('🚀 ~ ProductsByCategory ~ productsData:', productsData)

  const fetchProductsData = useCallback(async () => {
    const data = await fetchContentTypeClient({
      contentType: 'products',
      params: {
        filters: {},
      },
    })
    setProductsData(data?.data)
  }, [])
  useEffect(() => {
    fetchProductsData()
  }, [fetchProductsData])

  const renderContent = (
    <div className="flex flex-col gap-y-10">
      {heading ? <HeadingSection heading={heading} /> : null}
    </div>
  )
  return <StylesSection styles={styles}>{renderContent}</StylesSection>
}
