import { MarqueeImage } from '@ttplatform/core-page-builder/components'
import { TImageSliderFields } from '@ttplatform/core-page-builder/libs'
import HeadingSection from '../renderer/heading-section'
import StylesSection from '../renderer/styles-section'

const ImageSliderSection = ({
  heading,
  styles,
  slider,
}: TImageSliderFields) => {
  const renderContent = (
    <div>
      <div className="flex flex-col gap-10">
        <HeadingSection heading={heading || {}} />
        <MarqueeImage
          type={slider?.type || 'default'}
          images={slider?.images || []}
          speed={slider?.speed as any}
          direction={slider?.direction as any}
          pauseOnHover={slider?.pause_on_hover as any}
        />
      </div>
    </div>
  )

  return <StylesSection styles={styles}>{renderContent}</StylesSection>
}

export default ImageSliderSection
