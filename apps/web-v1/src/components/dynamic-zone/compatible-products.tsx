import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  Typography,
} from '@ttplatform/ui/components'
import HeadingSection from '../renderer/heading-section'
import StylesSection from '../renderer/styles-section'
//----------------------------------------------------------------------------------

type TProps = {
  styles: any
  heading: any
  products: any
}
//----------------------------------------------------------------------------------
export default function CompatibleProducts({
  styles,
  heading,
  products,
}: TProps) {
  const renderTable = (
    <div className="w-full  border border-gray-200 rounded-lg overflow-hidden shadow-md">
      <Table className="w-full">
        <TableHeader
          style={{
            background:
              'linear-gradient(90deg, #FC0 70%, #FFD326 90%, #FFDE59 100%)',
          }}
        >
          <TableRow>
            <TableHead className="text-left uppercase px-6 py-4">
              <Typography variant="h4" weight="bold" color="gray-800 h-auto">
                DANH MỤC SẢN PHẨM
              </Typography>
            </TableHead>
            <TableHead className="text-right uppercase px-6 py-4">
              <Typography variant="h4" weight="bold" color="gray-800">
                MODEL SẢN PHẨM
              </Typography>
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {products?.map((item: any, idx: number) => (
            <TableRow key={idx}>
              <TableCell className="font-medium px-6 py-4">
                <Typography variant="body1" weight="medium" color="gray-800">
                  {item?.name}
                </Typography>
              </TableCell>
              <TableCell className="text-right px-6 py-4">
                <Typography variant="body2" weight="normal" color="gray-800">
                  {item?.product_models
                    ?.map((item: any) => item?.name)
                    .join(', ')}
                </Typography>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )
  const renderContent = (
    <div className="flex flex-col gap-y-10">
      {heading ? <HeadingSection heading={heading} /> : null}
      {renderTable}
    </div>
  )
  return <StylesSection styles={styles}>{renderContent}</StylesSection>
}
