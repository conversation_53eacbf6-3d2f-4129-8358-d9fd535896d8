'use client'

import MediaGallery from '../media-gallery'
import HeadingSection from '../renderer/heading-section'
import StylesSection from '../renderer/styles-section'
//----------------------------------------------------------------------------------

type TProps = {
  styles: any
  heading: any
  media_gallery: any
}
//----------------------------------------------------------------------------------

export default function MediaGallerySection({
  styles,
  heading,
  media_gallery,
}: TProps) {
  const renderContent = (
    <div className="flex flex-col gap-y-10">
      {heading ? <HeadingSection heading={heading} /> : null}
      <MediaGallery
        galleryData={media_gallery}
        content_max_width={styles?.content_max_width}
      />
    </div>
  )

  return <StylesSection styles={styles}>{renderContent}</StylesSection>
}
