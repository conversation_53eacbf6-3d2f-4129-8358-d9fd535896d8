import { t } from '@lingui/core/macro'
import { PostCardLarge } from '@ttplatform/core-page-builder/components'
import {
  EBlogStatus,
  ECardType,
  T_PromotionSectionSchema,
  getPromotionDisplayDate,
} from '@ttplatform/core-page-builder/libs'
import { EmptyContent } from '@ttplatform/ui/templates'
import { motion } from 'framer-motion'
import HeadingSection from '../../renderer/heading-section'
import StylesSection from '../../renderer/styles-section'

const LatestPromotions: React.FC<
  T_PromotionSectionSchema & { notFound?: boolean }
> = ({
  promotions = [],
  heading,
  styles,
  notFound,
  // locale = 'vi',
}) => {
  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.7, ease: 'easeOut' },
    },
  }

  const latestPromotions = promotions.slice(0, 3)

  return (
    <StylesSection
      styles={{
        ...styles,
        background: {
          ...styles?.background,
          backgroundPosition: 'bottom right',
        },
      }}
    >
      <section className="b-promotions relative z-1">
        <div className="flex flex-col gap-8 md:gap-12">
          {/* Heading Section */}
          {heading && (
            <HeadingSection
              heading={heading}
              isDisabledIcon={false}
              // fallbackHeading="CHƯƠNG TRÌNH KHUYẾN MẠI"
            />
          )}

          {/* Promotions Grid */}
          {notFound ? (
            <EmptyContent title={t`No data`} />
          ) : (
            <div className="grid gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
              {latestPromotions.map((promo, index) => {
                const displayDate = getPromotionDisplayDate(promo)

                return (
                  <motion.div
                    key={promo.id}
                    variants={cardVariants as any}
                    initial="hidden"
                    animate="visible"
                    transition={{ delay: index * 0.2 }}
                  >
                    <PostCardLarge
                      id={promo.id.toString()}
                      type={ECardType.Promotion}
                      slug={promo.slug}
                      coverImage={promo.image}
                      title={promo.title}
                      excerpt={promo.description || 'Ưu đãi đặc biệt'}
                      validUntil={displayDate} // Use as validUntil for promotions
                      status={EBlogStatus.PUBLISHED}
                    />
                  </motion.div>
                )
              })}
            </div>
          )}
        </div>
      </section>
    </StylesSection>
  )
}

export default LatestPromotions
