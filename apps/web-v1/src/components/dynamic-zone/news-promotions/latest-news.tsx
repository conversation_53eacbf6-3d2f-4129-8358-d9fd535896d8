'use client'
import { t } from '@lingui/core/macro'
import { PostCardLarge } from '@ttplatform/core-page-builder/components'
import {
  EBlogStatus,
  ECardType,
  T_NewsSectionSchema,
  getNewsDisplayDate,
} from '@ttplatform/core-page-builder/libs'
import { EmptyContent } from '@ttplatform/ui/templates'
import { motion, useInView } from 'framer-motion'
import { useRef } from 'react'
import { cn } from '~/src/utils'
import HeadingSection from '../../renderer/heading-section'
import StylesSection from '../../renderer/styles-section'

const LatestNews: React.FC<T_NewsSectionSchema & { notFound?: boolean }> = ({
  news = [],
  heading,
  styles,
  notFound,
}) => {
  const newsRef = useRef<HTMLDivElement>(null)
  const isNewsInView = useInView(newsRef, { once: true, margin: '-100px' })

  const fadeInUp = {
    hidden: { opacity: 0, y: 50 },
    visible: (index: number = 0) => ({
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
        delay: index * 0.2,
        ease: 'easeOut',
      },
    }),
  }

  const isGridLayout = heading?.type === 'grid'

  return (
    <StylesSection styles={styles || {}}>
      <section ref={newsRef} className="flex flex-col gap-8 md:gap-12">
        {/* Heading Section */}
        {heading && (
          <HeadingSection
            heading={heading}
            buttonsClassName="items-center"
            isDisabledIcon={false}
            // fallbackHeading="TIN TỨC & SỰ KIỆN"
          />
        )}

        {/* News Grid */}
        {notFound ? (
          <EmptyContent title={t`No data`} />
        ) : (
          <div
            className={cn(
              'grid gap-6',
              isGridLayout
                ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3'
                : 'grid-cols-1 md:grid-cols-3',
            )}
          >
            {news?.slice(0, 3).map((item, index) => {
              const category = item.category?.title || 'Tin tức'
              const displayDate = getNewsDisplayDate(item)

              return (
                <motion.div
                  key={item.id}
                  initial="hidden"
                  animate={isNewsInView ? 'visible' : 'hidden'}
                  variants={fadeInUp as any}
                  custom={index}
                >
                  <PostCardLarge
                    type={ECardType.News}
                    id={item.id.toString()}
                    slug={item.slug}
                    title={item.title}
                    coverImage={item.image}
                    excerpt={
                      item.description || 'Tin tức mới nhất từ chúng tôi'
                    }
                    topic={category}
                    publishedAt={displayDate}
                    status={EBlogStatus.PUBLISHED}
                  />
                </motion.div>
              )
            })}
          </div>
        )}
      </section>
    </StylesSection>
  )
}

export default LatestNews
