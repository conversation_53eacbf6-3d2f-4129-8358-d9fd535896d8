'use client'

import { useParams } from 'next/navigation'
import { useCallback, useEffect, useState } from 'react'
import fetchContentTypeClient from '~/src/libs/cms/strapi/fetchContentTypeClient'
import { apiRoute } from '~/src/libs/constants/cms'
import { useLocale } from '~/src/libs/data/use-locale'
import MediaGallery from '../media-gallery'
import HeadingSection from '../renderer/heading-section'
import StylesSection from '../renderer/styles-section'
//----------------------------------------------------------------------------------

type TProps = {
  styles: any
  heading: any
}
//----------------------------------------------------------------------------------

export default function ProductMediaGallery({ styles, heading }: TProps) {
  const { slug } = useParams()
  const locale = useLocale()

  const [productData, setProductData] = useState<any>(null)

  const fetchProductData = useCallback(async () => {
    const data = await fetchContentTypeClient({
      contentType: apiRoute.products,
      params: {
        filters: {
          slug,
          locale,
        },
      },
    })
    setProductData(data?.data?.[0])
  }, [locale, slug])

  useEffect(() => {
    fetchProductData()
  }, [fetchProductData])

  const renderContent = (
    <div className="flex flex-col gap-y-10">
      {heading ? <HeadingSection heading={heading} /> : null}
      <MediaGallery
        galleryData={productData?.media_gallery}
        content_max_width={styles?.content_max_width}
      />
    </div>
  )

  return <StylesSection styles={styles}>{renderContent}</StylesSection>
}
