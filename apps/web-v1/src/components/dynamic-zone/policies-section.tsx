'use client'

import { MainButton } from '@ttplatform/core-page-builder/components'
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  Typography,
} from '@ttplatform/ui/components'
import Autoplay from 'embla-carousel-autoplay'
import { useRef } from 'react'
import { useLinguiClient } from '~/src/localization/i18n-client'
import { RenderImageUrlStrapi } from '../render-imageUrl-strapi'
import HeadingSection from '../renderer/heading-section'
import StylesSection from '../renderer/styles-section'
//----------------------------------------------------------------------------------

type TProps = {
  styles: any
  heading: any
  policies: any
}
//----------------------------------------------------------------------------------

export default function PoliciesSection({ styles, heading, policies }: TProps) {
  const plugin = useRef(Autoplay({ delay: 5000, stopOnInteraction: true }))

  const renderContent = (
    <div className="flex flex-col gap-y-10">
      {heading ? <HeadingSection heading={heading} /> : null}
      <div>
        <Carousel
          // plugins={[plugin.current]}
          opts={{
            align: 'start',
            loop: true,
          }}
          className="w-full relative"
        >
          <CarouselContent>
            {policies?.map((item: any, idx: number) => (
              <CarouselItem
                key={idx}
                className="basis-7/8 sm:basis-2/3 md:basis-1/2"
              >
                <CardPolicy item={item} />
              </CarouselItem>
            ))}
          </CarouselContent>
        </Carousel>
      </div>
    </div>
  )

  return <StylesSection styles={styles}>{renderContent}</StylesSection>
}

function CardPolicy({ item }: { item: any }) {
  const i18n = useLinguiClient()
  return (
    <div
      className="relative w-full h-auto aspect-[4/3] flex items-end rounded-lg overflow-hidden group"
      style={{
        backgroundImage: `url(${RenderImageUrlStrapi({ url: item?.image?.url })})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat',
      }}
    >
      <div
        className="flex flex-col justify-end p-4 md:p-8 rounded-xl z-50"
        style={{}}
      >
        <Typography variant="h3" className="text-white font-bold ">
          {item?.title}
          <span className="mt-1 md:mt-2 block sm:hidden group-hover:block w-[55px] md:w-[68px] h-[5px] md:h-[8px] rounded-none  bg-gradient-to-r from-[#FFCC00] via-[#FFD326] to-[#FFDE59]" />
        </Typography>
        <div className="sm:overflow-hidden">
          <div className="my-4 block sm:opacity-0 sm:h-0 sm:my-0 sm:group-hover:opacity-100 sm:group-hover:h-auto sm:group-hover:my-6 transition-all duration-300 text-white">
            <Typography
              variant="body2"
              className="line-clamp-2 sm:line-clamp-3 text-white"
            >
              {item?.excerpt}
            </Typography>
          </div>
          <div className="block sm:opacity-0 sm:h-0 sm:group-hover:opacity-100 sm:group-hover:h-auto transition-all duration-300">
            <MainButton
              label={i18n._('learn more')}
              variant="primary"
              url={'#'}
            />
          </div>
        </div>
      </div>
      <div
        className="absolute w-full h-full sm:h-[50%] bottom-0 left-0 z-20"
        style={{
          background:
            'linear-gradient(0deg, #222 0%, rgba(34, 34, 34, 0.00) 100%)',
        }}
      />
    </div>
  )
}
