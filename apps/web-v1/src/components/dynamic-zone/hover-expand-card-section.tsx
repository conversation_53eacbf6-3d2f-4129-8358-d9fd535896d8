'use client'
import { ExpandCardSlider } from '@ttplatform/core-page-builder/components'
import { useRef } from 'react'
import HeadingSection from '../renderer/heading-section'
import StylesSection from '../renderer/styles-section'

const HoverExpandCardSection = ({
  heading,
  items,
  styles,
}: {
  heading: any
  items: any
  styles: any
  locale?: string
}) => {
  const contentRef = useRef<HTMLDivElement>(null)

  const renderContent = (
    <div className="flex flex-col gap-y-20" ref={contentRef}>
      <HeadingSection heading={heading} isDisabledIcon={false}></HeadingSection>
      <ExpandCardSlider slides={items} />
    </div>
  )
  return <StylesSection styles={styles}>{renderContent}</StylesSection>
}

export default HoverExpandCardSection
