import { Typography } from '@ttplatform/ui/components'
// import { useParams } from 'next/navigation';
import { useParams } from 'next/navigation'
import { useGetPartModels } from '~/src/libs/cms/strapi/use-parts'
import { useLocale } from '~/src/libs/data/use-locale'
import HeadingSection from '../renderer/heading-section'
import StylesSection from '../renderer/styles-section'
//----------------------------------------------------------------------------------

type TProps = {
  styles: any
  heading: any
}
//----------------------------------------------------------------------------------
export default function PartSpecifications({ styles, heading }: TProps) {
  const params = useParams()

  const locale = useLocale()

  const { data: partSpecifications } = useGetPartModels({
    filters: {
      slug: params.partModelSlug,
    },
    locale,
  })

  const renderPartSpecifications = (
    <div className="w-full flex flex-col">
      {partSpecifications?.data?.[0]?.part_specification_values?.map(
        (item: any) => (
          <div
            key={item.id}
            className="w-full flex justify-between gap-x-4 py-4 px-8"
          >
            <Typography variant="body1" weight="medium" color="gray-800">
              {item?.product_specification?.name}
            </Typography>
            <Typography
              variant="body2"
              weight="normal"
              color="gray-800"
              className="text-right"
            >
              {item?.value}/{item?.value_in_us}
            </Typography>
          </div>
        ),
      )}
    </div>
  )
  const renderContent = (
    <div className="flex flex-col gap-y-10">
      {heading ? <HeadingSection heading={heading} /> : null}
      {renderPartSpecifications}
    </div>
  )
  return <StylesSection styles={styles}>{renderContent}</StylesSection>
}
