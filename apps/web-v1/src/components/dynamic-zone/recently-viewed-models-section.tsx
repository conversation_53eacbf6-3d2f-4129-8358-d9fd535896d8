'use client'

import { EmptyContent } from '@ttplatform/ui/templates'
import { Loader2 } from 'lucide-react'
import { useMemo } from 'react'
import { KEYS } from '~/src/config-global'
import { useGetProductModels } from '~/src/libs/cms/strapi/use-products'
import { getCookieClient } from '~/src/libs/data/cookies-client'
import { useLocale } from '~/src/libs/data/use-locale'
import { useLinguiClient } from '~/src/localization/i18n-client'
import { CardProduct } from '../card/card-product'
import HeadingSection from '../renderer/heading-section'

type TProps = {
  styles?: any
  heading?: any
  pagination?: any
}

const { MODEL_VIEWED } = KEYS

const RecentlyViewedProductsSection = ({ heading, pagination }: TProps) => {
  const locale = useLocale()
  const i18n = useLinguiClient()

  const recentlyViewedProducts = JSON.parse(
    getCookieClient(MODEL_VIEWED) || '[]',
  )

  const {
    data: productData,
    isLoading,
    error,
  } = useGetProductModels({
    filters: { locale, documentId: { $in: recentlyViewedProducts } },
    populate: {
      product_specification_values: {
        populate: ['product_specification'],
      },
      images: {
        populate: '*',
      },
    },
    pagination: {
      limit: pagination?.limit || 3,
    },
  })

  const reOrderData = useMemo(() => {
    const data = productData?.data || []
    const reOrderData = recentlyViewedProducts
      .map((item: any) =>
        data.find((product: any) => product.documentId === item),
      )
      .filter(Boolean)
    return reOrderData
  }, [productData, recentlyViewedProducts])
  // console.log("🚀 ~ reOrderData ~ reOrderData:", reOrderData)

  const notFound =
    !isLoading &&
    (!productData || !productData.data.length || !reOrderData?.length || error)

  const renderContent = isLoading ? (
    <Loader2 className="w-10 h-10 animate-spin mx-auto" />
  ) : reOrderData && reOrderData.length > 0 ? (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {reOrderData.map((product: any, index: number) => (
        <CardProduct key={index} product={product} />
      ))}
    </div>
  ) : null

  if (notFound) return null

  return (
    <div className="w-full mx-auto container px-4 pb-10 min-[1440px]:max-w-7xl 3xl:max-w-[1600px]">
      <div className="flex flex-col gap-y-10">
        {heading && (
          <HeadingSection underlineBackground="#FFCC00" heading={heading} />
        )}

        {renderContent}

        {notFound && <EmptyContent title={i18n._('No data')} />}
      </div>
    </div>
  )
}

export default RecentlyViewedProductsSection
