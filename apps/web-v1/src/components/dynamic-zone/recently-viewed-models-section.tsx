'use client'
import { t } from '@lingui/core/macro'
import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from '@ttplatform/ui/components'
import { EmptyContent } from '@ttplatform/ui/templates'
import Cookies from 'js-cookie'
import { Loader2 } from 'lucide-react'
import { useMemo } from 'react'
import { KEYS } from '~/src/config-global'
import { useGetProductModels } from '~/src/libs/cms/strapi/use-products'
import { useLocale } from '~/src/libs/data/use-locale'
import { CardProduct } from '../card/card-product'
import HeadingSection from '../renderer/heading-section'

type TProps = {
  styles?: any
  heading?: any
  pagination?: any
}

const { MODEL_VIEWED_LIMIT, MODEL_VIEWED } = KEYS

const RecentlyViewedProductsSection = ({ heading, pagination }: TProps) => {
  if (pagination?.limit) {
    Cookies.set(MODEL_VIEWED_LIMIT, JSON.stringify(pagination.limit))
  }
  const locale = useLocale()

  const recentlyViewedProducts = JSON.parse(
    Cookies.get(MODEL_VIEWED) || '[]',
  )

  const {
    data: productData,
    isLoading,
    error,
  } = useGetProductModels({
    filters: { locale, documentId: { $in: recentlyViewedProducts } },
    populate: '*',
    pagination: {
      limit: pagination?.limit || 3,
    },
  })

  const notFound =
    !isLoading && (!productData || !productData.data.length || error)

  const reOrderData = useMemo(() => {
    const data = productData?.data || []
    const reOrderData = recentlyViewedProducts
      .map((item: any) =>
        data.find((product: any) => product.documentId === item),
      )
      .filter(Boolean)
    return reOrderData
  }, [productData, recentlyViewedProducts])

  const renderContent = (
    <div className="flex flex-col gap-y-10">
      {heading ? (
        <HeadingSection underlineBackground="#FFCC00" heading={heading} />
      ) : null}
      {isLoading ? (
        <Loader2 className="w-10 h-10 animate-spin mx-auto" />
      ) : notFound ? (
        <EmptyContent title={t`No data`} />
      ) : reOrderData && reOrderData.length > 0 ? (
        <Carousel>
          <CarouselContent className="w-full">
            {reOrderData.map((product: any) => (
              <CarouselItem
                key={product.id}
                className="md:basis-1/2 lg:basis-1/3 pl-6"
              >
                <CardProduct product={product} />
              </CarouselItem>
            ))}
          </CarouselContent>
        </Carousel>
      ) : null}
    </div>
  )
  return (
    <div className="w-full mx-auto container px-4 pb-10 min-[1440px]:max-w-7xl 3xl:max-w-[1600px]">
      {renderContent}
    </div>
  )
}

export default RecentlyViewedProductsSection
