import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from '@ttplatform/ui/components'
import Autoplay from 'embla-carousel-autoplay'
import { useRef } from 'react'
import { CardPart } from '../card/card-part'
import HeadingSection from '../renderer/heading-section'
import StylesSection from '../renderer/styles-section'
//----------------------------------------------------------------------------------

type TProps = {
  styles: any
  heading: any
  part_models: any
}
//----------------------------------------------------------------------------------
export default function RelatedParts({ styles, heading, part_models }: TProps) {
  const plugin = useRef(Autoplay({ delay: 5000, stopOnInteraction: true }))

  const renderContent = (
    <div className="flex flex-col gap-y-10">
      {heading ? <HeadingSection heading={heading} /> : null}
      <div>
        <Carousel
          plugins={[plugin.current]}
          opts={{
            align: 'start',
            loop: true,
          }}
          className="w-full relative"
        >
          <CarouselContent>
            {part_models?.map((item: any, idx: number) => (
              <CarouselItem
                key={idx}
                className="basis-4/5 sm:basis-2/5 md:basis-1/3 lg:basis-1/4"
              >
                <CardPart item={item} />
              </CarouselItem>
            ))}
          </CarouselContent>
        </Carousel>
      </div>
    </div>
  )
  return <StylesSection styles={styles}>{renderContent}</StylesSection>
}
