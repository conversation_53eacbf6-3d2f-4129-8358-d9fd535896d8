import { MainButton } from '@ttplatform/core-page-builder/components'
import { Typography } from '@ttplatform/ui/components'
import Link from 'next/link'
import { useParams } from 'next/navigation'
import { RenderImageUrlStrapi } from '../render-imageUrl-strapi'

//---------------------------------------------------------------------------------
type CardProps = {
  item: any
}
//---------------------------------------------------------------------------------
export function CardPart({ item }: CardProps) {
  const params = useParams()

  return (
    <Link href={`/parts/${params?.categorySlug}/${item?.slug}`}>
      <div
        className="relative h-full flex flex-col bg-gray-50 rounded-md border border-gray-200 overflow-hidden group hover:shadow-none "
        style={{
          boxShadow:
            '0px 4px 8px -2px rgba(16, 24, 40, 0.10), 0px 2px 4px -2px rgba(16, 24, 40, 0.06)',
        }}
      >
        <div className="w-full h-auto aspect-square overflow-hidden">
          <div
            className=" w-full h-full group-hover:scale-110 ease-in-out transition-all duration-500"
            style={{
              backgroundImage: `url(${RenderImageUrlStrapi({ url: item?.image?.url })})`,
              backgroundRepeat: 'no-repeat',
              backgroundSize: 'cover',
              backgroundPosition: 'center',
            }}
          ></div>
        </div>
        <div
          className="px-6 py-2"
          style={{
            background:
              'linear-gradient(90deg, rgba(255, 204, 0, 0.95) 70%, rgba(255, 204, 0, 0.85) 90%, rgba(255, 204, 0, 0.65) 100%)',
          }}
        >
          <div className="flex items-center justify-between gap-2">
            <Typography
              variant={'h6'}
              className="text-gray-800 uppercase line-clamp-2 font-semibold"
            >
              {item?.name}: {item?.part?.name}
            </Typography>
            <MainButton variant="secondary" />
          </div>
        </div>
        <div className="px-6 pt-4 pb-6">
          <Typography variant={'body2'} className="text-gray-700 line-clamp-3">
            {item?.description}
          </Typography>
        </div>
      </div>
    </Link>
  )
}
