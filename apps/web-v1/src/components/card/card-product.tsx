import { MainButton } from '@ttplatform/core-page-builder/components'
import { Checkbox, Typography } from '@ttplatform/ui/components'
import { RenderImageUrlStrapi } from '../render-imageUrl-strapi'

//---------------------------------------------------------------------------------
type CardProps = {
  product: any
}
//---------------------------------------------------------------------------------
export function CardProduct({ product }: CardProps) {
  return (
    <div
      className="relative flex flex-col bg-gray-50 rounded-lg overflow-hidden group hover:shadow-none"
      style={{
        boxShadow:
          '0px 4px 8px -2px rgba(16, 24, 40, 0.10), 0px 2px 4px -2px rgba(16, 24, 40, 0.06)',
      }}
    >
      <div className="p-4 absolute z-30 top-0 left-0">
        <div
          className="p-2 w-[36px] flex items-center gap-2 bg-white rounded-sm group-hover:w-[95px] transition-all duration-500 overflow-hidden"
          style={{
            boxShadow:
              '0px 1px 3px 0px rgba(16, 24, 40, 0.10), 0px 1px 2px 0px rgba(16, 24, 40, 0.06)',
          }}
        >
          <Checkbox className="data-[state=checked]:text-black w-5 h-5 min-w-5 min-h-5" />
          <Typography
            variant={'caption'}
            className="text-gray-800 text-nowrap font-medium"
          >
            So sánh
          </Typography>
        </div>
      </div>
      <div className="relative z-10 w-full h-auto aspect-[4/3] flex items-end overflow-hidden ">
        <div
          className="absolute z-20 w-full h-full group-hover:scale-110 ease-in-out transition-all duration-500"
          style={{
            backgroundImage: `url(${RenderImageUrlStrapi({ url: product?.image?.url })})`,
            backgroundRepeat: 'no-repeat',
            backgroundSize: 'cover',
            backgroundPosition: 'center',
          }}
        ></div>
        <div
          className="absolute bottom-0 left-0 z-50 w-full h-auto px-6 py-4"
          style={{
            background:
              'linear-gradient(90deg, rgba(255, 204, 0, 0.95) 70%, rgba(255, 204, 0, 0.85) 90%, rgba(255, 204, 0, 0.65) 100%)',
          }}
        >
          <div className="flex items-center justify-between">
            <Typography
              variant={'h6'}
              className="text-gray-800 uppercase line-clamp-1 font-semibold"
            >
              {product?.name}
            </Typography>
            <MainButton variant="secondary" />
          </div>
          {/* <div className="flex h-[52px] w-full">
            <div className="w-full px-6 h-full bg-[#FFCC00]/95 flex items-center">
              
            </div>
            <div className="h-full w-[120px] bg-[#FFCC00]/85"></div>
            <Link href={`/product/${product?.slug}`}>
              <div className="w-auto h-full px-2 sm:px-4 md:px-6 flex items-center  bg-[#FFCC00]/65">
               
              </div>
            </Link>
          </div> */}
        </div>
      </div>
      <div className="px-6 py-4 flex flex-col gap-4">
        <ItemDetails
          title="Công suất hiệu dụng"
          description={product?.cong_suat}
        />
        <ItemDetails
          title="Dung tích gầu"
          description={product?.dung_tich_gau}
        />
        <ItemDetails
          title="Khối lượng vận hành"
          description={product?.khoi_luong_van_hanh}
        />
      </div>
    </div>
  )
}
type ItemDetailsProps = {
  title: string
  description: string
}
function ItemDetails({ title, description }: ItemDetailsProps) {
  return (
    <div className="flex items-center justify-between">
      <Typography
        variant={'body2'}
        className="text-gray-800 font-bold line-clamp-2"
      >
        {title}
      </Typography>
      <Typography variant={'body2'} className="text-gray-800">
        {description}
      </Typography>
    </div>
  )
}
