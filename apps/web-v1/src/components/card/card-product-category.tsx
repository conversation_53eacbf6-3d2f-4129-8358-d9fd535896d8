import { CmsMedia, MainButton } from '@ttplatform/core-page-builder/components'
import { Typography } from '@ttplatform/ui/components'
import Link from 'next/link'
//---------------------------------------------------------------------------------
type CardProps = {
  category: any
}
//---------------------------------------------------------------------------------
export function CardProductCategory({ category }: CardProps) {
  const { image, name, excerpt } = category || {}
  return (
    <Link href={'#'}>
      <div
        className="flex flex-col bg-gray-50 rounded-lg overflow-hidden group hover:shadow-none min-h-[374px]"
        style={{
          boxShadow:
            '0px 4px 8px -2px rgba(16, 24, 40, 0.10), 0px 2px 4px -2px rgba(16, 24, 40, 0.06)',
        }}
      >
        <div className="relative w-full h-auto aspect-[9/6] overflow-hidden ">
          <CmsMedia
            media={image}
            width={0}
            height={0}
            sizes="100vw"
            className="w-full h-full object-cover"
          />
          <div
            className="absolute w-full h-auto py-4 px-6 bottom-0 left-0 flex items-center justify-between gap-4"
            style={{
              background:
                'linear-gradient(90deg,rgba(255, 204, 0, 1) 0%, rgba(255, 221, 0, 0.85) 60%, rgba(255, 221, 0, 0.85) 80%,rgba(255, 221, 0, 0.65) 100%)',
            }}
          >
            <Typography
              variant={'h6'}
              className="text-gray-800 uppercase font-semibold line-clamp-2"
            >
              {name}
            </Typography>
            <MainButton variant="secondary" />
          </div>
        </div>
        <div className="px-6 py-4">
          <Typography variant={'body2'} className="text-gray-700 line-clamp-3">
            {excerpt}
          </Typography>
        </div>
      </div>
    </Link>
  )
}
