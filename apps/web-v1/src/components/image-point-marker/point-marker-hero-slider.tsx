'use client'

import { useEffect, useState } from 'react'

import { CmsMedia, MainButton } from '@ttplatform/core-page-builder/components'
import { TImagePointMarkersSchema } from '@ttplatform/core-page-builder/libs'
import {
  Checkbox,
  Popover,
  PopoverContent,
  PopoverPortal,
  PopoverTrigger,
  Typography,
} from '@ttplatform/ui/components'
import { cn } from '@ttplatform/ui/lib'
import Image from 'next/image'
import React from 'react'

type Point = { x: number; y: number }
type ZoomedPoint = {
  top: string
  left: string
  xPosition: number
  yPosition: number
}

const getPointPosition = (
  point: Point,
  originalWidth: number,
  currentWidth: number,
  originalHeight: number,
  currentHeight: number,
): ZoomedPoint => {
  // Calculate absolute pixel positions based on percentages
  const absoluteX = (originalWidth * point.x) / 100
  const absoluteY = (originalHeight * point.y) / 100

  // Calculate zoom ratio between original and current dimensions
  const zoomRatioX = currentWidth / originalWidth
  const zoomRatioY = currentHeight / originalHeight

  // Apply zoom to get new pixel positions
  const zoomedX = absoluteX * zoomRatioX
  const zoomedY = absoluteY * zoomRatioY

  // Convert back to percentages relative to current dimensions
  return {
    left: `${(zoomedX / currentWidth) * 100}%`,
    top: `${(zoomedY / currentHeight) * 100}%`,
    xPosition: (zoomedX / currentWidth) * 100,
    yPosition: (zoomedY / currentHeight) * 100,
  }
}

type TProps = {
  point_markers: TImagePointMarkersSchema
  children: React.ReactNode
}

// Helper to get highest-priority data
function getPriorityData(point: any, card: any) {
  // 1. link_product or link_product_part
  const product = card?.link_product || card?.link_product_part || null
  if (product) {
    return {
      image: product.image || card?.image || point.image,
      title: product.name || card?.name || point.title,
      content:
        product.description ||
        (card?.content && card.content.length > 0
          ? card.content
              .map((block: any) =>
                block.children?.map((child: any) => child.text).join(''),
              )
              .join(' ')
          : null) ||
        point.content,
      content_items: card?.content_items || [],
      button: card?.button || point,
      product_category: product.product_category,
      product,
    }
  }
  // 2. pointer_card
  if (card) {
    return {
      image: card.image || point.image,
      title: card.name || point.title,
      content:
        (card.content && card.content.length > 0
          ? card.content
              .map((block: any) =>
                block.children?.map((child: any) => child.text).join(''),
              )
              .join(' ')
          : null) || point.content,
      content_items: card.content_items || [],
      button: card.button || point,
      product_category: card.product_category,
      product: null,
    }
  }
  // 3. point
  return {
    image: point.image,
    title: point.title,
    content: point.content,
    content_items: [],
    button: point,
    product_category: null,
    product: null,
  }
}

const PointMarkerHeroSlider = ({ point_markers, children }: TProps) => {
  const [viewWidth, setViewWidth] = useState(0)
  const { points, pointer_cards } = point_markers || {}

  const bgUrl = points?.imageUrl || ''

  const markupWidth = points?.markupWidth || 0
  const markupHeight = points?.markupHeight || 0

  useEffect(() => {
    const handleResize = () => {
      setViewWidth(window.innerWidth)
    }

    // Initial calculation
    handleResize()

    // Add resize event listener
    window.addEventListener('resize', handleResize)

    // Cleanup
    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [])

  const renderBackground = (
    <div className="w-full h-full bg-cover bg-center z-10">
      <Image
        src={bgUrl}
        alt="bg"
        fill
        className="w-full h-full object-cover"
        sizes="100vw"
        priority
      />
    </div>
  )

  const renderPopupContent = (point: any, card: any, pointPosition: any) => {
    const data = getPriorityData(point, card)

    // Determine best side based on point position
    const getSide = () => {
      if (pointPosition.yPosition < 30) return 'bottom' // Top area
      if (pointPosition.yPosition > 70) return 'top' // Bottom area
      if (pointPosition.xPosition < 30) return 'right' // Left area
      if (pointPosition.xPosition > 70) return 'left' // Right area
      return 'top' // Default
    }

    const getAlign = () => {
      if (pointPosition.xPosition < 20) return 'start'
      if (pointPosition.xPosition > 80) return 'end'
      return 'center'
    }

    return (
      <PopoverContent
        className="w-72 sm:w-80 md:w-88 3xl:w-96 p-0 shadow-lg max-w-[90vw]"
        sideOffset={8}
        align={getAlign()}
        side={getSide()}
        avoidCollisions={true}
        collisionPadding={20}
      >
        {/* Product Image Header */}
        {data.image && (
          <div className="relative group hover:shadow-none">
            <div className="w-full h-40 sm:h-48 md:h-56 3xl:h-64 overflow-hidden rounded-lg">
              <CmsMedia
                media={data.image}
                className="w-full h-full object-cover"
                hoverAnimation={false}
              />
            </div>
            <div className="p-2 sm:p-4 absolute z-30 top-0 left-0 hidden sm:block">
              <div
                className="p-2 w-[36px] flex items-center gap-2 bg-white rounded-sm group-hover:w-[95px] transition-all duration-500 overflow-hidden"
                style={{
                  boxShadow:
                    '0px 1px 3px 0px rgba(16, 24, 40, 0.10), 0px 1px 2px 0px rgba(16, 24, 40, 0.06)',
                }}
              >
                <Checkbox className="data-[state=checked]:text-black w-5 h-5 min-w-5 min-h-5" />
                <Typography
                  variant={'caption'}
                  className="text-gray-800 text-nowrap font-medium"
                >
                  So sánh
                </Typography>
              </div>
            </div>
            {/* Product title overlay */}
            <div
              className="absolute bottom-0 left-0 z-50 w-full h-auto px-2 py-1.5 sm:px-4 sm:py-2 3xl:px-6 3xl:py-4"
              style={{
                background:
                  'linear-gradient(90deg, rgba(255, 204, 0, 0.95) 70%, rgba(255, 204, 0, 0.85) 90%, rgba(255, 204, 0, 0.65) 100%)',
              }}
            >
              <div className="flex items-center justify-between flex-wrap gap-1 sm:gap-2">
                <Typography
                  variant="h6"
                  className="text-gray-800 uppercase line-clamp-1 font-semibold text-sm sm:text-base"
                >
                  {data.title}
                </Typography>

                <MainButton
                  variant={data.button?.variant || 'secondary'}
                  label={data.button?.text}
                  url={data.button?.url}
                  openInNewTab={data.button?.target}
                />
              </div>
            </div>
          </div>
        )}
        {/* Content */}
        <div className="px-2 py-1.5 sm:px-4 sm:py-2 3xl:px-6 3xl:py-4 space-y-1.5 sm:space-y-2 w-full">
          {data.content && (
            <Typography
              variant="body2"
              className="text-black text-xs sm:text-sm line-clamp-3"
            >
              {data.content}
            </Typography>
          )}
          {/* Equipment Specifications */}
          {data.content_items && data.content_items.length > 0 && (
            <div className="space-y-1.5 sm:space-y-2">
              {data.content_items
                .slice(0, 4)
                .map((item: any, index: number) => (
                  <div
                    key={index}
                    className="flex items-center justify-between gap-2"
                  >
                    <div className="flex items-center gap-1.5 sm:gap-2 min-w-0 flex-1">
                      {item.icon && (
                        <div className="w-3 h-3 sm:w-4 sm:h-4 flex-shrink-0">
                          <CmsMedia
                            media={item.icon}
                            className="w-3 h-3 sm:w-4 sm:h-4 object-contain"
                          />
                        </div>
                      )}
                      <Typography
                        variant="body2"
                        className="text-xs sm:text-sm font-bold truncate"
                      >
                        {item.title}:
                      </Typography>
                    </div>
                    <Typography
                      variant="body2"
                      className="text-xs sm:text-sm font-normal text-right flex-shrink-0"
                    >
                      {item.sub_title}
                    </Typography>
                  </div>
                ))}
            </div>
          )}
        </div>
      </PopoverContent>
    )
  }

  const renderPoints = points?.points?.map((point: any, idx: number) => {
    // Use container dimensions for point positioning
    const containerWidth = viewWidth || window.innerWidth
    const containerHeight = window.innerHeight // Use full viewport height

    const pointPosition = getPointPosition(
      point,
      markupWidth,
      containerWidth,
      markupHeight,
      containerHeight,
    )
    const card =
      pointer_cards?.find((c) => c.card_id === point.id) ||
      (point._index !== undefined &&
      pointer_cards &&
      point._index < pointer_cards.length
        ? pointer_cards[point._index]
        : pointer_cards?.[0])

    return (
      <React.Fragment key={idx}>
        <div
          className="absolute z-50"
          style={{
            top: pointPosition.top,
            left: pointPosition.left,
            transform: 'translate(-50%, -50%)',
          }}
        >
          <Popover>
            <PopoverTrigger asChild>
              <div
                className={cn(
                  'w-5 h-5 point-marker cursor-pointer transition-all duration-200 relative',
                  'hover:scale-110',
                )}
                style={{
                  clipPath:
                    'polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%)',
                }}
              >
                <div
                  className="absolute inset-0 bg-white animate-ping opacity-70 group-hover:opacity-100 transition-opacity duration-1000"
                  style={{ clipPath: 'inherit' }}
                />
                <div
                  className="absolute inset-1 bg-white"
                  style={{ clipPath: 'inherit' }}
                />
              </div>
            </PopoverTrigger>
            <PopoverPortal>
              {renderPopupContent(point, card, pointPosition)}
            </PopoverPortal>
          </Popover>
        </div>
      </React.Fragment>
    )
  })

  return (
    <div className="relative w-full h-full">
      {bgUrl && renderBackground}

      {renderPoints}

      <div className="absolute inset-0 w-full h-full z-10">{children}</div>
    </div>
  )
}

export default PointMarkerHeroSlider
