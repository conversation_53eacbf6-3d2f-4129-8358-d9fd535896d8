'use client'

import Image from 'next/image'
import Link from 'next/link'
import { useEffect, useState } from 'react'

import { APP_CONFIG } from '@/config-global'
import { type SearchResult, useSearch } from '@/hooks/use-search'
import { useBoolean } from '@/libs/hooks/use-boolean'
import {
  Badge,
  Button,
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  Input,
  ScrollArea,
} from '@ttplatform/ui/components'
import { ExternalLink, Loader2, Search } from 'lucide-react'

import { Trans } from '@lingui/react'
import { useLinguiClient } from '~/src/localization/i18n-client'

export function SearchBar() {
  const i18n = useLinguiClient()
  const openDialog = useBoolean()
  const { search, isLoading, error, isReady } = useSearch()
  const [query, setQuery] = useState('')
  const [results, setResults] = useState<SearchResult[]>([])
  const [isSearching, setIsSearching] = useState(false)

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
        event.preventDefault()
        openDialog.onTrue()
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [openDialog.onTrue, openDialog])

  // Handle search with debouncing
  useEffect(() => {
    if (!isReady || !query.trim()) {
      setResults([])
      setIsSearching(false)
      return
    }

    setIsSearching(true)
    const timeoutId = setTimeout(() => {
      const searchResults = search(query)
      setResults(searchResults.slice(0, 10)) // Limit to 10 results
      setIsSearching(false)
    }, 300)

    return () => clearTimeout(timeoutId)
  }, [query, search, isReady])

  const handleResultClick = () => {
    openDialog.onFalse()
    setQuery('')
    setResults([])
  }

  const isMac =
    typeof navigator !== 'undefined' &&
    /Mac|iPhone|iPad|iPod/i.test(navigator.userAgent)
  const modifierKey = isMac ? '⌘' : 'Ctrl'

  const getTypeColor = (type: string) => {
    const colors: Record<string, string> = {
      PRODUCTS: 'bg-blue-100 text-blue-800',
      NEWS: 'bg-green-100 text-green-800',
      CAREERS: 'bg-purple-100 text-purple-800',
      SERVICES: 'bg-orange-100 text-orange-800',
      'SUPPORT-CENTERS': 'bg-red-100 text-red-800',
      PAGES: 'bg-gray-100 text-gray-800',
    }
    return colors[type] || 'bg-gray-100 text-gray-800'
  }

  const highlightText = (text: string, searchQuery: string) => {
    if (!searchQuery.trim() || !text) return text

    try {
      const escapedQuery = searchQuery.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
      const regex = new RegExp(`(${escapedQuery})`, 'gi')

      const highlightedText = text.replace(
        regex,
        '<mark style="background-color: #fef08a; color: #111827; font-weight: 600; padding: 2px 4px; border-radius: 4px;">$1</mark>',
      )

      return <span dangerouslySetInnerHTML={{ __html: highlightedText }} />
    } catch (error) {
      console.error('Highlight error:', error)
      return text
    }
  }

  return (
    <div className="z-10">
      <Button
        variant="text"
        size="icon"
        onClick={openDialog.onTrue}
        className="transition-all duration-300 hover:bg-primary rounded-full"
      >
        <Search className="h-8 w-8" />
      </Button>

      <Dialog open={openDialog.value} onOpenChange={openDialog.onFalse}>
        <DialogContent className="max-w-2xl max-h-[80vh]">
          <DialogHeader>
            <DialogTitle>{i18n._('Search')}</DialogTitle>
            <DialogDescription>
              {i18n._(
                "Enter your search query below to find what you're looking for.",
              )}
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="relative">
              <Input
                type="search"
                placeholder={i18n._(
                  'Search products, news, promotions, careers...',
                )}
                className="pl-10 pr-4"
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                autoFocus
              />
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              {isSearching && (
                <Loader2 className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 animate-spin text-gray-400" />
              )}
            </div>

            {/* Loading state */}
            {isLoading && (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="h-6 w-6 animate-spin" />
                <span className="ml-2">{i18n._('Loading search data...')}</span>
              </div>
            )}

            {/* Error state */}
            {error && (
              <div className="text-center py-8 text-red-500">
                <p>{i18n._('Failed to load search data')}</p>
                <p className="text-sm text-gray-500 mt-1">{error}</p>
              </div>
            )}

            {/* Search results */}
            {isReady && query && (
              <ScrollArea className="max-h-96">
                {results.length > 0 ? (
                  <div className="space-y-2">
                    {results.map((result, index) => (
                      <Link
                        key={`${result.item.documentId}-${index}`}
                        href={result.item.link}
                        onClick={handleResultClick}
                        className="block p-3 rounded-lg border hover:bg-gray-50 transition-colors"
                      >
                        <div className="flex items-start gap-3">
                          {result.item.thumbnail && (
                            <div className="flex-shrink-0 w-12 h-12 rounded overflow-hidden bg-gray-100">
                              <Image
                                src={
                                  result.item.thumbnail.startsWith('http')
                                    ? result.item.thumbnail
                                    : `${APP_CONFIG.apiUrl}${result.item.thumbnail}`
                                }
                                alt={result.item.name}
                                width={48}
                                height={48}
                                className="w-full h-full object-cover"
                                unoptimized
                              />
                            </div>
                          )}

                          <div className="flex-1 min-w-0">
                            <div className="flex items-start justify-between gap-2 mb-1">
                              <h4 className="font-medium text-sm line-clamp-1">
                                {highlightText(result.item.name, query)}
                              </h4>
                              <Badge
                                variant="secondary"
                                className={`text-xs ${getTypeColor(result.item.type)}`}
                              >
                                {result.item.type}
                              </Badge>
                            </div>

                            {result.item.description && (
                              <p className="text-sm text-gray-600 line-clamp-2">
                                {highlightText(
                                  result.item.description.length > 100
                                    ? `${result.item.description.substring(0, 100)}...`
                                    : result.item.description,
                                  query,
                                )}
                              </p>
                            )}

                            {result.item.category && (
                              <p className="text-xs text-gray-500 mt-1">
                                Category: {result.item.category}
                              </p>
                            )}
                          </div>

                          <ExternalLink className="h-4 w-4 text-gray-400 flex-shrink-0" />
                        </div>
                      </Link>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <Search className="h-12 w-12 mx-auto mb-2 text-gray-300" />
                    <p>
                      <Trans
                        id="No results found for &quot;{query}&quot;"
                        values={{ query }}
                      />
                    </p>
                    <p className="text-sm mt-1">
                      {i18n._('Try different keywords or check spelling')}
                    </p>
                  </div>
                )}
              </ScrollArea>
            )}

            {/* Instructions */}
            {!query && isReady && (
              <div className="text-center py-8 text-gray-500">
                <Search className="h-12 w-12 mx-auto mb-2 text-gray-300" />
                <p>{i18n._('Start typing to search across the website')}</p>
                <p className="text-sm mt-1">
                  {i18n._(
                    'Products, news, promotions, careers, services and more...',
                  )}
                </p>
              </div>
            )}

            <div className="text-sm text-gray-500 text-center">
              {i18n._('Press')}{' '}
              <kbd className="px-2 py-1 bg-gray-100 rounded text-xs">
                {modifierKey} + K
              </kbd>{' '}
              {i18n._('to open this dialog again.')}
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
