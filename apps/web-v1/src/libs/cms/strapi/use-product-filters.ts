import type {
  FilterOption,
  ProductCategoriesResponse,
  ProductCategory,
  ProductSpecificationValuesResponse,
  ProductSpecificationsResponse,
  RangeFilterData,
} from '../../types/product'
import { useClientFetch } from './use-client-fetch'

/**
 * Hook to fetch product categories for filter
 */
export const useProductCategories = () => {
  const { data, isLoading, error, mutate } =
    useClientFetch<ProductCategoriesResponse>({
      contentType: 'product-categories',
      params: {
        populate: ['image', 'parent', 'children'],
        sort: ['rank:asc', 'name:asc'],
        pagination: {
          pageSize: 100, // Get all categories
        },
      },
    })

  // Transform categories to filter options
  const categoryOptions: FilterOption[] =
    data?.data?.map((category: ProductCategory) => ({
      id: category.documentId,
      label: category.name,
      value: category.documentId,
      count: category.products?.length || 0,
    })) || []

  return {
    categories: data?.data || [],
    categoryOptions,
    isLoading,
    error,
    mutate,
  }
}

/**
 * Hook to fetch product specifications for filters
 */
export const useProductSpecifications = () => {
  const { data, isLoading, error, mutate } =
    useClientFetch<ProductSpecificationsResponse>({
      contentType: 'product-specifications',
      params: {
        populate: ['parent', 'children', 'product_specification_values'],
        sort: ['rank:asc', 'name:asc'],
        pagination: {
          pageSize: 100,
        },
      },
    })

  return {
    specifications: data?.data || [],
    isLoading,
    error,
    mutate,
  }
}

/**
 * Hook to fetch product specification values
 */
export const useProductSpecificationValues = (specificationId?: string) => {
  const { data, isLoading, error, mutate } =
    useClientFetch<ProductSpecificationValuesResponse>({
      contentType: 'product-specification-values',
      params: {
        populate: ['product_specification', 'product', 'product_model'],
        ...(specificationId && {
          filters: {
            product_specification: {
              documentId: {
                $eq: specificationId,
              },
            },
          },
        }),
        sort: ['value:asc'],
        pagination: {
          pageSize: 1000, // Get all values
        },
      },
    })

  return {
    values: data?.data || [],
    isLoading,
    error,
    mutate,
  }
}

/**
 * Hook to get filter data for specific specifications
 */
export const useFilterData = () => {
  const { specifications, isLoading: specsLoading } = useProductSpecifications()

  // Find specific specifications by name
  const boltLengthSpec = specifications.find((spec) =>
    spec.name.toLowerCase().includes('bolt length'),
  )
  const boreDiameterSpec = specifications.find((spec) =>
    spec.name.toLowerCase().includes('bore diameter'),
  )
  const efficiencyRatingSpec = specifications.find((spec) =>
    spec.name.toLowerCase().includes('efficiency rating'),
  )
  const insideDiameterSpec = specifications.find((spec) =>
    spec.name.toLowerCase().includes('inside diameter'),
  )

  // Fetch values for each specification
  const { values: boltLengthValues, isLoading: boltLoading } =
    useProductSpecificationValues(boltLengthSpec?.documentId)
  const { values: boreDiameterValues, isLoading: boreLoading } =
    useProductSpecificationValues(boreDiameterSpec?.documentId)
  const { values: efficiencyValues, isLoading: efficiencyLoading } =
    useProductSpecificationValues(efficiencyRatingSpec?.documentId)
  const { values: insideDiameterValues, isLoading: insideLoading } =
    useProductSpecificationValues(insideDiameterSpec?.documentId)

  // Transform bolt length values to range data
  const boltLengthData: RangeFilterData = {
    min: 0,
    max: 500,
    values: boltLengthValues
      .map((v) => Number.parseFloat(v.value))
      .filter((v) => !Number.isNaN(v))
      .sort((a, b) => a - b),
    unit: boltLengthSpec?.unit || 'mm',
  }

  // Transform bore diameter values to range data
  const boreDiameterData: RangeFilterData = {
    min: 0,
    max: 200,
    values: boreDiameterValues
      .map((v) => Number.parseFloat(v.value))
      .filter((v) => !Number.isNaN(v))
      .sort((a, b) => a - b),
    unit: boreDiameterSpec?.unit || 'mm',
  }

  // Transform inside diameter values to range data
  const insideDiameterData: RangeFilterData = {
    min: 0,
    max: 200,
    values: insideDiameterValues
      .map((v) => Number.parseFloat(v.value))
      .filter((v) => !Number.isNaN(v))
      .sort((a, b) => a - b),
    unit: insideDiameterSpec?.unit || 'mm',
  }

  // Transform efficiency rating values to options
  const efficiencyOptions: FilterOption[] = efficiencyValues
    .map((v) => ({
      id: v.documentId,
      label: v.value,
      value: v.value,
    }))
    .filter(
      (option, index, self) =>
        index === self.findIndex((o) => o.value === option.value),
    ) // Remove duplicates

  const isLoading =
    specsLoading ||
    boltLoading ||
    boreLoading ||
    efficiencyLoading ||
    insideLoading

  return {
    boltLengthData,
    boreDiameterData,
    insideDiameterData,
    efficiencyOptions,
    specifications: {
      boltLength: boltLengthSpec,
      boreDiameter: boreDiameterSpec,
      efficiencyRating: efficiencyRatingSpec,
      insideDiameter: insideDiameterSpec,
    },
    isLoading,
  }
}
