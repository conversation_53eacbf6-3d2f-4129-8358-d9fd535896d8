import { FilterOption } from '../../types/product'
import { useClientFetch } from './use-client-fetch'

/**
 * Hook to fetch products for filter (categories)
 */
export const useProductsForFilter = () => {
  const { data, isLoading, error, mutate } = useClientFetch<any>({
    contentType: 'products',
    params: {
      fields: ['id', 'name', 'slug', 'featured'],
      sort: ['name:asc'],
      pagination: {
        pageSize: 100,
      },
      filters: {
        publishedAt: {
          $notNull: true,
        },
      },
    },
  })

  // Transform products to filter options
  const categoryOptions: FilterOption[] =
    data?.data
      ?.filter((product: any) => product.name && product.slug)
      ?.map((product: any) => ({
        id: product.documentId,
        label: product.name,
        value: product.slug,
        count: 0,
      }))
      ?.sort((a: FilterOption, b: FilterOption) =>
        a.label.localeCompare(b.label),
      ) || []

  return {
    products: data?.data || [],
    categoryOptions,
    isLoading,
    error,
    mutate,
  }
}

/**
 * Hook to fetch product types for filter (categories) - DEPRECATED
 * @param productSlug - If provided, only get product types for this specific product
 */
export const useProductTypes = (productSlug?: string) => {
  const { data, isLoading, error, mutate } = useClientFetch<any>({
    contentType: 'product-types',
    params: {
      populate: {
        product: {
          fields: ['id', 'name', 'slug', 'featured'], // Only get essential product fields
        },
      },
      sort: ['name:asc'],
      pagination: {
        pageSize: 100, // Increase to get all types
      },
      filters: {
        // Only get published product types
        publishedAt: {
          $notNull: true,
        },
        // If productSlug is provided, filter by product slug
        ...(productSlug && {
          product: {
            slug: {
              $eq: productSlug,
            },
          },
        }),
      },
    },
  })

  // Transform product types to filter options - only include those with products
  const categoryOptions: FilterOption[] =
    data?.data
      ?.filter((type: any) => type.name && type.product) // Only product-types with associated products
      ?.map((type: any) => {
        // Count published products only
        const productCount = Array.isArray(type.product)
          ? type.product.filter((p: any) => p && p.id).length
          : type.product && type.product.id
            ? 1
            : 0

        return {
          id: type.documentId,
          label: type.name,
          value: type.documentId, // Use documentId for filtering instead of generated slug
          count: productCount,
          hasProduct: true, // Flag to indicate this product-type has an associated product
        }
      })
      ?.sort((a: FilterOption, b: FilterOption) => {
        // Sort by name alphabetically
        return a.label.localeCompare(b.label)
      }) || []

  return {
    productTypes: data?.data || [],
    categoryOptions,
    isLoading,
    error,
    mutate,
  }
}
