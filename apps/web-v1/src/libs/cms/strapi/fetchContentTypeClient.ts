import qs from 'qs'
import { APP_CONFIG } from '~/src/config-global'
/**
 * Fetches data for a specified Strapi content type.
 *
 * @param {string} contentType - The type of content to fetch from Strapi.
 * @param {string} params - Query parameters to append to the API request.
 * @return {Promise<object>} The fetched data.
 */

interface StrapiData {
  id: number
  [key: string]: any // Allow for any additional fields
}

interface StrapiResponse {
  data: StrapiData | StrapiData[]
}

export function spreadStrapiData(data: StrapiResponse): StrapiData | null {
  if (Array.isArray(data.data) && data.data.length > 0) {
    return data.data[0]
  }
  if (!Array.isArray(data.data)) {
    return data.data
  }
  return null
}

type FetchContentTypeOptions = {
  contentType: string
  params?: Record<string, any>
  spreadData?: boolean
}

export default async function fetchContentTypeClient({
  contentType,
  params = {},
  spreadData = true,
}: FetchContentTypeOptions) {
  try {
    const queryString = qs.stringify(
      {
        ...params,
      },
      {
        encodeValuesOnly: true,
      },
    )

    const response = await fetch(
      `${APP_CONFIG.apiUrl}/${contentType}${queryString ? `?${queryString}` : ''}`,
      {
        headers: {
          'Content-Type': 'application/json',
        },
        cache: 'no-store',
      },
    )

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const result = await response.json()

    if (spreadData && result.data) {
      return Array.isArray(result.data)
        ? result.data.map((item: any) => ({ ...item.attributes, id: item.id }))
        : { ...result.data.attributes, id: result.data.id }
    }

    return result
  } catch (error) {
    console.error('Error fetching content:', error)
    throw error
  }
}
