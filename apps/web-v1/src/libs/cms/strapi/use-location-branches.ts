import { cmsContentType } from '../../constants/cms'
import { QueryParams, useClientFetch } from './use-client-fetch'

const CNT_BRANCHES = cmsContentType.branches

export const useGetBranches = ({
  filters,
  locale,
  sort,
  pagination,
  populate,
  fields,
}: QueryParams) => {
  const { data, isLoading, error, mutate } = useClientFetch({
    contentType: CNT_BRANCHES,
    params: {
      filters,
      locale,
      sort,
      pagination,
      populate,
      fields,
    },
  })

  return {
    data,
    isLoading,
    error,
    mutate,
  }
}
