// src/libs/cms/strapi/use-cms.ts

import qs from 'qs'
import useSWR from 'swr'
import { APP_CONFIG } from '~/src/config-global'

const { apiUrl } = APP_CONFIG
interface StrapiError extends Error {
  info?: any
  status?: number
}

const createStrapiError = async (response: Response): Promise<StrapiError> => {
  const error = new Error(
    'An error occurred while fetching the data.',
  ) as StrapiError
  error.info = await response.json()
  error.status = response.status
  error.message = response.statusText
  return error
}

const fetcher = async (endpoint: string) => {
  const response = await fetch(`${apiUrl}/api/${endpoint}`, {
    headers: {
      'Cache-Control': 'no-cache',
      'Content-Type': 'application/json',
      Accept: 'application/json',
      // Authorization: `Bearer ${apiKey}`,
    },
  })

  if (!response.ok) {
    throw await createStrapiError(response)
  }

  return response.json()
}

export interface QueryParams {
  locale?: string
  filters?: Record<string, any>
  status?: string
  sort?: string | string[]
  pagination?: {
    page?: number
    pageSize?: number
    start?: number
    limit?: number
  }
  populate?: '*' | string[] | Record<string, any>
  fields?: string[]
}

export interface UseClientFetchParams {
  contentType: string
  params?: QueryParams
  options?: object
}

export function useClientFetch<T = any>({
  contentType,
  params = {},
  options = {},
}: UseClientFetchParams) {
  const { locale, filters, pagination, populate, fields, status, sort } = params

  const queryString = qs.stringify(
    {
      locale,
      filters,
      pagination,
      populate,
      fields,
      status,
      sort,
    },
    { encodeValuesOnly: true },
  )

  const endpoint = `${contentType}?${queryString}`

  const { data, error, isLoading, mutate } = useSWR<T>(
    endpoint,
    fetcher,
    options,
  )

  return {
    data,
    error,
    isLoading,
    mutate,
  }
}
