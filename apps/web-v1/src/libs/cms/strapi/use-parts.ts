import { cmsContentType } from '../../constants/cms'
import { QueryParams, useClientFetch } from './use-client-fetch'

const CNT_PARTS = cmsContentType.parts
const CNT_PART_MODELS = cmsContentType.partModels
const CNT_PART_CATEGORIES = cmsContentType.partCategories

// #########################################################################################
// PARTS
// #########################################################################################
/**
 * GET LIST MODELS
 */
export const useGetPartProducts = ({
  filters,
  locale,
  sort,
  pagination,
  populate,
  fields,
}: QueryParams) => {
  const { data, isLoading, error, mutate } = useClientFetch({
    contentType: CNT_PARTS,
    params: {
      filters,
      locale,
      sort,
      pagination,
      populate,
      fields,
    },
  })

  return {
    data,
    isLoading,
    error,
    mutate,
  }
}

/**
 * GET LIST MODELS
 */
export const useGetPartModels = ({
  filters,
  locale,
  sort,
  pagination,
  populate,
  fields,
}: QueryParams) => {
  const { data, isLoading, error, mutate } = useClientFetch({
    contentType: CNT_PART_MODELS,
    params: {
      filters,
      locale,
      sort,
      pagination,
      populate,
      fields,
    },
  })

  return {
    data,
    isLoading,
    error,
    mutate,
  }
}

// #########################################################################################
// PART CATEGORIES
// #########################################################################################

export const useGetPartCategories = ({
  filters,
  locale,
  sort,
  pagination,
  populate,
  fields,
}: QueryParams) => {
  const { data, isLoading, error, mutate } = useClientFetch({
    contentType: CNT_PART_CATEGORIES,
    params: {
      filters,
      locale,
      sort,
      pagination,
      populate,
      fields,
    },
  })

  return {
    data,
    isLoading,
    error,
    mutate,
  }
}
