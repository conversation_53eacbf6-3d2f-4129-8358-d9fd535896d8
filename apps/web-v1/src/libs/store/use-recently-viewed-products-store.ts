import { create } from 'zustand'
import { KEYS } from '~/src/config-global'
import { getCookieClient, setCookieClient } from '../data/cookies-client'

const { MODEL_VIEWED, MODEL_VIEWED_LIMIT } = KEYS

interface RecentlyViewedStore {
  MAX_RECENTLY_VIEWED_PRODUCTS: number
  list: string[]
  addProduct: (productId: string) => void
  loadFromCookies: () => void
}

export const useRecentlyViewedStore = create<RecentlyViewedStore>(
  (set, get) => ({
    MAX_RECENTLY_VIEWED_PRODUCTS: getCookieClient(MODEL_VIEWED_LIMIT)
      ? JSON.parse(getCookieClient(MODEL_VIEWED_LIMIT) || '3')
      : 3,
    list: [],
    loadFromCookies: () => {
      try {
        const cookie = getCookieClient(MODEL_VIEWED)
        const parsed = cookie ? JSON.parse(cookie) : []
        set({ list: parsed })
      } catch {
        set({ list: [] })
      }
    },
    addProduct: (productId: string) => {
      let list: string[]
      try {
        const cookie = getCookieClient(MODEL_VIEWED)
        list = cookie ? JSON.parse(cookie) : []
      } catch {
        list = []
      }
      list = list.filter((pid) => pid !== productId)
      list.unshift(productId)
      if (list.length > get().MAX_RECENTLY_VIEWED_PRODUCTS) {
        list = list.slice(0, get().MAX_RECENTLY_VIEWED_PRODUCTS)
      }

      setCookieClient(MODEL_VIEWED, JSON.stringify(list))
      set({ list })
    },
  }),
)
