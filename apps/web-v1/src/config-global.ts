////////////////////////////////
// App config
////////////////////////////////
export const APP_CONFIG = {
  baseUrl: process.env.NEXT_PUBLIC_BASE_URL || 'https://localhost:3000',
  apiUrl: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:1337',
  sessionKey: process.env.NEXT_PUBLIC_SESSION_KEY || 'ptc_session',
  rootDomain: process.env.NEXT_PUBLIC_ROOT_DOMAIN || 'domain.com',
  apiKey: process.env.NEXT_PUBLIC_API_TOKEN || '',
}

export const KEYS = {
  keyAccessToken: '_ptc_ac_token',
  keyRefreshToken: '_ptc_rf_token',
  keyCacheId: '_ptc_cache_id',
}

const locales = process.env.NEXT_PUBLIC_LANGUAGES?.split(',') || ['en', 'vi']

export const LINGUI_CONFIG = {
  locales: locales || ['en', 'vi'],
  defaultLocale: process.env.NEXT_PUBLIC_DEFAULT_LOCALE || 'en',
}

////////////////////////////////
// Analytics config
////////////////////////////////
export const ANALYTIC_API = {
  url: process.env.NEXT_PUBLIC_ANALYTIC_API_URL || '',
  apiKey: process.env.NEXT_PUBLIC_ANALYTIC_API_KEY || '',
}

////////////////////////////////
// Auth config
////////////////////////////////
export const AUTH_API = {
  google: {
    clientId: process.env.NEXT_PUBLIC_AUTH_GOOGLE_CLIENT_ID || '',
    clientSecret: process.env.NEXT_PUBLIC_AUTH_GOOGLE_CLIENT_SECRET || '',
  },
  facebook: {
    clientId: process.env.NEXT_PUBLIC_AUTH_FACEBOOK_CLIENT_ID || '',
    clientSecret: process.env.NEXT_PUBLIC_AUTH_FACEBOOK_CLIENT_SECRET || '',
  },
}

////////////////////////////////
// Form submission config
////////////////////////////////
export const FORM_SUBMISSION = {
  apiUrl: process.env.NEXT_PUBLIC_API_URL || '',
  token: process.env.NEXT_PUBLIC_API_TOKEN_SUBMISSION || '',
  endpoints: {
    // contactMessages: '/api/contact-messages',
    sponsorshipMessages: '/api/sponsor-messages',
    jobApplications: '/api/form-job-applications',
  },
}
