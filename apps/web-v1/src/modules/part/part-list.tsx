'use client'

import { useCallback, useEffect, useState } from 'react'

import { LayoutContainer } from '@ttplatform/core-page-builder/components'
import Image from 'next/image'
import HeadingSection from '~/src/components/renderer/heading-section'
import { useGetProductSpecifications } from '~/src/libs/cms/strapi/use-products'
import CategoryInsights from './category-insights'
import PartFilterBar from './part-filter-bar'
import PartFilterResults from './part-filter-results'
import PartSortBar from './part-sort-bar'
//---------------------------------------------------------------------------------
const Mock_Heading = {
  type: 'default',
  heading: {
    text: 'SẢN PHẨM',
    styles: {
      text_align: 'text-center',
      color: '#182230',
    },
  },
}
//---------------------------------------------------------------------------------
const defaultFilters: any = {
  sortBy: 'newest',
  keyWords: '',
  category: [],
}
//---------------------------------------------------------------------------------
type partListProps = {
  categorySlug: string
  locale: string
  insightsData: any
}

export default function PartList({
  categorySlug,
  locale,
  insightsData,
}: partListProps) {
  const [filters, setFilters] = useState(defaultFilters)

  const type_of_measure = filters?.['units-of-measure']

  const handleFilters = useCallback((name: string, value: any) => {
    setFilters((prevState: any) => ({
      ...prevState,
      [name]: value,
    }))
  }, [])

  const {
    data: partSpecifications,
    // isLoading: isLoadingPartSpecifications,
    // error: errorPartSpecifications,
  } = useGetProductSpecifications({
    filters: {
      type: 'part',
    },
    sort: 'rank:desc',
    locale,
  })

  useEffect(() => {
    if (partSpecifications?.data?.length > 0) {
      const type_of_measure = partSpecifications?.data?.find(
        (item: any) => item?.slug === 'units-of-measure',
      )

      const other_specifications = partSpecifications?.data?.filter(
        (item: any) => item?.slug !== 'units-of-measure',
      )

      const newArray = other_specifications?.map((item: any) => {
        return {
          [item?.slug]: [],
        }
      })

      const specification_fields = Object.fromEntries(
        newArray?.map((item: any) => [
          Object.keys(item)[0],
          item[Object.keys(item)[0]],
        ]),
      )

      setFilters((prevState: any) => ({
        ...prevState,
        [type_of_measure?.slug]: type_of_measure?.unit,
        specifications: specification_fields,
      }))
    }
  }, [partSpecifications])

  return (
    <LayoutContainer>
      <div className="w-full flex flex-col gap-10">
        <HeadingSection heading={Mock_Heading} />
        <div className="grid grid-cols-1 md:grid-cols-12 gap-x-6 lg:gap-x-10 relative">
          <aside className="hidden md:flex md:col-span-4 xl:col-span-3  flex-col w-full">
            <div className="flex flex-col gap-6">
              <PartFilterBar
                filters={filters}
                onFilters={handleFilters}
                categorySlug={categorySlug}
                locale={locale}
                partSpecificationsData={partSpecifications?.data}
                type_of_measure={type_of_measure}
              />
              <div className="hidden md:flex flex-col md:gap-4 lg:gap-6">
                {Array(2)
                  .fill(null)
                  .map((_, index) => (
                    <Image
                      key={index}
                      src="https://res.cloudinary.com/dgbocu1qv/image/upload/v1746019649/advertisement.png"
                      alt="Advertisement"
                      width={350}
                      height={350}
                      className="mx-auto mb-2 w-full"
                    />
                  ))}
              </div>
            </div>
          </aside>
          <div className="col-span-1 md:col-span-8 xl:col-span-9 w-full">
            <div className="flex flex-col gap-10">
              <div className="flex flex-col gap-4">
                <PartSortBar
                  filters={filters}
                  onFilters={handleFilters}
                  categorySlug={categorySlug}
                  locale={locale}
                  partSpecificationsData={partSpecifications?.data}
                  type_of_measure={type_of_measure}
                />
                <PartFilterResults
                  filters={filters}
                  locale={locale}
                  type_of_measure={type_of_measure}
                />
              </div>
              <CategoryInsights insightsData={insightsData} />
            </div>
          </div>
        </div>
      </div>
    </LayoutContainer>
  )
}
