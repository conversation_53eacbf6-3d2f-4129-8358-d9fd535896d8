'use client'

import { MainButton } from '@ttplatform/core-page-builder/components'
import { CardPart } from '~/src/components/card/card-part'
import { useGetPartModels } from '~/src/libs/cms/strapi/use-parts'
//---------------------------------------------------------------------------------
type IProps = {
  filters: any
  locale: string
  type_of_measure: string
}
//---------------------------------------------------------------------------------
export default function PartFilterResults({
  filters,
  locale,
  type_of_measure,
}: IProps) {
  const {
    data: partModels,
    // isLoading: isLoadingPartModels,
    // error: errorPartModels,
  } = useGetPartModels({
    filters: {
      // filter by keyWords
      $or: [
        {
          name: {
            $containsi: filters?.keyWords,
          },
        },
        {
          slug: {
            $containsi: filters?.keyWords,
          },
        },
        {
          part: {
            name: {
              $containsi: filters?.keyWords,
            },
          },
        },
        {
          part: {
            slug: {
              $containsi: filters?.keyWords,
            },
          },
        },
      ],

      // filter by category
      part: {
        category: {
          slug: {
            $in: filters?.category || [],
          },
        },
      },

      // filter by specifications

      part_specification_values:
        Object.values(filters?.specifications || {}).flat().length === 0
          ? {}
          : {
              $and: Object.entries(filters.specifications).map(
                ([slug, values]) => ({
                  $and: [
                    {
                      product_specification: {
                        slug: {
                          $eq: slug,
                        },
                      },
                    },
                    {
                      [type_of_measure === 'metric' ? 'value' : 'value_in_us']:
                        {
                          $in: values,
                        },
                    },
                  ],
                }),
              ),
            },
      locale,
    },
    sort: filters?.sortBy === 'newest' ? 'createdAt:desc' : 'createdAt:asc',
  })

  return (
    <div className="flex flex-col gap-4 items-center">
      <div className="w-full grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 lg:gap-6">
        {partModels?.data?.map((item: any, idx: number) => (
          <CardPart key={idx} item={item} />
        ))}
      </div>
      <MainButton label="Xem thêm" variant="secondary" isDisabledIcon />
    </div>
  )
}
