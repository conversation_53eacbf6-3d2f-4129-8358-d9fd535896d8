'use client'

import { <PERSON><PERSON><PERSON><PERSON> } from '@ttplatform/core-page-builder/components'
import {
  Button,
  Input,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@ttplatform/ui/components'
import { ArrowUpDown, ListFilter, Search } from 'lucide-react'
import { useCallback } from 'react'
import {
  Sheet,
  Sheet<PERSON>ontent,
  SheetTrigger,
} from '../../../../../packages/ui/src/components/sheet'
import PartFilterBar from './part-filter-bar'
//---------------------------------------------------------------------------------
const _SORT_LIST = [
  {
    id: 1,
    slug: 'newest',
    name: 'Mới nhất',
  },
  {
    id: 2,
    slug: 'oldest',
    name: '<PERSON><PERSON> nhất',
  },
]
//---------------------------------------------------------------------------------
type IProps = {
  filters: any
  onFilters: (name: string, value: any) => void
  locale: string
  partSpecificationsData: any
  type_of_measure: string
  categorySlug: string
}
//---------------------------------------------------------------------------------
export default function PartSortBar({
  filters,
  onFilters,
  locale,
  partSpecificationsData,
  type_of_measure,
  categorySlug,
}: IProps) {
  const handleFilterKeyWord = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      onFilters('keyWords', event.target.value)
    },
    [onFilters],
  )
  const handleSort = useCallback(
    (slug: string) => {
      onFilters('sortBy', slug)
    },
    [onFilters],
  )
  const renderSearchBar = (
    <div className="w-full md:w-1/2 flex items-center gap-2 px-3.5 py-2.5 border rounded-xl">
      <Search className="text-gray-400 w-5 min-w-5 h-5" />
      <Input
        onChange={handleFilterKeyWord}
        value={filters?.keyWord}
        placeholder={'Tìm kiếm theo tên/mã phụ tùng'}
        className="p-0 text-gray-500 border-0 shadow-none focus-visible:ring-0"
      />
      <MainButton />
    </div>
  )
  const renderSortBy = (
    <Select onValueChange={handleSort} defaultValue={_SORT_LIST[0]?.slug}>
      <SelectTrigger className="max-w-max min-w-[150px] data-[size=default]:h-10 lg:data-[size=default]:h-13">
        <ArrowUpDown className="w-5 h-5 min-w-5 min-h-5 text-gray-700" />{' '}
        <span className="text-gray-700 font-semibold">Sắp xếp:</span>
        <SelectValue />
      </SelectTrigger>
      <SelectContent>
        {_SORT_LIST.map((item: any, idx: number) => (
          <SelectItem key={idx} value={item?.slug}>
            {item?.name}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  )

  return (
    <div className="w-full flex flex-col sm:flex-row items-center justify-between gap-y-3 gap-x-4 sticky md:relative top-0 bg-white z-50 py-2 md:py-0">
      {renderSearchBar}
      <div className="w-full md:w-auto flex items-center gap-2 sm:gap-4 justify-end">
        {renderSortBy}
        <Sheet>
          <SheetTrigger asChild className="block md:hidden">
            <Button variant="primary" className="[&_svg]:size-5">
              <ListFilter />
            </Button>
          </SheetTrigger>
          <SheetContent className="w-[300px]">
            <PartFilterBar
              filters={filters}
              onFilters={onFilters}
              categorySlug={categorySlug}
              locale={locale}
              partSpecificationsData={partSpecificationsData}
              type_of_measure={type_of_measure}
            />
          </SheetContent>
        </Sheet>
      </div>
    </div>
  )
}
