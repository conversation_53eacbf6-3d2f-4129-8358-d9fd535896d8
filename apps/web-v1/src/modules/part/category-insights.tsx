import { MainButton } from '@ttplatform/core-page-builder/components'
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Ta<PERSON>Trigger,
  Typography,
} from '@ttplatform/ui/components'
import { useState } from 'react'
import GalleryVideos from '~/src/components/media-gallery/gallery-videos'
import { RenderImageUrlStrapi } from '~/src/components/render-imageUrl-strapi'
import BlockEditor from '~/src/components/renderer/block-editor'
//---------------------------------------------------------------------------------
type IProps = {
  insightsData: any
}
//---------------------------------------------------------------------------------
export default function CategoryInsights({ insightsData }: IProps) {
  const [activeTab, setActiveTab] = useState('insights_features_benefits')
  const [showMore, setShowMore] = useState(false)

  const handleTabChange = (value: string) => {
    setActiveTab(value)
    if (value === 'insights_features_benefits') {
      setShowMore(false)
    }
  }
  const renderFeaturesBenefits = (
    <div className="flex flex-col gap-5 md:gap-10">
      <div className="grid grid-cols-1 md:grid-cols-12 gap-x-10 gap-y-4">
        <div className="col-span-1 md:col-span-5 flex justify-center items-start">
          <div
            className="w-full max-w-[300px] md:max-w-auto h-auto aspect-[555/503] bg-primary p-1"
            style={{
              clipPath:
                'polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%)',
            }}
          >
            <div
              className="w-full h-auto aspect-[555/503]"
              style={{
                backgroundImage: `url(${RenderImageUrlStrapi({ url: insightsData?.insights_features_benefits?.image?.url })})`,
                backgroundSize: 'cover',
                backgroundPosition: 'center',
                backgroundRepeat: 'no-repeat',
                clipPath:
                  'polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%)',
              }}
            ></div>
          </div>
        </div>
        <div className="col-span-1 md:col-span-7">
          <BlockEditor
            content={insightsData?.insights_features_benefits?.description}
          />
        </div>
      </div>
      {showMore && (
        <div className="w-full">
          <BlockEditor
            content={insightsData?.insights_features_benefits?.details}
          />
        </div>
      )}
      <div className="w-full flex justify-center">
        <MainButton
          variant="secondary"
          isDisabledIcon
          onClick={() => setShowMore(!showMore)}
          label={showMore ? 'Show Less' : 'Show More'}
        />
      </div>
    </div>
  )
  const renderReferences = (
    <div className="w-full max-w-screen-lg mx-auto h-auto flex flex-col divide-y divide-gray-200">
      {insightsData?.insights_references?.map((item: any, idx: number) => (
        <div
          key={idx}
          className="flex gap-5 items-center justify-between py-4 md:py-5"
        >
          <Typography variant={'body1'} className="text-gray-800">
            {item?.name}
          </Typography>
          <MainButton variant={'primary'} />
        </div>
      ))}
    </div>
  )
  const renderVideos = (
    <GalleryVideos gallery_videos={insightsData?.insights_videos} />
  )
  return (
    <div className="flex flex-col gap-10 border-t border-gray-200 py-10">
      <Tabs
        defaultValue="insights_features_benefits"
        onValueChange={handleTabChange}
      >
        <TabsList className="bg-transparent text-gray-800 w-full gap-2 md:gap-4">
          <TabsTrigger
            value={'insights_features_benefits'}
            className="data-[state=active]:shadow-sm bg-gray-50 data-[state=active]:bg-[#FFCC00] data-[state=active]:border-[#FFCC00] py-2 sm:py-4 px-2 sm:px-6 rounded-full uppercase border border-gray-200"
          >
            <Typography variant="body2"> FEATURES AND BENEFITS</Typography>
          </TabsTrigger>
          <TabsTrigger
            value={'insights_references'}
            className="data-[state=active]:shadow-sm bg-gray-50 data-[state=active]:bg-[#FFCC00] data-[state=active]:border-[#FFCC00] py-2 sm:py-4 px-2 sm:px-6 rounded-full uppercase border border-gray-200"
          >
            <Typography variant="body2">REFERENCES</Typography>
          </TabsTrigger>
          <TabsTrigger
            value={'insights_videos'}
            className="data-[state=active]:shadow-sm bg-gray-50 data-[state=active]:bg-[#FFCC00] data-[state=active]:border-[#FFCC00] py-2 sm:py-4 px-2 sm:px-6 rounded-full uppercase border border-gray-200"
          >
            <Typography variant="body2">VIDEOS</Typography>
          </TabsTrigger>
        </TabsList>
      </Tabs>
      <div className="w-full max-h-[800px]">
        {activeTab === 'insights_features_benefits' && renderFeaturesBenefits}
        {activeTab === 'insights_references' && renderReferences}
        {activeTab === 'insights_videos' && renderVideos}
      </div>
    </div>
  )
}
