import {
  CmsMedia,
  LayoutContainer,
  MainButton,
} from '@ttplatform/core-page-builder/components'
import { Typography } from '@ttplatform/ui/components'
//---------------------------------------------------------------------------------
type heroProps = {
  heading: any
  sub_heading: any
  buttons?: any
  image?: any
}
//---------------------------------------------------------------------------------
export default function PartCategoryHero({
  heading,
  sub_heading,
  buttons,
  image,
}: heroProps) {
  return (
    <LayoutContainer>
      <div className="w-full flex flex-col gap-4 md:gap-10">
        <div className="w-full max-w-screen-lg mx-auto flex flex-col gap-4 items-center text-center">
          {heading && (
            <Typography variant={'h1'} className="relative max-w-screen-lg">
              {heading}
              <span className="block mx-auto w-[68px] h-[8px] rounded-none  bg-gradient-to-r from-[#FFCC00] via-[#FFD326] to-[#FFDE59]" />
            </Typography>
          )}
          {sub_heading && (
            <Typography variant={'body1'} className="max-w-screen-lg">
              {sub_heading}
            </Typography>
          )}

          {buttons?.length ? (
            <div className="flex justify-center gap-x-6 gap-y-4 flex-wrap ">
              {buttons?.map((button: any, idx: number) => (
                <MainButton
                  key={idx}
                  variant="secondary"
                  isDisabledIcon={true}
                  label={button?.text}
                  url={button?.url}
                  openInNewTab={button?.open_in_new_tab}
                  icon={button?.icon}
                />
              ))}
            </div>
          ) : null}
        </div>

        {image?.url ? (
          <div className="w-full h-full">
            <CmsMedia
              media={image}
              width={0}
              height={0}
              sizes="100vw"
              className="w-full h-auto aspect-[16/9] object-cover rounded-2xl"
            />
          </div>
        ) : null}
      </div>
    </LayoutContainer>
  )
}
