'use client'

import {
  CmsMedia,
  LayoutContainer,
  MainButton,
} from '@ttplatform/core-page-builder/components'
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
  Typography,
} from '@ttplatform/ui/components'
import { cn } from '@ttplatform/ui/lib'
import { useState } from 'react'
import { RenderImageUrlStrapi } from '~/src/components/render-imageUrl-strapi'
//---------------------------------------------------------------------------------
type heroProps = {
  pageData: any
}
//---------------------------------------------------------------------------------
export default function PartDetailsHero({ pageData }: heroProps) {
  const [imageSelected, setImageSelected] = useState(
    pageData?.image_gallery?.[0],
  )

  const renderSliderImage = (
    <div className="flex flex-col gap-6 w-full max-w-sm md:max-w-none">
      <div
        className="w-full h-auto aspect-square rounded-2xl border border-gray-200"
        style={{
          backgroundImage: `url(${RenderImageUrlStrapi({ url: imageSelected?.url })})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat',
        }}
      />
      {pageData?.image_gallery?.length > 1 ? (
        <Carousel
          opts={{
            align: 'start',
          }}
          className={cn('w-full mx-auto px-10')}
        >
          <CarouselContent>
            {pageData?.image_gallery?.map((item: any, idx: number) => (
              <CarouselItem
                key={idx}
                onClick={() => setImageSelected(item)}
                className={cn(
                  'flex justify-center cursor-pointer',
                  pageData?.image_gallery?.length > 2
                    ? 'basis-1/3 lg:basis-1/4'
                    : 'basis-1/2',
                )}
              >
                <div
                  className={cn(
                    ' w-full p-0.5',
                    imageSelected?.id === item?.id
                      ? 'bg-[#FFCC00]'
                      : 'bg-gray-200',
                  )}
                  style={{
                    clipPath:
                      'polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%)',
                  }}
                >
                  <div
                    className="w-full h-auto aspect-[100/89]"
                    style={{
                      clipPath:
                        'polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%)',
                      backgroundImage: `url(${RenderImageUrlStrapi({ url: item?.url })})`,
                      backgroundSize: 'cover',
                      backgroundPosition: 'center',
                      backgroundRepeat: 'no-repeat',
                    }}
                  ></div>
                </div>
              </CarouselItem>
            ))}
          </CarouselContent>
          {pageData?.image_gallery?.length > 2 && (
            <CarouselPrevious
              className=" left-0 bg-[#FC0] text-gray-800 hover:text-white border-transparent rounded-none hover:bg-[#FC0]"
              style={{
                clipPath:
                  'polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%)',
                boxShadow:
                  '0px 30px 40px 0px rgba(0, 0, 0, 0.5), 0px 0px 20px 0px rgba(0, 0, 0, 0.5), 0px -10px 10px 0px rgba(255, 255, 255, 0.2)',
              }}
            />
          )}
          {pageData?.image_gallery?.length > 2 && (
            <CarouselNext
              className="right-0 bg-[#FC0] text-gray-800  border-transparent rounded-none hover:bg-[#FC0] hover:text-white"
              style={{
                clipPath:
                  'polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%)',
                boxShadow:
                  '0px 30px 40px 0px rgba(0, 0, 0, 0.5), 0px 0px 20px 0px rgba(0, 0, 0, 0.5), 0px -10px 10px 0px rgba(255, 255, 255, 0.2)',
              }}
            />
          )}
        </Carousel>
      ) : null}
    </div>
  )
  return (
    <LayoutContainer>
      <div className="grid grid-cols-1 md:grid-cols-5 lg:grid-cols-3  gap-x-10">
        {/* Slider Image */}
        <div className="hidden md:block col-span-1 md:col-span-2 lg:col-span-1">
          {renderSliderImage}
        </div>
        {/* Content */}
        <div className="col-span-1 md:col-span-3 lg:col-span-2  w-full flex flex-col gap-6 sm:gap-8 md:gap-10">
          <div className="w-full max-w-screen-lg flex flex-col gap-6">
            <div className="w-full flex flex-col gap-4">
              {pageData?.name && (
                <Typography variant={'h1'} className="relative max-w-screen-lg">
                  {pageData?.name}:&nbsp;{pageData?.part?.name}
                  <span className="block w-[68px] h-[8px] rounded-none  bg-gradient-to-r from-[#FFCC00] via-[#FFD326] to-[#FFDE59]" />
                </Typography>
              )}
              {pageData?.brand?.name ? (
                <Typography
                  variant={'body1'}
                  weight={'semibold'}
                  color={'#FF9900'}
                >
                  Brand:&nbsp;{pageData?.brand?.name}
                </Typography>
              ) : null}
            </div>
            <div className="w-full flex justify-center md:hidden ">
              {renderSliderImage}
            </div>
            {pageData?.description && (
              <Typography variant={'body1'} className="max-w-screen-lg">
                {pageData?.description}
              </Typography>
            )}

            <div className="flex gap-x-6 gap-y-4 flex-wrap ">
              <MainButton
                variant="secondary"
                label={'Xem giá và đặt hàng'}
                // url={button?.url}
                // openInNewTab={button?.open_in_new_tab}
              />
              <MainButton
                variant="primary"
                label={'Yêu cầu tư vấn'}
                // url={button?.url}
                // openInNewTab={button?.open_in_new_tab}
              />
            </div>
          </div>

          {pageData?.ad_image?.url ? (
            <div className="w-full h-full">
              <CmsMedia
                media={pageData?.ad_image}
                width={0}
                height={0}
                sizes="100vw"
                className="w-full h-auto object-cover rounded-2xl"
              />
            </div>
          ) : null}
        </div>
      </div>
    </LayoutContainer>
  )
}
