import {
  Checkbox,
  Label,
  ScrollArea,
  Tabs,
  TabsList,
  TabsTrigger,
  Typography,
} from '@ttplatform/ui/components'
import { useCallback } from 'react'
import { useGetPartCategories } from '~/src/libs/cms/strapi/use-parts'
//---------------------------------------------------------------------------------
type FilterBarProps = {
  filters: any
  onFilters: (name: string, value: any) => void
  categorySlug: string
  locale: string
  partSpecificationsData: any
  type_of_measure: string
}
//---------------------------------------------------------------------------------
export default function PartFilterBar({
  filters,
  onFilters,
  categorySlug,
  locale,
  partSpecificationsData,
  type_of_measure,
}: FilterBarProps) {
  const {
    data: partCategories,
    // isLoading: isLoadingPartCategories,
    // error: errorPartCategories,
  } = useGetPartCategories({
    filters: {
      slug: categorySlug,
    },

    locale,
  })

  const render_unit_of_measure = partSpecificationsData?.find(
    (item: any) => item?.slug === 'units-of-measure',
  )

  const render_other_specifications = partSpecificationsData?.filter(
    (item: any) => item?.slug !== 'units-of-measure',
  )

  const handleFilterCategory = useCallback(
    (checked: boolean, slug: string) => {
      if (checked) {
        onFilters('category', [...(filters?.category || []), slug])
      } else {
        onFilters(
          'category',
          filters?.category?.filter((item: string) => item !== slug) || [],
        )
      }
    },
    [onFilters, filters?.category],
  )
  //---------------------------------------------------------------------------------
  const handleFilterUnitOfMeasure = useCallback(
    (newValue: string) => {
      onFilters('units-of-measure', newValue)
      onFilters('specifications', {
        ...Object.fromEntries(
          Object.keys(filters?.specifications || {}).map((key) => [key, []]),
        ),
      })
    },
    [onFilters, filters?.specifications],
  )
  //---------------------------------------------------------------------------------
  const handleFilterSpecification = useCallback(
    (checked: boolean, slug: string, value: any) => {
      if (checked) {
        onFilters('specifications', {
          ...filters?.specifications,
          ...{ [slug]: [...(filters?.specifications?.[slug] || []), value] },
        })
      } else {
        onFilters('specifications', {
          ...filters?.specifications,
          ...{
            [slug]: filters?.specifications?.[slug]?.filter(
              (item: any) => item !== value,
            ),
          },
        })
      }
    },
    [onFilters, filters?.specifications],
  )

  const renderFilterCategory = (
    <div className="flex flex-col gap-4">
      <Typography
        variant="h6"
        weight={'semibold'}
        className="text-gray-800 uppercase"
      >
        Category
      </Typography>
      <div className="flex flex-col gap-4">
        {partCategories?.data?.[0]?.children?.map(
          (category: any, idx: number) => (
            <div key={idx} className="flex items-center space-x-2">
              <Checkbox
                id={category?.slug}
                onCheckedChange={(checked: boolean) =>
                  handleFilterCategory(checked, category?.slug)
                }
              />
              <Label htmlFor={category?.slug}>{category?.title}</Label>
            </div>
          ),
        )}
      </div>
    </div>
  )

  const renderFilterSpecifications = (
    <div className="flex flex-col gap-6">
      <Typography
        variant="h6"
        weight={'semibold'}
        className="text-gray-800 uppercase"
      >
        Specifications
      </Typography>
      {render_unit_of_measure && (
        <div className="flex flex-col gap-4">
          <Typography
            variant="body1"
            weight={'medium'}
            className="text-gray-700"
          >
            {render_unit_of_measure?.name}
          </Typography>
          <Tabs
            defaultValue={render_unit_of_measure?.unit}
            onValueChange={handleFilterUnitOfMeasure}
          >
            <TabsList className="bg-transparent text-gray-800 w-full">
              <TabsTrigger
                value={render_unit_of_measure?.unit}
                className="data-[state=active]:shadow-sm data-[state=active]:bg-[#FFCC00] p-2 rounded-none uppercase"
              >
                {render_unit_of_measure?.unit}
              </TabsTrigger>
              <TabsTrigger
                value={render_unit_of_measure?.unit_in_us}
                className="data-[state=active]:shadow-sm data-[state=active]:bg-[#FFCC00] p-2 rounded-none uppercase"
              >
                {render_unit_of_measure?.unit_in_us}
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
      )}
      {render_other_specifications?.map((item: any, idx: number) => {
        return (
          <div key={idx} className="flex flex-col gap-4">
            <Typography
              variant="body1"
              weight={'medium'}
              className="text-gray-700"
            >
              {item?.name}
            </Typography>

            {item?.product_specification_values
              ? item?.product_specification_values?.map(
                  (value: any, idx: number) => {
                    return (
                      <div key={idx} className="flex items-center space-x-2">
                        <Checkbox
                          id={value?.id}
                          checked={filters?.specifications?.[
                            item?.slug
                          ]?.includes(
                            type_of_measure === 'metric'
                              ? value?.value
                              : value?.value_in_us,
                          )}
                          onCheckedChange={(checked: boolean) =>
                            handleFilterSpecification(
                              checked,
                              item?.slug,
                              type_of_measure === 'metric'
                                ? value?.value
                                : value?.value_in_us,
                            )
                          }
                        />
                        <Label htmlFor={value?.id}>
                          {type_of_measure === 'metric'
                            ? value?.value
                            : value?.value_in_us}
                        </Label>
                      </div>
                    )
                  },
                )
              : null}
          </div>
        )
      })}
    </div>
  )

  return (
    <div className="flex flex-col h-auto max-h-dvh gap-6 md:border border-gray-200 rounded-none md:rounded-lg p-6">
      <Typography
        variant="h5"
        weight={'bold'}
        className="text-gray-800 uppercase"
      >
        LỌC SẢN PHẨM
      </Typography>
      <ScrollArea className="h-full">
        <div className="flex flex-col gap-6">
          {renderFilterCategory}
          {renderFilterSpecifications}
        </div>
      </ScrollArea>
    </div>
  )
}
