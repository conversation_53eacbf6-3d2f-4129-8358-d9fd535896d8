'use client'

import { useEffect } from 'react'
import { DynamicZoneManager } from '~/src/components/dynamic-zone'
import { LINGUI_CONFIG } from '~/src/config-global'
import { useRecentlyViewedStore } from '~/src/libs/store/use-recently-viewed-products-store'
import DetailsHeader from './components/deails-header'
import { ScrollIdBar } from './components/scroll-id-bar'

const _LIST_LINK = [
  {
    title: 'Lợi ích',
    url: '#LOI_ICH',
    icon: '/icons/products/loi-ich.svg',
  },
  {
    title: 'Tính năng',
    url: '#TINH_NANG',
    icon: '/icons/products/categories.svg',
  },
  {
    title: 'Thông số',
    url: '#THONG_SO',
    icon: '/icons/products/thong-so.svg',
  },
  {
    title: 'Thiết bị',
    url: '#THIET_BI',
    icon: '/icons/products/thiet-bi.svg',
  },
  {
    title: 'Đánh giá',
    url: '#DANH_GIA',
    icon: '/icons/products/danh-gia.svg',
  },
  {
    title: 'Công nghệ',
    url: '#CONG_NGHE',
    icon: '/icons/products/cong-nghe.svg',
  },
  {
    title: 'Gallery',
    url: '#GALLERY',
    icon: '/icons/products/gallery.svg',
  },
  {
    title: 'So sánh',
    url: '#SO_SANH',
    icon: '/icons/products/so-sanh.svg',
  },
  {
    title: 'Phụ tùng',
    url: '#PHU_TUNG',
    icon: '/icons/products/phu-tung.svg',
  },
]

const ProductModelDetailView = ({
  productModelData,
}: { productModelData: any }) => {
  const addProductModel = useRecentlyViewedStore((state) => state.addProduct)
  useEffect(() => {
    if (!productModelData) return
    addProductModel(productModelData.documentId)
  }, [productModelData, addProductModel])
  return (
    <div className="relative">
      <ScrollIdBar listLink={_LIST_LINK} />
      <DetailsHeader modelData={productModelData} />

      {productModelData?.dynamic_zone &&
      productModelData.dynamic_zone.length > 0 ? (
        <DynamicZoneManager
          dynamicZone={productModelData.dynamic_zone}
          locale={LINGUI_CONFIG.defaultLocale}
        />
      ) : (
        /* Fallback to hardcoded content */
        <>
          {/* <DetailsOutstandingFeatures />
          <DetailsSpecifications />
          <DetailsBenefits />
          <DetailsDevice />
          <DetailsReviews product={product} />
          <DetailsFaq product={product} />
          <DetailsRelatedTechnologyAndServices />
          <DetailsMediaGallery />
          <DetailsCompare product={product} />
          <DetailsRelatedParts />
          <RecentlyViewedProductsSection /> */}
        </>
      )}
    </div>
  )
}

export default ProductModelDetailView
