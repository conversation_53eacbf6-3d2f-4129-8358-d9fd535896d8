import { BRecentlyViewedProducts } from '@ttplatform/core-page-builder/components'
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@ttplatform/ui/components'
import { MessageCircle, Pen, Phone } from 'lucide-react'
import ProductList from './product-list'
//---------------------------------------------------------------------------------

const _HEADING = {
  title: 'MÁY XÚC ĐÀO THỦY LỰC CAT',
  description:
    'Với bất kỳ ứng dụng công việc nào - đào đất đá, đào rãnh hay chất tải vật liệu - máy xúc đào thủy lực Cat® đều mang đến hiệu suất cao nhất, sự linh hoạt vượt trội và hiệu quả tiêu thụ nhiên liệu tuyệt vời. Vận hành đơn giản, bảo dưỡng dễ dàng cùng sự bền bỉ và an toàn vượt trội và được tích hợp các công nghệ tiến tiến nhất sẽ giúp các công ty xây dựng và khai mỏ tăng tiến độ, giảm chi phí và tối đa hóa lợi nhuận. Được công nhận trên toàn cầu về độ tin cậy và hiệu suất vượt trội, máy xúc đào bánh xích Cat® đã và đang khẳng định vị thế dẫn đầu và giành được sự tin tưởng của khách hàng tại Việt Nam.',
  buttons: [
    {
      icon: <Phone />,
      text: '1800 599 990',
      url: '#',
    },
    {
      icon: <Pen />,
      text: 'YÊU CẦU BÁO GIÁ',
      url: '#',
    },
    {
      icon: <MessageCircle />,
      text: 'CHAT NGAY',
      url: '#',
    },
  ],
  image: '/images/products/may-cong-trinh-sem.png',
}
//---------------------------------------------------------------------------------
interface ProductsViewProps {
  searchParams?: { [key: string]: string | string[] | undefined }
}

export default function ProductsView({ searchParams }: ProductsViewProps) {
  const renderBreadcrumb = (
    <div className="container max-w-screen-xl mx-auto px-4">
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/">Trang chủ</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink>Sản phẩm</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink>Thiết bị rửa cát & nghiền sàng</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Máy xúc đào thủy lực CAT</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>
    </div>
  )

  return (
    <div className="relative">
      {/* <BProductHeading heading={_HEADING} breadcrumb={renderBreadcrumb} /> */}
      <ProductList />
      <BRecentlyViewedProducts />
    </div>
  )
}
