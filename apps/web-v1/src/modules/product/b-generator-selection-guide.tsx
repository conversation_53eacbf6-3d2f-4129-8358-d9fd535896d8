'use client'

import { t } from '@lingui/core/macro'
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
  ScrollArea,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  Typography,
} from '@ttplatform/ui/components'

type GeneratorItem = {
  module: string
  power: string
}

type GeneratorType = {
  type: string
  capacityType: string
  list: GeneratorItem[]
}

type Props = {
  title?: string
  generatorTypes?: GeneratorType[]
}

export function BGeneratorSelectionGuide({ generatorTypes = [] }: Props) {
  return (
    <section className="relative z-10">
      <div className="flex flex-col gap-10 py-10">
        <Carousel
          opts={{
            align: 'start',
          }}
          className="w-full px-10"
        >
          <CarouselContent className="-ml-6">
            {generatorTypes.map((item: GeneratorType, idx: number) => (
              <CarouselItem
                key={idx}
                className="pl-6 basis-full md:basis-1/2 lg:basis-1/3"
              >
                <ItemDetails item={item} />
              </CarouselItem>
            ))}
          </CarouselContent>
          <CarouselPrevious
            className="left-0 bg-primary text-gray-800 hover:text-white border-transparent rounded-none hover:bg-gray-50"
            style={{
              clipPath:
                'polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%)',
              boxShadow:
                '0px 30px 40px 0px rgba(0, 0, 0, 0.5), 0px 0px 20px 0px rgba(0, 0, 0, 0.5), 0px -10px 10px 0px rgba(255, 255, 255, 0.2)',
            }}
          />
          <CarouselNext
            className="right-0 bg-primary text-gray-800 hover:text-white border-transparent rounded-none hover:bg-gray-50"
            style={{
              clipPath:
                'polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%)',
              boxShadow:
                '0px 30px 40px 0px rgba(0, 0, 0, 0.5), 0px 0px 20px 0px rgba(0, 0, 0, 0.5), 0px -10px 10px 0px rgba(255, 255, 255, 0.2)',
            }}
          />
        </Carousel>
      </div>
    </section>
  )
}

type ItemProps = {
  item: GeneratorType
}

function ItemDetails({ item }: ItemProps) {
  const handleScrollAreaWheel = (e: React.WheelEvent) => {
    e.stopPropagation()
  }

  return (
    <div className="flex flex-col w-full border rounded-lg overflow-hidden bg-white text-gray-800">
      <div className="py-4 md:py-5 px-5 md:px-6 bg-gray-50">
        <Typography variant={'h6'} className="uppercase font-semibold">
          {item.type}
        </Typography>
      </div>

      <Table>
        <TableHeader className="bg-primary">
          <TableRow>
            <TableHead className="w-[40%] md:w-[50%] p-0">
              <Typography
                variant={'body2'}
                className="text-[#182230] font-medium px-4 md:px-6 py-3"
              >
                {t`Module`}
              </Typography>
            </TableHead>
            <TableHead className="w-[60%] md:w-[50%] p-0">
              <Typography
                variant={'body2'}
                className="text-[#182230] font-medium px-4 md:px-6 py-3"
              >
                {item.capacityType}
              </Typography>
            </TableHead>
          </TableRow>
        </TableHeader>
      </Table>

      <div
        className="w-full h-[400px] 2xl:h-[720px]"
        onWheel={handleScrollAreaWheel}
      >
        <ScrollArea className="w-full h-full">
          <Table>
            <TableBody className="bg-white">
              {item.list.map((generator: GeneratorItem, idx: number) => (
                <TableRow key={idx}>
                  <TableCell className="px-4 md:px-6 py-3 md:py-4">
                    <Typography variant={'body2'} className="text-gray-800">
                      {generator.module}
                    </Typography>
                  </TableCell>

                  <TableCell className="px-4 md:px-6 py-3 md:py-4">
                    <Typography variant={'body2'} className="text-gray-800">
                      {generator.power}
                    </Typography>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </ScrollArea>
      </div>
    </div>
  )
}
