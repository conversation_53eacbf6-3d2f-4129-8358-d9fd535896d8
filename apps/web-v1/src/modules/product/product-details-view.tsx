'use client'

import { BRecentlyViewedProducts } from '@ttplatform/core-page-builder/components'
import { useEffect } from 'react'
import { useRecentlyViewedStore } from '~/src/libs/store/use-recently-viewed-products-store'
import DetailsHeader from './components/deails-header'
import DetailsBenefits from './components/details-benefits'
import { DetailsCompare } from './components/details-compare'
import DetailsDevice from './components/details-device'
import DetailsFaq from './components/details-faq'
import { DetailsMediaGallery } from './components/details-media-gallery'
import DetailsOutstandingFeatures from './components/details-outstanding-features'
import { DetailsRelatedParts } from './components/details-related-parts'
import DetailsRelatedTechnologyAndServices from './components/details-related-technology-and-services'
import DetailsReviews from './components/details-reviews'
import DetailsSpecifications from './components/details-specifications'
import { ScrollIdBar } from './components/scroll-id-bar'
//---------------------------------------------------------------------------------
const product = {
  name: 'MÁY XÚC ĐÀO THỦY LỰC CAT®',
  product_code: '320 GX',
  description:
    'Cat® 320 GX mới là một chiếc máy đào thủy lực 20 tấn đáp ứng mọi kỳ vọng của khách hàng về độ tin cậy và vận hành đơn giản, cùng với việc dễ dàng bảo dưỡng và chi phí sở hữu hợp lý sẽ giúp khách hàng hoàn vốn đầu tư nhanh hơn.',
  image: '/images/products/may-cong-trinh.png',
  cong_suat: '104 kw',
  dung_tich_gau: '1.2 m3',
  khoi_luong_van_hanh: '20500kg',
}
const _LIST_LINK = [
  {
    title: 'Lợi ích',
    url: '#LOI_ICH',
    icon: '/icons/products/loi-ich.svg',
  },
  {
    title: 'Tính năng',
    url: '#TINH_NANG',
    icon: '/icons/products/categories.svg',
  },
  {
    title: 'Thông số',
    url: '#THONG_SO',
    icon: '/icons/products/thong-so.svg',
  },
  {
    title: 'Thiết bị',
    url: '#THIET_BI',
    icon: '/icons/products/thiet-bi.svg',
  },
  {
    title: 'Đánh giá',
    url: '#DANH_GIA',
    icon: '/icons/products/danh-gia.svg',
  },
  {
    title: 'Công nghệ',
    url: '#CONG_NGHE',
    icon: '/icons/products/cong-nghe.svg',
  },
  {
    title: 'Gallery',
    url: '#GALLERY',
    icon: '/icons/products/gallery.svg',
  },
  {
    title: 'So sánh',
    url: '#SO_SANH',
    icon: '/icons/products/so-sanh.svg',
  },
  {
    title: 'Phụ tùng',
    url: '#PHU_TUNG',
    icon: '/icons/products/phu-tung.svg',
  },
]
//---------------------------------------------------------------------------------
export default function ProductDetailsView({
  productData,
}: { productData: any }) {
  const addProduct = useRecentlyViewedStore((state) => state.addProduct)
  useEffect(() => {
    if (!productData) return
    addProduct(productData.documentId)
  }, [productData, addProduct])
  return (
    <div className="relative">
      <ScrollIdBar listLink={_LIST_LINK} />
      <DetailsHeader />
      <DetailsBenefits />
      <DetailsOutstandingFeatures />
      <DetailsSpecifications />
      <DetailsDevice />
      <DetailsReviews product={product} />
      <DetailsFaq product={product} />
      <DetailsRelatedTechnologyAndServices />
      <DetailsMediaGallery />
      <DetailsCompare product={product} />
      <DetailsRelatedParts />
      <BRecentlyViewedProducts />
    </div>
  )
}
