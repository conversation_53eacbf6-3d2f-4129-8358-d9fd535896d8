import { Trans } from '@lingui/react/macro'
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
  Label,
  Slider,
  Typography,
} from '@ttplatform/ui/components'
import { cn } from '@ttplatform/ui/lib'
import { useCallback, useState } from 'react'
import {
  useFilterData,
  useProductCategories,
} from '../../../libs/cms/strapi/use-product-filters'
import type { DynamicFilterSpec } from '../../../libs/types/product'
import { CustomCheckbox } from './custom-checkbox'

//---------------------------------------------------------------------------------
type FilterBarProps = {
  filters: any
  onFilters: (name: string, value: any) => void
}

//---------------------------------------------------------------------------------
export default function FilterBar({ filters, onFilters }: FilterBarProps) {
  const [isMetric, setIsMetric] = useState(true)
  const [selectedCategories, setSelectedCategories] = useState<string[]>([])
  const [selectedEfficiencyRatings, setSelectedEfficiencyRatings] = useState<
    string[]
  >([])

  // Fetch dynamic data
  const { categoryOptions, isLoading: categoriesLoading } =
    useProductCategories()
  const { specifications } = useFilterData()

  // Handlers
  const handleFilterCategory = useCallback(
    (slug: string, checked: boolean) => {
      setSelectedCategories((prev) => {
        const newSelection = checked
          ? [...prev, slug]
          : prev.filter((item) => item !== slug)
        onFilters('category', newSelection)
        return newSelection
      })
    },
    [onFilters],
  )

  const handleFilterEfficiencyRating = useCallback(
    (slug: string, checked: boolean) => {
      setSelectedEfficiencyRatings((prev) => {
        const newSelection = checked
          ? [...prev, slug]
          : prev.filter((item) => item !== slug)
        onFilters('efficiencyRating', newSelection)
        return newSelection
      })
    },
    [onFilters],
  )

  const handleRangeFilter = useCallback(
    (filterSlug: string, newValue: number | number[]) => {
      onFilters(filterSlug, newValue as number[])
    },
    [onFilters],
  )

  const getUnitLabel = (baseText: string) => {
    return isMetric ? `${baseText} (mm)` : `${baseText} (in)`
  }

  // Render dynamic filter specification
  const renderFilterSpec = (spec: DynamicFilterSpec) => {
    if (spec.isLoading) {
      return (
        <Accordion
          key={spec.id}
          type="single"
          collapsible
          defaultValue="item-1"
        >
          <AccordionItem value="item-1">
            <AccordionTrigger className="py-0 hover:no-underline cursor-pointer">
              <Typography variant="body1" className="text-gray-700 font-medium">
                <Trans>{spec.name}</Trans>
              </Typography>
            </AccordionTrigger>
            <AccordionContent className="py-4">
              <div className="text-gray-500">Loading...</div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      )
    }

    if (spec.type === 'range') {
      return (
        <Accordion
          key={spec.id}
          type="single"
          collapsible
          defaultValue="item-1"
        >
          <AccordionItem value="item-1">
            <AccordionTrigger className="py-0 hover:no-underline cursor-pointer">
              <Typography variant="body1" className="text-gray-700 font-medium">
                <Trans>{getUnitLabel(spec.name)}</Trans>
              </Typography>
            </AccordionTrigger>
            <AccordionContent className="py-4">
              <Slider
                value={filters?.[spec.slug] || [spec.min, spec.max]}
                onValueChange={(value) => handleRangeFilter(spec.slug, value)}
                max={spec.max}
                min={spec.min}
                step={isMetric ? 1 : 0.1}
                className="w-full"
              />
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      )
    }

    if (spec.type === 'options') {
      return (
        <Accordion
          key={spec.id}
          type="single"
          collapsible
          defaultValue="item-1"
        >
          <AccordionItem value="item-1">
            <AccordionTrigger className="py-0 hover:no-underline cursor-pointer">
              <Typography variant="body1" className="text-gray-700 font-medium">
                <Trans>{spec.name}</Trans>
              </Typography>
            </AccordionTrigger>
            <AccordionContent className="py-4">
              <div className="flex flex-col gap-4">
                {spec.options.map((option) => (
                  <div
                    key={option.id}
                    className="flex items-center space-x-2 cursor-pointer"
                  >
                    <CustomCheckbox
                      id={option.value.toString()}
                      checked={selectedEfficiencyRatings.includes(
                        option.value.toString(),
                      )}
                      onCheckedChange={(checked) =>
                        handleFilterEfficiencyRating(
                          option.value.toString(),
                          checked as boolean,
                        )
                      }
                      className="cursor-pointer"
                    />
                    <Typography variant="body2">
                      <Label
                        className="cursor-pointer"
                        htmlFor={option.value.toString()}
                      >
                        {option.label}
                      </Label>
                    </Typography>
                  </div>
                ))}
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      )
    }

    return null
  }

  // Render category filter
  const renderFilterCategory = (
    <Accordion type="single" collapsible defaultValue="item-1">
      <AccordionItem value="item-1">
        <AccordionTrigger className="py-0 hover:no-underline cursor-pointer">
          <Typography variant="body1" className="text-gray-700 font-medium">
            <Trans>Category</Trans>
          </Typography>
        </AccordionTrigger>
        <AccordionContent className="py-4">
          <div className="flex flex-col gap-4">
            {categoriesLoading ? (
              <div className="text-gray-500">Loading categories...</div>
            ) : (
              categoryOptions.map((category) => (
                <div
                  key={category.id}
                  className="flex items-center space-x-2 cursor-pointer"
                >
                  <CustomCheckbox
                    id={category.value.toString()}
                    checked={selectedCategories.includes(
                      category.value.toString(),
                    )}
                    onCheckedChange={(checked) =>
                      handleFilterCategory(
                        category.value.toString(),
                        checked as boolean,
                      )
                    }
                    className="cursor-pointer"
                  />
                  <Typography variant="body2">
                    <Label
                      className="cursor-pointer"
                      htmlFor={category.value.toString()}
                    >
                      {category.label}
                    </Label>
                  </Typography>
                </div>
              ))
            )}
          </div>
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  )

  return (
    <div className="sticky top-4 h-[calc(100vh-2rem)]">
      <div className="bg-white border border-gray-200 rounded-lg shadow-sm h-full flex flex-col">
        <div className="p-6 border-b border-gray-200">
          <Typography variant="h6" className="text-gray-900 font-semibold">
            <Trans>FILTER PRODUCTS</Trans>
          </Typography>
        </div>

        <div className="flex-1 overflow-hidden">
          <div
            className="h-full overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-transparent"
            style={{ overscrollBehavior: 'contain' }}
          >
            <div className="p-6 space-y-6">
              {/* Unit Toggle */}
              <div className="flex flex-col gap-4">
                <Typography
                  variant="body1"
                  className="text-gray-700 font-medium"
                >
                  <Trans>Units of measure</Trans>
                </Typography>
                <div className="flex gap-2">
                  <button
                    type="button"
                    onClick={() => setIsMetric(false)}
                    className={cn(
                      'px-3 py-1 text-sm rounded border',
                      !isMetric
                        ? 'bg-blue-500 text-white border-blue-500'
                        : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50',
                    )}
                  >
                    <Trans>US</Trans>
                  </button>
                  <button
                    type="button"
                    onClick={() => setIsMetric(true)}
                    className={cn(
                      'px-3 py-1 text-sm rounded border',
                      isMetric
                        ? 'bg-blue-500 text-white border-blue-500'
                        : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50',
                    )}
                  >
                    <Trans>METRIC</Trans>
                  </button>
                </div>
              </div>

              {/* Category Filter */}
              {renderFilterCategory}

              {/* Dynamic Specification Filters */}
              {specifications.map((spec) => renderFilterSpec(spec))}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
