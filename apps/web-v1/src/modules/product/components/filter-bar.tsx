import { Trans } from '@lingui/react/macro'
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
  Label,
  Slider,
  Typography,
} from '@ttplatform/ui/components'
import { cn } from '@ttplatform/ui/lib'
import { useCallback, useState } from 'react'
import {
  useFilterData,
  useProductCategories,
} from '../../../libs/cms/strapi/use-product-filters'
import type { DynamicFilterSpec } from '../../../libs/types/product'
import { CustomCheckbox } from './custom-checkbox'

//---------------------------------------------------------------------------------
type FilterBarProps = {
  filters: any
  onFilters: (name: string, value: any) => void
}

//---------------------------------------------------------------------------------
export default function FilterBar({ filters, onFilters }: FilterBarProps) {
  const [isMetric, setIsMetric] = useState(true)
  const [selectedCategories, setSelectedCategories] = useState<string[]>([])
  const [selectedSpecificationOptions, setSelectedSpecificationOptions] = useState<Record<string, string[]>>({})

  // Fetch dynamic data
  const { categoryOptions, isLoading: categoriesLoading } = useProductCategories()
  const { specifications } = useFilterData()

  const handleFilterCategory = useCallback(
    (slug: string, checked: boolean) => {
      setSelectedCategories((prev) => {
        const newSelection = checked
          ? [...prev, slug]
          : prev.filter((item) => item !== slug)
        onFilters('category', newSelection)
        return newSelection
      })
    },
    [onFilters],
  )

  const handleRangeFilter = useCallback(
    (filterSlug: string, newValue: number | number[]) => {
      onFilters(filterSlug, newValue as number[])
    },
    [onFilters],
  )

  const handleSpecificationOptionFilter = useCallback(
    (specSlug: string, optionSlug: string, checked: boolean) => {
      setSelectedSpecificationOptions((prev) => {
        const currentOptions = prev[specSlug] || []
        const newSelection = checked
          ? [...currentOptions, optionSlug]
          : currentOptions.filter((item) => item !== optionSlug)

        const updatedState = {
          ...prev,
          [specSlug]: newSelection,
        }

        onFilters(specSlug, newSelection)
        return updatedState
      })
    },
    [onFilters],
  )

  const getUnitLabel = (baseText: string) => {
    return `${baseText} (${isMetric ? 'mm' : 'in'})`
  }

  // Render category filter (same as before)
  const renderFilterCategory = (
    <div className="flex flex-col gap-4 mt-2">
      <Typography variant="h6" className="text-gray-800 uppercase font-semibold">
        <Trans>Category</Trans>
      </Typography>
      <div className="flex flex-col gap-4">
        {categoriesLoading ? (
          <div className="text-gray-500">Loading categories...</div>
        ) : (
          categoryOptions.map((category) => (
            <div key={category.id} className="flex items-center space-x-2 cursor-pointer">
              <CustomCheckbox
                id={category.value.toString()}
                checked={selectedCategories.includes(category.value.toString())}
                onCheckedChange={(checked) =>
                  handleFilterCategory(category.value.toString(), checked as boolean)
                }
                className="cursor-pointer"
              />
              <Typography variant="body2">
                <Label
                  className={cn(
                    'cursor-pointer',
                    selectedCategories.includes(category.value.toString()) ? 'font-medium' : 'font-normal'
                  )}
                  htmlFor={category.value.toString()}
                >
                  {category.label}
                </Label>
              </Typography>
            </div>
          ))
        )}
      </div>
    </div>
  )

  // Render dynamic specification filter
  const renderSpecificationFilter = (spec: DynamicFilterSpec) => {
    if (spec.isLoading) {
      return (
        <Accordion key={spec.id} type="single" collapsible defaultValue="item-1">
          <AccordionItem value="item-1">
            <AccordionTrigger className="py-0 hover:no-underline cursor-pointer">
              <Typography variant="body1" className="text-gray-700 font-medium">
                <Trans>{spec.name}</Trans>
              </Typography>
            </AccordionTrigger>
            <AccordionContent className="py-4">
              <div className="text-gray-500">Loading...</div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      )
    }

    if (spec.type === 'range') {
      const unitLabel = spec.unit ? getUnitLabel(spec.name) : spec.name
      return (
        <Accordion key={spec.id} type="single" collapsible defaultValue="item-1">
          <AccordionItem value="item-1">
            <AccordionTrigger className="py-0 hover:no-underline cursor-pointer">
              <Typography variant="body1" className="text-gray-700 font-medium">
                <Trans>{unitLabel}</Trans>
              </Typography>
            </AccordionTrigger>
            <AccordionContent className="py-4">
              <Slider
                value={filters?.[spec.slug] || [spec.min, spec.max]}
                onValueChange={(value) => handleRangeFilter(spec.slug, value)}
                max={spec.max}
                min={spec.min}
                step={isMetric ? 1 : 0.1}
                className="w-full"
              />
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      )
    }

    if (spec.type === 'options') {
      const selectedOptions = selectedSpecificationOptions[spec.slug] || []
      return (
        <Accordion key={spec.id} type="single" collapsible defaultValue="item-1">
          <AccordionItem value="item-1">
            <AccordionTrigger className="py-0 hover:no-underline cursor-pointer">
              <Typography variant="body1" className="text-gray-700 font-medium">
                <Trans>{spec.name}</Trans>
              </Typography>
            </AccordionTrigger>
            <AccordionContent className="py-4">
              <div className="flex flex-col gap-4">
                {spec.options.map((option) => (
                  <div
                    key={option.id}
                    className="flex items-center space-x-2 cursor-pointer"
                  >
                    <CustomCheckbox
                      id={`${spec.slug}-${option.value}`}
                      checked={selectedOptions.includes(option.value.toString())}
                      onCheckedChange={(checked) =>
                        handleSpecificationOptionFilter(
                          spec.slug,
                          option.value.toString(),
                          checked as boolean,
                        )
                      }
                      className="cursor-pointer"
                    />
                    <Typography variant="body2">
                      <Label
                        className={cn(
                          'cursor-pointer',
                          selectedOptions.includes(option.value.toString()) ? 'font-medium' : 'font-normal'
                        )}
                        htmlFor={`${spec.slug}-${option.value}`}
                      >
                        {option.label}
                      </Label>
                    </Typography>
                  </div>
                ))}
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      )
    }

    return null
  }

  return (
    <div className="sticky top-4 h-[calc(100vh-2rem)]">
      <div
        className="flex flex-col h-full border border-gray-200/20 rounded-lg bg-white shadow-sm overflow-hidden"
        style={{
          overscrollBehavior: 'contain',
        }}
      >
        <div className="p-6 pb-4 flex-shrink-0">
          <Typography variant="h5" className="text-gray-800 uppercase font-bold">
            <Trans>FILTER PRODUCTS</Trans>
          </Typography>
        </div>
        <div
          className="flex-1 overflow-y-auto pr-0 pl-6 pb-6 filter-scrollbar"
          style={{
            overscrollBehavior: 'contain',
          }}
          onWheel={(e) => {
            e.stopPropagation()
          }}
        >
          <div className="flex flex-col gap-4 2xl:gap-6 pr-3">
            {renderFilterCategory}

            <Typography variant="h6" className="text-gray-800 uppercase font-semibold">
              <Trans>Specifications</Trans>
            </Typography>

            {/* Units of measure */}
            <div className="mb-4">
              <div className="flex items-center justify-between">
                <Typography variant="body1" className="text-gray-700 font-medium">
                  <Trans>Units of measure</Trans>
                </Typography>
                <div className="flex items-center space-x-2 font-semibold">
                  <button
                    className={cn(
                      'cursor-pointer px-3 py-1 text-gray-900 transition-colors',
                      !isMetric ? 'bg-primary shadow-md' : 'hover:bg-gray-50',
                    )}
                    onClick={() => setIsMetric(false)}
                  >
                    <Trans>US</Trans>
                  </button>
                  <button
                    className={cn(
                      'cursor-pointer px-3 py-1 transition-colors',
                      isMetric ? 'bg-primary shadow-md' : 'hover:bg-gray-50',
                    )}
                    onClick={() => setIsMetric(true)}
                  >
                    <Trans>METRIC</Trans>
                  </button>
                </div>
              </div>
            </div>

            {/* Dynamic Specification Filters */}
            {specifications.map((spec) => renderSpecificationFilter(spec))}
          </div>
        </div>
      </div>
    </div>
  )
}
