import { Trans } from '@lingui/react/macro'
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
  Label,
  Slider,
  Typography,
} from '@ttplatform/ui/components'
import { cn } from '@ttplatform/ui/lib'
import { useCallback, useState } from 'react'
import { CustomCheckbox } from './custom-checkbox'

//---------------------------------------------------------------------------------
const _CATEGORY_LIST = [
  {
    id: 1,
    slug: 'may-xuc-dao-mini',
    name: '<PERSON><PERSON>y xúc đào mini',
  },
  {
    id: 2,
    slug: 'may-xuc-dao-co-nho',
    name: '<PERSON><PERSON><PERSON> xúc đào cỡ nhỏ',
  },
  {
    id: 3,
    slug: 'may-xuc-dao-co-trung',
    name: '<PERSON><PERSON><PERSON> xúc cỡ trung',
  },
  {
    id: 4,
    slug: 'may-xuc-dao-co-lon',
    name: '<PERSON><PERSON><PERSON> xúc đào cỡ lớn',
  },
]

const _BORE_DIAMETER_LIST = [
  {
    id: 1,
    name: '134,5',
    slug: '134,5',
  },
]

const _EFFICIENCY_RATING_LIST = [
  {
    id: 1,
    name: '170',
    slug: '170',
  },
  {
    id: 2,
    name: '200 Micron',
    slug: '200-micron',
  },
  {
    id: 3,
    name: '590 Micron',
    slug: '590-micron',
  },
  {
    id: 4,
    name: 'Advanced Efficiency',
    slug: 'advanced-efficiency',
  },
  {
    id: 5,
    name: 'Fire Resistant',
    slug: 'fire-resistant',
  },
  {
    id: 6,
    name: 'High Efficiency',
    slug: 'high-efficiency',
  },
  {
    id: 7,
    name: 'Low Efficiency',
    slug: 'low-efficiency',
  },
  {
    id: 8,
    name: 'Not specified',
    slug: 'not-specified',
  },
  {
    id: 9,
    name: 'Standard Efficiency',
    slug: 'standard-efficiency',
  },
  {
    id: 10,
    name: 'Ultra High Efficiency',
    slug: 'ultra-high-efficiency',
  },
]

const _INSIDE_DIAMETER_LIST = [
  {
    id: 1,
    name: '6.0',
    slug: '6.0',
  },
  {
    id: 2,
    name: '8.7',
    slug: '8.7',
  },
  {
    id: 3,
    name: '8.74',
    slug: '8.74',
  },
  {
    id: 4,
    name: '9.5',
    slug: '9.5',
  },
  {
    id: 5,
    name: '9.7',
    slug: '9.7',
  },
  {
    id: 6,
    name: '10.0',
    slug: '10.0',
  },
]
//---------------------------------------------------------------------------------
type FilterBarProps = {
  filters: any
  onFilters: (name: string, value: any) => void
}
//---------------------------------------------------------------------------------
export default function FilterBar({ filters, onFilters }: FilterBarProps) {
  const [isMetric, setIsMetric] = useState(true)
  const [selectedCategories, setSelectedCategories] = useState<string[]>([])
  const [selectedBoreDiameters, setSelectedBoreDiameters] = useState<string[]>(
    [],
  )
  const [selectedEfficiencyRatings, setSelectedEfficiencyRatings] = useState<
    string[]
  >([])
  const [selectedInsideDiameters, setSelectedInsideDiameters] = useState<
    string[]
  >([])

  const handleFilterCategory = useCallback(
    (slug: string, checked: boolean) => {
      setSelectedCategories((prev) => {
        const newSelection = checked
          ? [...prev, slug]
          : prev.filter((item) => item !== slug)
        onFilters('category', newSelection)
        return newSelection
      })
    },
    [onFilters],
  )

  const handleFilterBoltLength = useCallback(
    (newValue: number | number[]) => {
      onFilters('boltLength', newValue as number[])
    },
    [onFilters],
  )

  const handleFilterBoreDiameter = useCallback(
    (slug: string, checked: boolean) => {
      setSelectedBoreDiameters((prev) => {
        const newSelection = checked
          ? [...prev, slug]
          : prev.filter((item) => item !== slug)
        onFilters('boreDiameter', newSelection)
        return newSelection
      })
    },
    [onFilters],
  )

  const handleFilterEfficiencyRating = useCallback(
    (slug: string, checked: boolean) => {
      setSelectedEfficiencyRatings((prev) => {
        const newSelection = checked
          ? [...prev, slug]
          : prev.filter((item) => item !== slug)
        onFilters('efficiencyRating', newSelection)
        return newSelection
      })
    },
    [onFilters],
  )

  const handleFilterInsideDiameter = useCallback(
    (slug: string, checked: boolean) => {
      setSelectedInsideDiameters((prev) => {
        const newSelection = checked
          ? [...prev, slug]
          : prev.filter((item) => item !== slug)
        onFilters('insideDiameter', newSelection)
        return newSelection
      })
    },
    [onFilters],
  )

  const getUnitLabel = (baseText: string) => {
    return `${baseText} (${isMetric ? 'mm' : 'in'})`
  }

  const renderFilterCategory = (
    <div className="flex flex-col gap-4 mt-2">
      <Typography
        variant="h6"
        className="text-gray-800 uppercase font-semibold"
      >
        <Trans>Category</Trans>
      </Typography>
      <div className="flex flex-col gap-4">
        {_CATEGORY_LIST.map((category: any, idx: number) => (
          <div key={idx} className="flex items-center space-x-2 cursor-pointer">
            <CustomCheckbox
              id={category?.slug}
              checked={selectedCategories.includes(category?.slug)}
              onCheckedChange={(checked) =>
                handleFilterCategory(category?.slug, checked as boolean)
              }
              className="cursor-pointer"
            />
            <Typography variant="body2">
              <Label className="cursor-pointer" htmlFor={category?.slug}>
                {category?.name}
              </Label>
            </Typography>
          </div>
        ))}
      </div>
    </div>
  )

  const renderFilterBoltLength = (
    <Accordion type="single" collapsible defaultValue="item-1">
      <AccordionItem value="item-1">
        <AccordionTrigger className="py-0 hover:no-underline cursor-pointer">
          <Typography variant="body1" className="text-gray-700 font-medium">
            <Trans>{getUnitLabel('Bolt length')}</Trans>
          </Typography>
        </AccordionTrigger>
        <AccordionContent className="py-4">
          <Slider
            value={filters?.boltLength}
            onValueChange={(value) => handleFilterBoltLength(value)}
            max={isMetric ? 500 : 20}
            step={isMetric ? 10 : 0.5}
            className="w-full"
          />
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  )

  const renderFilterBoreDiameter = (
    <Accordion type="single" collapsible defaultValue="item-1">
      <AccordionItem value="item-1">
        <AccordionTrigger className="py-0 hover:no-underline cursor-pointer">
          <Typography variant="body1" className="text-gray-700 font-medium">
            <Trans>{getUnitLabel('Bore Diameter')}</Trans>
          </Typography>
        </AccordionTrigger>
        <AccordionContent className="py-4">
          <div className="flex flex-col gap-4">
            {_BORE_DIAMETER_LIST.map((option: any, idx: number) => (
              <div
                key={idx}
                className="flex items-center space-x-2 cursor-pointer"
              >
                <CustomCheckbox
                  id={option?.slug}
                  checked={selectedBoreDiameters.includes(option?.slug)}
                  onCheckedChange={(checked) =>
                    handleFilterBoreDiameter(option?.slug, checked as boolean)
                  }
                  className="cursor-pointer"
                />
                <Typography variant="body2">
                  <Label className="cursor-pointer" htmlFor={option?.slug}>
                    {option?.name}
                  </Label>
                </Typography>
              </div>
            ))}
          </div>
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  )

  const renderFilterEfficiencyRating = (
    <Accordion type="single" collapsible defaultValue="item-1">
      <AccordionItem value="item-1">
        <AccordionTrigger className="py-0 hover:no-underline cursor-pointer">
          <Typography variant="body1" className="text-gray-700 font-medium">
            <Trans>Efficiency Rating</Trans>
          </Typography>
        </AccordionTrigger>
        <AccordionContent className="py-4">
          <div className="flex flex-col gap-4">
            {_EFFICIENCY_RATING_LIST.map((option: any, idx: number) => (
              <div
                key={idx}
                className="flex items-center space-x-2 cursor-pointer"
              >
                <CustomCheckbox
                  id={option?.slug}
                  checked={selectedEfficiencyRatings.includes(option?.slug)}
                  onCheckedChange={(checked) =>
                    handleFilterEfficiencyRating(
                      option?.slug,
                      checked as boolean,
                    )
                  }
                  className="cursor-pointer"
                />
                <Typography variant="body2">
                  <Label className="cursor-pointer" htmlFor={option?.slug}>
                    {option?.name}
                  </Label>
                </Typography>
              </div>
            ))}
          </div>
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  )

  const renderFilterInsideDiameter = (
    <Accordion type="single" collapsible defaultValue="item-1">
      <AccordionItem value="item-1">
        <AccordionTrigger className="py-0 hover:no-underline cursor-pointer">
          <Typography variant="body1" className="text-gray-700 font-medium">
            <Trans>{getUnitLabel('Inside Diameter')}</Trans>
          </Typography>
        </AccordionTrigger>
        <AccordionContent className="py-4">
          <div className="flex flex-col gap-4">
            {_INSIDE_DIAMETER_LIST.map((option: any, idx: number) => (
              <div
                key={idx}
                className="flex items-center space-x-2 cursor-pointer"
              >
                <CustomCheckbox
                  id={option?.slug}
                  checked={selectedInsideDiameters.includes(option?.slug)}
                  onCheckedChange={(checked) =>
                    handleFilterInsideDiameter(option?.slug, checked as boolean)
                  }
                  className="cursor-pointer"
                />
                <Typography variant="body2">
                  <Label className="cursor-pointer" htmlFor={option?.slug}>
                    {option?.name}
                  </Label>
                </Typography>
              </div>
            ))}
          </div>
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  )

  return (
    <div className="sticky top-4 h-[calc(100vh-2rem)]">
      <div
        className="flex flex-col h-full border border-gray-200/20 rounded-lg bg-white shadow-sm overflow-hidden"
        style={{
          overscrollBehavior: 'contain',
        }}
      >
        <div className="p-6 pb-4 flex-shrink-0">
          <Typography
            variant="h5"
            className="text-gray-800 uppercase font-bold"
          >
            <Trans>FILTER PRODUCTS</Trans>
          </Typography>
        </div>
        <div
          className="flex-1 overflow-y-auto pr-0 pl-6 pb-6 filter-scrollbar"
          style={{
            overscrollBehavior: 'contain',
          }}
          onWheel={(e) => {
            e.stopPropagation()
          }}
        >
          <div className="flex flex-col gap-4 2xl:gap-6 pr-3">
            {renderFilterCategory}

            <Typography
              variant="h6"
              className="text-gray-800 uppercase font-semibold"
            >
              <Trans>Specifications</Trans>
            </Typography>

            {/* Units of measure */}
            <div className="mb-4">
              <div className="flex items-center justify-between">
                <Typography
                  variant="body1"
                  className="text-gray-700 font-medium"
                >
                  <Trans>Units of measure</Trans>
                </Typography>
                <div className="flex items-center space-x-2 font-semibold">
                  <button
                    className={cn(
                      'cursor-pointer px-3 py-1 text-gray-900 transition-colors',
                      !isMetric ? 'bg-primary shadow-md' : 'hover:bg-gray-50',
                    )}
                    onClick={() => setIsMetric(false)}
                  >
                    <Trans>US</Trans>
                  </button>
                  <button
                    className={cn(
                      'cursor-pointer px-3 py-1 transition-colors',
                      isMetric ? 'bg-primary shadow-md' : 'hover:bg-gray-50',
                    )}
                    onClick={() => setIsMetric(true)}
                  >
                    <Trans>METRIC</Trans>
                  </button>
                </div>
              </div>
            </div>

            {renderFilterBoltLength}
            {renderFilterBoreDiameter}
            {renderFilterEfficiencyRating}
            {renderFilterInsideDiameter}
          </div>
        </div>
      </div>
    </div>
  )
}