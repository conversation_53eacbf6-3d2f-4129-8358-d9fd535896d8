import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
  Label,
  RadioGroup,
  RadioGroupItem,
  Slider,
  Typography
} from '@ttplatform/ui/components'
import { useCallback } from 'react'
//---------------------------------------------------------------------------------
const _CATEGORY_LIST = [
  {
    id: 1,
    slug: 'may-xuc-dao-mini',
    name: '<PERSON><PERSON>y xúc đào mini',
  },
  {
    id: 2,
    slug: 'may-xuc-dao-co-nho',
    name: '<PERSON><PERSON><PERSON> xúc đào cỡ nhỏ',
  },
  {
    id: 3,
    slug: 'may-xuc-dao-co-trung',
    name: '<PERSON><PERSON><PERSON> xúc cỡ trung',
  },
  {
    id: 4,
    slug: 'may-xuc-dao-co-lon',
    name: '<PERSON><PERSON><PERSON> xúc đào cỡ lớn',
  },
]

const _BORE_DIAMETER_LIST = [
  {
    id: 1,
    name: '134,5',
    slug: '134,5',
  },
]

const _EFFICIENCY_RATING_LIST = [
  {
    id: 1,
    name: '170',
    slug: '170',
  },
  {
    id: 2,
    name: '200 Micron',
    slug: '200-micron',
  },
  {
    id: 3,
    name: '590 Micron',
    slug: '590-micron',
  },
  {
    id: 4,
    name: 'Advanced Efficiency',
    slug: 'advanced-efficiency',
  },
  {
    id: 5,
    name: 'Fire Resistant',
    slug: 'fire-resistant',
  },
  {
    id: 6,
    name: 'High Efficiency',
    slug: 'high-efficiency',
  },
  {
    id: 7,
    name: 'Low Efficiency',
    slug: 'low-efficiency',
  },
  {
    id: 8,
    name: 'Not specified',
    slug: 'not-specified',
  },
  {
    id: 9,
    name: 'Standard Efficiency',
    slug: 'standard-efficiency',
  },
  {
    id: 10,
    name: 'Ultra High Efficiency',
    slug: 'ultra-high-efficiency',
  },
]

const _INSIDE_DIAMETER_LIST = [
  {
    id: 1,
    name: '6.0',
    slug: '6.0',
  },
  {
    id: 2,
    name: '8.7',
    slug: '8.7',
  },
  {
    id: 3,
    name: '8.74',
    slug: '8.74',
  },
  {
    id: 4,
    name: '9.5',
    slug: '9.5',
  },
  {
    id: 5,
    name: '9.7',
    slug: '9.7',
  },
  {
    id: 6,
    name: '10.0',
    slug: '10.0',
  },
]
//---------------------------------------------------------------------------------
type FilterBarProps = {
  filters: any
  onFilters: (name: string, value: any) => void
}
//---------------------------------------------------------------------------------
export default function FilterBar({ filters, onFilters }: FilterBarProps) {
  const handleFilterCategory = useCallback(
    (slug: string) => {
      onFilters('category', slug)
    },
    [onFilters],
  )
  const handleFilterBoltLength = useCallback(
    (newValue: number | number[]) => {
      onFilters('boltLength', newValue as number[])
    },
    [onFilters],
  )
  const handleFilterBoreDiameter = useCallback(
    (slug: string) => {
      onFilters('boreDiameter', slug)
    },
    [onFilters],
  )
  const handleFilterEfficiencyRating = useCallback(
    (slug: string) => {
      onFilters('efficiencyRating', slug)
    },
    [onFilters],
  )

  const handleFilterInsideDiameter = useCallback(
    (slug: string) => {
      onFilters('insideDiameter', slug)
    },
    [onFilters],
  )

  const renderFilterCategory = (
    <div className="flex flex-col gap-4">
      <Typography variant="h5" className="text-gray-800 uppercase">
        Category
      </Typography>
      <RadioGroup
        value={filters?.category}
        className="gap-4"
        onValueChange={handleFilterCategory}
      >
        {_CATEGORY_LIST.map((category: any, idx: number) => (
          <div key={idx} className="flex items-center space-x-2">
            <RadioGroupItem value={category?.slug} id={category?.slug} />
            <Label htmlFor={category?.slug}>{category?.name}</Label>
          </div>
        ))}
      </RadioGroup>
    </div>
  )

  const renderFilterBoltLength = (
    <Accordion type="single" collapsible>
      <AccordionItem value="item-1">
        <AccordionTrigger className="py-0 text-[18px]">
          <Typography variant="body1" className="text-gray-700 font-medium">
            Bolt length (MM)
          </Typography>
        </AccordionTrigger>
        <AccordionContent className="py-4">
          <Slider
            value={filters?.boltLength}
            onValueChange={(value) => handleFilterBoltLength(value)}
            max={500}
            step={10}
            className="w-full"
          />
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  )

  const renderFilterBoreDiameter = (
    <Accordion type="single" collapsible>
      <AccordionItem value="item-1">
        <AccordionTrigger className="py-0 text-[18px]">
          <Typography variant="body1" className="text-gray-700 font-medium">
            Bore Diameter (MM)
          </Typography>
        </AccordionTrigger>
        <AccordionContent className="py-4">
          <RadioGroup
            value={filters?.boreDiameter}
            className="gap-4"
            onValueChange={handleFilterBoreDiameter}
          >
            {_BORE_DIAMETER_LIST.map((option: any, idx: number) => (
              <div key={idx} className="flex items-center space-x-2">
                <RadioGroupItem value={option?.slug} id={option?.slug} />
                <Label htmlFor={option?.slug}>{option?.name}</Label>
              </div>
            ))}
          </RadioGroup>
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  )

  const renderFilterEfficiencyRating = (
    <Accordion type="single" collapsible>
      <AccordionItem value="item-1">
        <AccordionTrigger className="py-0 text-[18px]">
          <Typography variant="body1" className="text-gray-700 font-medium">
            Efficiency Rating
          </Typography>
        </AccordionTrigger>
        <AccordionContent className="py-4">
          <RadioGroup
            value={filters?.efficiencyRating}
            className="gap-4"
            onValueChange={handleFilterEfficiencyRating}
          >
            {_EFFICIENCY_RATING_LIST.map((option: any, idx: number) => (
              <div key={idx} className="flex items-center space-x-2">
                <RadioGroupItem value={option?.slug} id={option?.slug} />
                <Label htmlFor={option?.slug}>{option?.name}</Label>
              </div>
            ))}
          </RadioGroup>
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  )

  const renderFilterInsideDiameter = (
    <Accordion type="single" collapsible>
      <AccordionItem value="item-1">
        <AccordionTrigger className="py-0 text-[18px]">
          <Typography variant="body1" className="text-gray-700 font-medium">
            Inside Diameter (MM)
          </Typography>
        </AccordionTrigger>
        <AccordionContent className="py-4">
          <RadioGroup
            value={filters?.insideDiameter}
            className="gap-4"
            onValueChange={handleFilterInsideDiameter}
          >
            {_INSIDE_DIAMETER_LIST.map((option: any, idx: number) => (
              <div key={idx} className="flex items-center space-x-2">
                <RadioGroupItem value={option?.slug} id={option?.slug} />
                <Label htmlFor={option?.slug}>{option?.name}</Label>
              </div>
            ))}
          </RadioGroup>
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  )

  return (
    <div className="sticky top-4 h-fit max-h-[calc(100vh-2rem)]">
      <div className="flex flex-col gap-6 border border-gray-200 rounded-lg p-6 pr-0 bg-white shadow-sm">
        <Typography variant="h3" className="text-gray-800 uppercase">
          LỌC SẢN PHẨM
        </Typography>
        <div
          className="max-h-[calc(100vh-12rem)] overflow-y-auto"
        >
          <div className="flex flex-col gap-6 pr-2">
            {renderFilterCategory}

            <Typography variant="h5" className="text-gray-800 uppercase">
              Specifications
            </Typography>
            {renderFilterBoltLength}
            {renderFilterBoreDiameter}
            {renderFilterEfficiencyRating}
            {renderFilterInsideDiameter}
          </div>
        </div>
      </div>
    </div>
  )
}
