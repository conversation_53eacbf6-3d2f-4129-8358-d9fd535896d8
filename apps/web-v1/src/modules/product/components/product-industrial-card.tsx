import { CmsMedia, MainButton } from '@ttplatform/core-page-builder/components'
import { Card, CardContent, Typography } from '@ttplatform/ui/components'

type TProductIndustrialCard = {
  title: string
  description: string
  image: any
  slug?: string
}

export const ProductIndustrialCard: React.FC<TProductIndustrialCard> = ({
  title,
  description,
  image,
  slug,
}) => {
  return (
    <Card className="rounded-sm !p-0 max-w-[600px] cursor-pointer">
      <CardContent className="!p-0">
        <div className="relative w-full overflow-hidden">
          <CmsMedia
            media={image}
            className="object-cover w-full h-[388px]"
            width={600}
            height={388}
            alt={title}
          />
          <div className="absolute bottom-0 w-full bg-yellow-400 opacity-80 h-[76px]" />
          <div className="absolute bottom-0 w-full h-[76px] flex justify-between items-center px-6">
            <div className="flex flex-col">
              <Typography
                variant="h6"
                className="font-bold text-base line-clamp-2 uppercase"
              >
                {title}
              </Typography>
            </div>
            <MainButton variant="secondary" url={slug} />
          </div>
        </div>
        <div className="px-6 py-4">
          <Typography
            variant="body2"
            className="font-normal line-clamp-3 text-justify"
          >
            {description}
          </Typography>
        </div>
      </CardContent>
    </Card>
  )
}
