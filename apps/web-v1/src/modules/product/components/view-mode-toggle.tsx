import { cn } from '@ttplatform/ui/lib'
import { Columns2, LayoutGrid } from 'lucide-react'

type ViewModeToggleProps = {
  value: string
  onChange: (value: string) => void
  iconSize?: number
  buttonSize?: number
}

export function ViewModeToggle({
  value,
  onChange,
  iconSize = 20,
  buttonSize = 20,
}: ViewModeToggleProps) {
  return (
    <div className="flex gap-4">
      <button
        onClick={() => onChange('grid')}
        className={cn(
          'flex items-center justify-center rounded-md transition-colors cursor-pointer',
          value === 'grid'
            ? 'text-gray-800'
            : 'text-gray-500 hover:text-gray-700',
        )}
        style={{ width: buttonSize, height: buttonSize }}
      >
        <LayoutGrid size={iconSize} />
      </button>
      <button
        onClick={() => onChange('table')}
        className={cn(
          'flex items-center justify-center rounded-md transition-colors cursor-pointer',
          value === 'table'
            ? 'text-gray-800'
            : 'text-gray-500 hover:text-gray-700',
        )}
        style={{ width: buttonSize, height: buttonSize }}
      >
        <Columns2 size={iconSize} />
      </button>
    </div>
  )
}
