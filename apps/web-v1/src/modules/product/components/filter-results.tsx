import { t } from '@lingui/core/macro'
import { Trans } from '@lingui/react/macro'
import { SectionTitle } from '@ttplatform/core-page-builder/components'
import {
  Checkbox,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  Typography,
} from '@ttplatform/ui/components'
import { cn } from '@ttplatform/ui/lib'
import { useCallback } from 'react'

import { HexagonButton } from '@ttplatform/core-page-builder/components'
import { CardProduct } from '~/src/components/card/card-product'
import { ViewModeToggle } from './view-mode-toggle'
//---------------------------------------------------------------------------------
const __LIST_PRODUCT = [
  {
    name: '320 GX',
    image: '/images/products/may-cong-trinh.png',
    cong_suat: '104 kw',
    dung_tich_gau: '1.2 m3',
    khoi_luong_van_hanh: '20500kg',
  },
  {
    name: '320 GX',
    image: '/images/products/may-cong-trinh.png',
    cong_suat: '104 kw',
    dung_tich_gau: '1.2 m3',
    khoi_luong_van_hanh: '20500kg',
  },
  {
    name: '320 GX',
    image: '/images/products/may-cong-trinh.png',
    cong_suat: '104 kw',
    dung_tich_gau: '1.2 m3',
    khoi_luong_van_hanh: '20500kg',
  },
  {
    name: '320 GX',
    image: '/images/products/may-cong-trinh.png',
    cong_suat: '104 kw',
    dung_tich_gau: '1.2 m3',
    khoi_luong_van_hanh: '20500kg',
  },
  {
    name: '320 GX',
    image: '/images/products/may-cong-trinh.png',
    cong_suat: '104 kw',
    dung_tich_gau: '1.2 m3',
    khoi_luong_van_hanh: '20500kg',
  },
  {
    name: '320 GX',
    image: '/images/products/may-cong-trinh.png',
    cong_suat: '104 kw',
    dung_tich_gau: '1.2 m3',
    khoi_luong_van_hanh: '20500kg',
  },
  {
    name: '320 GX',
    image: '/images/products/may-cong-trinh.png',
    cong_suat: '104 kw',
    dung_tich_gau: '1.2 m3',
    khoi_luong_van_hanh: '20500kg',
  },
  {
    name: '320 GX',
    image: '/images/products/may-cong-trinh.png',
    cong_suat: '104 kw',
    dung_tich_gau: '1.2 m3',
    khoi_luong_van_hanh: '20500kg',
  },
  {
    name: '320 GX',
    image: '/images/products/may-cong-trinh.png',
    cong_suat: '104 kw',
    dung_tich_gau: '1.2 m3',
    khoi_luong_van_hanh: '20500kg',
  },
  {
    name: '320 GX',
    image: '/images/products/may-cong-trinh.png',
    cong_suat: '104 kw',
    dung_tich_gau: '1.2 m3',
    khoi_luong_van_hanh: '20500kg',
  },
  {
    name: '320 GX',
    image: '/images/products/may-cong-trinh.png',
    cong_suat: '104 kw',
    dung_tich_gau: '1.2 m3',
    khoi_luong_van_hanh: '20500kg',
  },
  {
    name: '320 GX',
    image: '/images/products/may-cong-trinh.png',
    cong_suat: '104 kw',
    dung_tich_gau: '1.2 m3',
    khoi_luong_van_hanh: '20500kg',
  },
]

const _TABLE_HEAD = [
  {
    name: 'Compare',
    className: 'max-w-max p-0',
  },
  {
    name: 'Model',
    className: 'w-[40%] md:w-[50%] p-0',
  },
  {
    name: 'Effective Power',
    className: 'w-[40%] md:w-[50%] p-0',
  },
  {
    name: 'Bucket Capacity',
    className: 'w-[40%] md:w-[50%] p-0',
  },
  {
    name: 'Operating Weight',
    className: 'w-[40%] md:w-[50%] p-0',
  },
]
//---------------------------------------------------------------------------------
type IProps = {
  filters: any
  onFilters: (name: string, value: any) => void
}
export default function FilterResults({ filters, onFilters }: IProps) {
  const handleChangeDisplayMode = useCallback(
    (value: string) => {
      onFilters('displayMode', value)
    },
    [onFilters],
  )

  const renderDisplayMode = (
    <div className="flex items-center gap-4 whitespace-nowrap">
      <Typography variant="body2" className="text-gray-700 font-medium">
        <Trans>Display Mode:</Trans>
      </Typography>
      <ViewModeToggle
        value={filters?.displayMode}
        onChange={handleChangeDisplayMode}
      />
    </div>
  )

  return (
    <div className="flex flex-col gap-4">
      <div className="flex items-center justify-between">
        <SectionTitle text={t`PRODUCT MODELS`} align="left" />
        {renderDisplayMode}
      </div>
      {/* Grid */}
      {filters?.displayMode === 'grid' ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-x-6 gap-y-10">
          {__LIST_PRODUCT.map((item: any, idx: number) => (
            <CardProduct key={idx} product={item} />
          ))}
        </div>
      ) : null}
      {/* Table */}
      {filters?.displayMode === 'table' ? (
        <Table className="border rounded-2xl">
          <TableHeader className="bg-[#FFCC00]">
            <TableRow>
              {_TABLE_HEAD.map((item: any, idx: number) => (
                <TableHead key={idx} className={cn('p-0', item?.className)}>
                  <Typography
                    variant={'body2'}
                    className="text-[#182230] font-medium px-4 md:px-6 py-3"
                  >
                    <Trans>{item?.name}</Trans>
                  </Typography>
                </TableHead>
              ))}
              <TableHead className="w-[40%] md:w-[50%] p-0"></TableHead>
            </TableRow>
          </TableHeader>

          <TableBody>
            {__LIST_PRODUCT.map((item: any, idx: number) => (
              <TableRow key={idx}>
                <TableCell className="px-4 md:px-6 py-3 md:py-4">
                  <Checkbox className="data-[state=checked]:text-black" />
                </TableCell>
                <TableCell className="px-4 md:px-6 py-3 md:py-4">
                  <Typography variant={'body2'} className="text-gray-800">
                    {item?.name}
                  </Typography>
                </TableCell>
                <TableCell className="px-4 md:px-6 py-3 md:py-4">
                  <Typography variant={'body2'} className="text-gray-800">
                    {item?.cong_suat}
                  </Typography>
                </TableCell>
                <TableCell className="px-4 md:px-6 py-3 md:py-4">
                  <Typography variant={'body2'} className="text-gray-800">
                    {item?.dung_tich_gau}
                  </Typography>
                </TableCell>
                <TableCell className="px-4 md:px-6 py-3 md:py-4">
                  <Typography variant={'body2'} className="text-gray-800">
                    {item?.khoi_luong_van_hanh}
                  </Typography>
                </TableCell>
                <TableCell className="px-4 md:px-6 py-3 md:py-4">
                  <HexagonButton />
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      ) : null}
    </div>
  )
}
