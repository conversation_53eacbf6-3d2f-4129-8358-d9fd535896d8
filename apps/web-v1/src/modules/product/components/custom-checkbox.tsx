import * as CheckboxPrimitive from '@radix-ui/react-checkbox'
import { cn } from '@ttplatform/ui/lib'
import { CheckIcon } from 'lucide-react'
import type * as React from 'react'

function CustomCheckbox({
  className,
  ...props
}: React.ComponentProps<typeof CheckboxPrimitive.Root>) {
  return (
    <CheckboxPrimitive.Root
      className={cn(
        'peer h-4 w-4 shrink-0 rounded-[4px] border border-gray-400 shadow-xs transition-shadow outline-none focus-visible:ring-2 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-[#FFCC00] data-[state=checked]:border-[#FFCC00]',
        className,
      )}
      {...props}
    >
      <CheckboxPrimitive.Indicator className="flex items-center justify-center text-gray-800">
        <CheckIcon className="h-3.5 w-3.5" />
      </CheckboxPrimitive.Indicator>
    </CheckboxPrimitive.Root>
  )
}

export { CustomCheckbox }
