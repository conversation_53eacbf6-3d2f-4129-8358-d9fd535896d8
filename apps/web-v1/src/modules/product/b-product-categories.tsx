'use client'

import { t } from '@lingui/core/macro'
import { CmsMedia, MainButton } from '@ttplatform/core-page-builder/components'
import { Typography } from '@ttplatform/ui/components'
import Image from 'next/image'

type TProps = {
  categories?: any[]
}

export function BProductCategories({ categories }: TProps) {
  const categoriesData = categories && categories.length > 0 ? categories : []

  return (
    <section className="my-10 2xl:my-16">
      <div className="w-full mx-auto">
        <div
          className="flex flex-col p-5 sm:p-10 lg:p-20 gap-10 md:gap-16 rounded-2xl bg-[#FAFAFA] border border-gray-200/20"
          style={{
            boxShadow:
              '0px 4px 8px -2px rgba(16, 24, 40, 0.10), 0px 2px 4px -2px rgba(16, 24, 40, 0.06)',
          }}
        >
          {categoriesData?.map((category, idx: number) => (
            <div
              key={idx}
              className="flex flex-col md:flex-row items-center gap-x-10 group md:odd:flex-row-reverse"
            >
              <div className="w-full md:w-1/2 flex flex-col gap-8">
                <Image
                  src={'/icons/icon-hexagon.svg'}
                  alt="category icon"
                  width={50}
                  height={40}
                />
                <div className="block md:hidden w-full h-auto aspect-[700/466] relative">
                  {category?.image && typeof category.image === 'object' ? (
                    <CmsMedia
                      media={category.image}
                      format="medium"
                      ratio={700 / 466}
                      className="rounded-2xl"
                      fallbackImageUrl={category?.image}
                    />
                  ) : (
                    <div
                      className="w-full h-full absolute top-0 left-0 rounded-2xl group-hover:scale-105 md:group-hover:scale-110 ease-in-out transition-all duration-500"
                      style={{
                        backgroundImage: `url(${category?.image})`,
                        backgroundRepeat: 'no-repeat',
                        backgroundSize: 'cover',
                        backgroundPosition: 'center',
                      }}
                    ></div>
                  )}
                </div>
                <div className="flex flex-col gap-2">
                  <Typography
                    variant={'h2'}
                    className="relative pb-4 text-gray-800 uppercase before:absolute before:bottom-0  before:content-[''] before:w-[80px] before:h-[8px] before:bg-[#FFCC00] before:left-0"
                  >
                    {category?.name || category?.title}
                  </Typography>
                  <Typography variant={'body1'} className="text-gray-700 m-0">
                    {category?.excerpt || category?.description}
                  </Typography>
                </div>
                <MainButton
                  variant="secondary"
                  isDisabledIcon={true}
                  label={t`View Details`}
                  url={`/product-categories/${category?.slug || '#'}`}
                />
              </div>
              <div className="hidden md:block md:w-1/2 h-auto aspect-[700/466] relative">
                {category?.image && typeof category.image === 'object' ? (
                  <CmsMedia
                    media={category.image}
                    format="large"
                    ratio={700 / 466}
                    className="rounded-2xl"
                    fallbackImageUrl={category?.image}
                  />
                ) : (
                  <div
                    className="w-full h-full absolute top-0 left-0 rounded-2xl group-hover:scale-110 ease-in-out transition-all duration-500"
                    style={{
                      backgroundImage: `url(${category?.image})`,
                      backgroundRepeat: 'no-repeat',
                      backgroundSize: 'cover',
                      backgroundPosition: 'center',
                    }}
                  ></div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
