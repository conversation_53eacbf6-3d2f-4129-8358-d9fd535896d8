import { t } from '@lingui/core/macro'
import { ECardType } from '@ttplatform/core-page-builder/libs'
import { Typography } from '@ttplatform/ui/components'
import { cn } from '@ttplatform/ui/lib'
import React from 'react'
import { CompactCardSkeleton } from '~/src/components/ui'
import PostCompactCard from './post-compact-card'

const PostCompactList: React.FC<{
  headerTitle: string
  postList: any[]
  className?: string
  type?: ECardType
  isLoading?: boolean
  error?: any
}> = ({ headerTitle, postList, className, type, isLoading, error }) => {
  const notFound = !isLoading && error && !postList.length
  return (
    <div
      className={cn(
        'bg-gray-50/80 shadow-sm rounded-lg  overflow-hidden',
        className,
      )}
    >
      <div className="p-6">
        <Typography variant="h4" className="uppercase">
          {headerTitle}
        </Typography>
        <div className="mt-4 flex flex-col gap-6">
          {isLoading ? (
            <div className="w-full h-full flex flex-col gap-4">
              {Array.from({ length: 3 }).map((_, index) => (
                <CompactCardSkeleton key={index} />
              ))}
            </div>
          ) : (
            <>
              {notFound ? (
                <EmptyContent title={t`No data`} className="text-center" />
              ) : (
                <>
                  {postList.map((post) => (
                    <PostCompactCard
                      key={post.id}
                      card={post}
                      type={type || ECardType.News}
                    />
                  ))}
                </>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  )
}

const EmptyContent = ({
  title,
  className,
}: { title: string; className?: string }) => {
  return (
    <div
      className={cn(
        'flex flex-col items-center justify-center min-h-[300px] sm:min-h-[600px] text-center',
        className,
      )}
    >
      {title && <p className="text-gray-600 text-sm sm:text-lg">{title}</p>}
    </div>
  )
}

export default PostCompactList
