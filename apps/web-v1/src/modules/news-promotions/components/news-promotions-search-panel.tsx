'use client'

import { t } from '@lingui/core/macro'
import { MainButton } from '@ttplatform/core-page-builder/components'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  Input,
} from '@ttplatform/ui/components'
import { ArrowDownUp, ChevronDown, SearchIcon } from 'lucide-react'
import { useState } from 'react'
import { SubmitHandler, useForm } from 'react-hook-form'

// Define type for sort options

interface PostSearchPanelProps {
  onFilter?: (name: string, value: any) => void
  sort?: string | string[]
  onSort?: (sort: string | string[]) => void
  className?: string
}

type TSearchProps = {
  search: string
}

export default function NewsPromotionsSearchPanel({
  onFilter,
  onSort,
  sort,
  // className,
}: PostSearchPanelProps) {
  const [searchKeyword, setSearchKeyword] = useState('')
  const { register, handleSubmit } = useForm<TSearchProps>()
  const handleSearch: SubmitHandler<TSearchProps> = (data) => {
    setSearchKeyword(data.search)
    onFilter?.('$or', [
      { title: { $containsi: data.search } },
      { description: { $containsi: data.search } },
    ])
  }

  return (
    <div className="flex items-center justify-between space-x-2 sm:space-x-4 ">
      {/* Search input */}
      <form
        className="relative flex-1 w-full max-w-[638px]"
        onSubmit={handleSubmit(handleSearch)}
      >
        <Input
          {...register('search')}
          defaultValue={searchKeyword}
          type="text"
          placeholder={t`Search by title or content`}
          className="w-full rounded-md border border-gray-300 pl-9 pr-10 py-2 h-12 text-sm sm:text-base"
        />
        <SearchIcon
          className="absolute left-3 top-1/2 transform -translate-y-1/2"
          size={16}
        />
        <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
          <MainButton type="submit" />
        </div>
      </form>

      {/* Sort dropdown */}
      <DropdownMenu>
        <DropdownMenuTrigger className="flex items-center justify-center space-x-1 text-gray-700 border border-gray-300 rounded-md px-2 sm:px-3 py-1 sm:py-2 h-10 sm:h-12 w-10 sm:w-fit text-sm sm:text-base">
          <ArrowDownUp className="w-4 h-4 aspect-square flex-shrink-0" />
          <p className="hidden sm:block">
            <strong>{t`Sort`}:</strong>{' '}
            {sort === 'publishedAt:desc' ? t`Newest` : t`Oldest`}
          </p>
          <ChevronDown className="w-4 h-4 aspect-square flex-shrink-0 hidden sm:block" />
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-40 sm:w-52 text-sm sm:text-base">
          <DropdownMenuItem onClick={() => onSort?.('publishedAt:desc')}>
            {t`Newest`}
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => onSort?.('publishedAt:asc')}>
            {t`Oldest`}
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  )
}
