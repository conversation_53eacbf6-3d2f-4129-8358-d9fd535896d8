'use client'

import { t } from '@lingui/core/macro'
import {
  Button,
  Calendar as CalendarComponent,
  Checkbox,
  Input,
  Label,
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@ttplatform/ui/components'
import { cn } from '@ttplatform/ui/lib'
import { format } from 'date-fns'
import { Calendar } from 'lucide-react'
import { useRouter, useSearchParams } from 'next/navigation'
import { useCallback, useEffect, useState } from 'react'
import { useGetArticlesCategory } from '~/src/libs/cms/strapi/use-articles'
import { useGetPromotionsCategory } from '~/src/libs/cms/strapi/use-promotions'
import { useLocale } from '~/src/libs/data/use-locale'

type TProps = {
  type: 'news' | 'promotions'
  onFilter?: (name: string, value: any) => void
}

export default function NewsPromotionsFilterBar({ type, onFilter }: TProps) {
  const router = useRouter()
  const searchParams = useSearchParams()
  const locale = useLocale()
  const { data: promotionsCategories } = useGetPromotionsCategory({
    filters: {
      locale,
    },
  })
  const { data: newsCategories } = useGetArticlesCategory({
    filters: {
      locale,
    },
  })

  const categories =
    type === 'promotions' ? promotionsCategories?.data : newsCategories?.data

  const [selectedCategories, setSelectedCategories] = useState<string[]>([
    'all',
  ])
  const [fromDate, setFromDate] = useState<Date | undefined>(undefined)
  const [toDate, setToDate] = useState<Date | undefined>(undefined)
  const [validUntil, setValidUntil] = useState<boolean>(false)

  useEffect(() => {
    const categoriesFromQuery = searchParams.get('categories')?.split(',') || [
      'all',
    ]
    const fromDateFromQuery = searchParams.get('fromDate')
    const toDateFromQuery = searchParams.get('toDate')
    const validUntilFromQuery = searchParams.get('validUntil')
    setSelectedCategories(categoriesFromQuery)
    setFromDate(fromDateFromQuery ? new Date(fromDateFromQuery) : undefined)
    setToDate(toDateFromQuery ? new Date(toDateFromQuery) : undefined)
    setValidUntil(validUntilFromQuery === 'true')
  }, [searchParams])

  // Update query parameters when filters change - memoize this function
  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  const updateQueryParams = useCallback(() => {
    const params = new URLSearchParams()

    // Add categories to query params
    if (selectedCategories.length > 0 && !selectedCategories.includes('all')) {
      params.set('categories', selectedCategories.join(','))
      const categoryIds = selectedCategories.map((id) => Number(id))
      onFilter?.('category', {
        id: {
          $in: categoryIds,
        },
      })
    } else {
      onFilter?.('category', undefined)
    }

    if (fromDate) {
      params.set('fromDate', format(fromDate, 'yyyy-MM-dd'))
      onFilter?.('publishedAt', {
        $gte: fromDate,
        $lte: toDate ? toDate : undefined,
      })
    } else {
      onFilter?.('publishedAt', undefined)
    }
    if (toDate) {
      params.set('toDate', format(toDate, 'yyyy-MM-dd'))
      onFilter?.('publishedAt', {
        $gte: fromDate ? fromDate : undefined,
        $lte: toDate,
      })
    } else {
      onFilter?.('publishedAt', undefined)
    }

    if (validUntil) {
      params.set('validUntil', 'true')
      onFilter?.('valid_until', {
        $gte: new Date(),
      })
    } else {
      onFilter?.('valid_until', undefined)
    }

    // Update URL
    router.push(`?${params.toString()}`, { scroll: false })
  }, [selectedCategories, fromDate, toDate, router, validUntil, onFilter])

  useEffect(() => {
    updateQueryParams()
  }, [updateQueryParams])
  // Handle category change
  const handleCategoryChange = useCallback(
    (categoryId: string, checked: boolean) => {
      if (categoryId === 'all') {
        setSelectedCategories(checked ? ['all'] : [])
      } else {
        setSelectedCategories((prevCategories) => {
          let newCategories = [...prevCategories]
          if (checked) {
            newCategories = [
              ...newCategories.filter((id) => id !== 'all'),
              categoryId,
            ]
          } else {
            newCategories = newCategories.filter((id) => id !== categoryId)
          }
          // If no categories are selected, default to 'all'
          return newCategories.length > 0 ? newCategories : ['all']
        })
      }
    },
    [],
  )

  return (
    <div className="rounded-2xl border p-6 shadow-sm space-y-6 bg-white w-full max-w-sm">
      <h2 className="text-2xl font-bold mb-4">
        {t`Filter`} {type === 'promotions' ? t`Promotions` : t`News`}
      </h2>

      <div className="space-y-6">
        {/* Categories */}
        <div>
          <h3 className="text-lg font-medium mb-4">{t`Category`}</h3>
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Checkbox
                id="all"
                className="data-[state=checked]:!text-[#182230]"
                checked={selectedCategories.includes('all')}
                onCheckedChange={(checked) =>
                  handleCategoryChange('all', checked === true)
                }
              />
              <Label
                htmlFor="all"
                className={cn(
                  'text-base font-normal cursor-pointer',
                  selectedCategories.includes('all') ? 'font-semibold' : '',
                )}
              >
                {t`All`} {type === 'promotions' ? t`Promotions` : t`News`}
              </Label>
            </div>
            {categories?.map((category: any) => (
              <div key={category.id} className="flex items-center gap-2">
                <Checkbox
                  className="data-[state=checked]:!text-[#182230]"
                  id={category.id}
                  checked={selectedCategories.includes(category.id.toString())}
                  onCheckedChange={(checked) =>
                    handleCategoryChange(
                      category.id.toString(),
                      checked === true,
                    )
                  }
                />
                <Label
                  htmlFor={category.id}
                  className={cn(
                    'text-base font-normal cursor-pointer',
                    selectedCategories.includes(category.id)
                      ? 'font-semibold'
                      : '',
                  )}
                >
                  {category.title}
                </Label>
              </div>
            ))}
          </div>
        </div>

        {type === 'news' ? (
          <div>
            <h3 className="text-base font-medium mb-2">{t`Published Date`}</h3>
            <div className="grid grid-cols-2 gap-4">
              <Popover>
                <PopoverTrigger asChild>
                  <div className="relative">
                    <Input
                      placeholder={t`From`}
                      value={fromDate ? format(fromDate, 'dd/MM/yyyy') : ''}
                      readOnly
                      className="pr-10"
                    />
                    <Button
                      variant="ghost"
                      size="icon"
                      className="absolute right-0 top-0 h-full"
                    >
                      <Calendar className="h-4 w-4 text-gray-500" />
                    </Button>
                  </div>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <CalendarComponent
                    mode="single"
                    selected={fromDate}
                    onSelect={setFromDate}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>

              {/* To Date */}
              <Popover>
                <PopoverTrigger asChild>
                  <div className="relative">
                    <Input
                      placeholder={t`To`}
                      value={toDate ? format(toDate, 'dd/MM/yyyy') : ''}
                      readOnly
                      className="pr-10"
                    />
                    <Button
                      variant="ghost"
                      size="icon"
                      className="absolute right-0 top-0 h-full"
                    >
                      <Calendar className="h-4 w-4 text-gray-500" />
                    </Button>
                  </div>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <CalendarComponent
                    mode="single"
                    selected={toDate}
                    onSelect={setToDate}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>
        ) : (
          <div className="flex items-center gap-2">
            <Checkbox
              id="all"
              className="data-[state=checked]:!text-[#182230]"
              checked={validUntil}
              onCheckedChange={(checked) => {
                setValidUntil(checked === true)
              }}
            />
            <Label
              htmlFor="all"
              className={cn('text-base font-normal cursor-pointer')}
            >
              {t`Only show`} {type === 'promotions' ? t`Promotions` : t`News`}{' '}
              {t`with effective date`}
            </Label>
          </div>
        )}
      </div>
    </div>
  )
}
