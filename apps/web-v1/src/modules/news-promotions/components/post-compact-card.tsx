'use client'
import { t } from '@lingui/core/macro'
import {
  CalendarIcon,
  CmsMedia,
} from '@ttplatform/core-page-builder/components'
import {
  ECardType,
  INewsCard,
  TPromotionSchema,
} from '@ttplatform/core-page-builder/libs'
import { Typography } from '@ttplatform/ui/components'
import { cn } from '@ttplatform/ui/lib'
import { format } from 'date-fns'
import { motion } from 'framer-motion'
import Link from 'next/link'
import React from 'react'

// Animation variants for consistency with PostCardLarge
const cardVariants = {
  initial: { opacity: 0, y: 10 },
  animate: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.4,
    },
  },
}

const imageVariants = {
  initial: { scale: 1 },
  hover: { scale: 1.03, transition: { duration: 0.3 } },
}

const PostCompactCard: React.FC<{
  card: any
  className?: string
  type: ECardType
}> = ({ card, className, type }) => {
  const { image, title, slug } = card
  const getCardUrl = () => {
    if (type === ECardType.Promotion) {
      return `/promotions/${slug}`
    }
    if (type === ECardType.News) {
      return `/news/${slug}`
    }
    return slug ? slug : '#'
  }

  const renderMetadata = () => {
    switch (type) {
      case ECardType.Promotion: {
        const promotion = card as TPromotionSchema
        return (
          <div className="flex items-center justify-between text-sm font-semibold text-red-700">
            <div className="flex font-medium text-gray-600">
              <CalendarIcon className="mr-2" />
              <span>{t`Valid until`}</span>
            </div>
            <span>
              {promotion.valid_until
                ? format(new Date(promotion.valid_until), 'dd MMMM yyyy')
                : 'No date'}
            </span>
          </div>
        )
      }
      case ECardType.News: {
        const news = card as INewsCard
        return (
          <div className="flex flex-col gap-1 sm:items-center sm:flex-row justify-between font-semibold text-sm w-full">
            <Typography
              variant="caption"
              className="text-orange-400 font-medium"
            >
              {news.category?.title || 'Category'}
            </Typography>
            <div className="flex items-center text-red-700">
              <CalendarIcon className="mr-1" />
              <Typography variant="caption">
                {news.publishedAt
                  ? format(new Date(news.publishedAt), 'dd MMMM yyyy')
                  : ''}
              </Typography>
            </div>
          </div>
        )
      }
      default:
        return null
    }
  }

  return (
    <Link href={getCardUrl()}>
      <motion.div
        variants={cardVariants}
        initial="initial"
        animate="animate"
        whileHover="hover"
        className={cn('flex gap-2', className)}
      >
        <div className="w-28 h-20 sm:h-18 flex-shrink-0 relative overflow-hidden rounded-sm">
          <motion.div variants={imageVariants} className="absolute inset-0">
            <CmsMedia
              media={image}
              className="object-cover w-full h-full"
              contentClassName="w-full h-full rounded-sm"
              width={200}
              height={200}
            />
          </motion.div>
        </div>
        <div className="flex flex-col justify-between flex-grow">
          {renderMetadata()}
          <Typography
            variant="body2"
            className="font-medium text-sm sm:text-base line-clamp-2 mt-2"
          >
            {title}
          </Typography>
        </div>
      </motion.div>
    </Link>
  )
}

export default PostCompactCard
