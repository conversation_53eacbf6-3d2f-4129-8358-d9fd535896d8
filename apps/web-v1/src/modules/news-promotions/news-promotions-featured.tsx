'use client'

import { cn } from '@ttplatform/ui/lib'
import { AnimatePresence, motion } from 'framer-motion'
import { useEffect, useRef, useState } from 'react'

import { t } from '@lingui/core/macro'
import {
  CmsMedia,
  LayoutContainer,
} from '@ttplatform/core-page-builder/components'
import {
  TArticleSchema,
  TPromotionSchema,
} from '@ttplatform/core-page-builder/libs'
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
  Button,
} from '@ttplatform/ui/components'
import { EmptyContent } from '@ttplatform/ui/templates'
import { CalendarIcon } from 'lucide-react'
import Link from 'next/link'
import { fDate } from '~/src/utils'

type TProps = {
  type: 'news' | 'promotions'
  items: TArticleSchema[] | TPromotionSchema[]
  notFound?: boolean
}

const _OPTIONS = [
  {
    type: 'news',
    label: t`News`,
    url: '/news',
  },
  {
    type: 'promotions',
    label: t`Promotions`,
    url: '/promotions',
  },
]

type TContent = TArticleSchema | TPromotionSchema

const FeaturedNewsPromotions = ({ items, type, notFound }: TProps) => {
  const contentRef = useRef<HTMLDivElement>(null)

  const [activeItem, setActiveItem] = useState<
    TArticleSchema | TPromotionSchema | null
  >(items?.[0] || null)

  const transition = {
    duration: 0.3,
    delay: 0.1,
    ease: [0, 0.71, 0.2, 1.01],
  }

  useEffect(() => {
    if (!items || items.length === 0) return
    setActiveItem(items?.[0] || null)
  }, [items])

  if (!items || items.length === 0) {
    return <EmptyContent title={t`No data`} />
  }

  const CategoryMapping: Record<string, React.ReactNode> = {
    promotions: (
      <div className="flex font-medium text-base text-gray-600">
        <CalendarIcon className="mr-2" />
        <span>{t`Valid until`}</span>
      </div>
    ),
    news: <span className="text-yellow-500">{t`Category`}</span>,
  }

  const renderCardContent = (item: TContent) => {
    return (
      <div className="flex justify-between items-center text-base font-semibold">
        {CategoryMapping[type] || null}
        <span className="mt-1 text-base text-red-700">
          {fDate(item.publishedAt)}
        </span>
      </div>
    )
  }

  const renderButtons = (
    <div className="flex items-center gap-2 justify-center">
      {_OPTIONS.map((itm) => (
        <Link key={itm.url} href={itm.url} prefetch={true}>
          <Button
            key={itm.url}
            variant="ghost"
            className={cn(
              'rounded-full cursor-pointer text-black uppercase font-bold',
              'w-full min-w-auto md:min-w-72',
              itm.type === type ? 'bg-primary ' : 'bg-gray-50',
            )}
          >
            {itm.label}
          </Button>
        </Link>
      ))}
    </div>
  )

  const renderFeaturedList = (
    <div className="flex flex-col md:grid w-full md:grid-cols-[1.15fr_1fr] overflow-hidden min-h-[300px] sm:min-h-[400px] md:min-h-[600px] mt-10">
      <div className="w-full relative overflow-hidden rounded-xl h-[200px] sm:h-[300px] md:h-auto">
        <AnimatePresence>
          <motion.div
            key={activeItem?.id}
            initial={{ opacity: 0, scale: 1.05 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 1 }}
            transition={transition as any}
            className="w-full h-full absolute"
          >
            <CmsMedia
              media={activeItem?.image}
              className="w-full h-full object-cover rounded-xl"
              sizes="(max-width: 640px) 100px, (max-width: 768px) 120px, 400px"
              fill
              hoverAnimation={false}
              link={`/${type}/${activeItem?.slug}`}
            />
          </motion.div>
        </AnimatePresence>
      </div>
      <div className="w-full flex items-center justify-center" ref={contentRef}>
        <div className="bg-gray-50/80 rounded-r-xl h-[calc(100%-24px)] w-full shadow-sm p-4 sm:p-6">
          <Accordion
            type="single"
            value={`item-${activeItem?.id || ''}`}
            className="w-full"
          >
            {items.map((item, index) => (
              <AccordionItem
                key={index}
                value={`item-${item.id}`}
                className="pb-2 sm:pb-4 xl:pr-8 3xl:pr-12 border-none"
                onMouseEnter={(e) => {
                  e.preventDefault()
                  e.stopPropagation()
                  setActiveItem(item || '')
                }}
              >
                <Link href={`/${type}/${item.slug}`}>
                  <div
                    className={cn(
                      index !== items.length - 1
                        ? 'border-b border-gray-100'
                        : '',
                    )}
                  >
                    <AccordionTrigger className="text-lg sm:text-xl lg:text-2xl font-bold text-gray-900 hover:no-underline uppercase">
                      {item.title}
                    </AccordionTrigger>
                    <AccordionContent>
                      <p className="text-gray-600 text-sm sm:text-base my-2 sm:my-4 text-justify line-clamp-3">
                        {item.description}
                      </p>
                      {renderCardContent(item)}
                    </AccordionContent>
                  </div>
                </Link>
              </AccordionItem>
            ))}
          </Accordion>
        </div>
      </div>
    </div>
  )

  return (
    <LayoutContainer>
      {renderButtons}
      {notFound ? <EmptyContent title={t`No data`} /> : renderFeaturedList}
    </LayoutContainer>
  )
}

export default FeaturedNewsPromotions
