'use client'
import {
  CmsMedia,
  PostCardLarge,
} from '@ttplatform/core-page-builder/components'
import {
  ECardType,
  TBannerAdsSchema,
  THeadingSchema,
} from '@ttplatform/core-page-builder/libs'
import { Button, Typography } from '@ttplatform/ui/components'
import { Loader2 } from 'lucide-react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { useEffect, useState } from 'react'

import { t } from '@lingui/core/macro'
import { EmptyContent } from '@ttplatform/ui/templates'
import {
  NewsPromotionsFilterBar,
  NewsPromotionsSearchPanel,
} from './components'

type TProps = {
  heading: THeadingSchema
  data: any
  banner_ads?: any
  paginationLimit: number
  isLoading: boolean
  error: any
  handleLoadMore: () => void
  sort?: string | string[]
  handleSort?: (sort: string | string[]) => void
  handleFilter?: (name: string, value: any) => void
  filter?: any
}

const NewsPromotionListing = ({
  data,
  banner_ads,
  paginationLimit,
  isLoading,
  error,
  handleLoadMore,
  sort,
  handleSort,
  handleFilter,
}: TProps) => {
  const [promotionList, setPromotionList] = useState<any[]>([])

  // use client
  // TODO: FIXME
  // @hao
  const pathName = usePathname()
  const type = pathName.split('/')[1]

  useEffect(() => {
    if (data?.data) {
      setPromotionList(data?.data)
    }
  }, [data?.data])

  const hasMore = data?.meta?.pagination?.total > paginationLimit

  const notFound = !isLoading && (error || !data?.data?.length)

  return (
    <section className="w-full grid grid-cols-1 gap-y-10 lg:grid-cols-12 lg:gap-x-6">
      <aside className="md:col-span-4 xl:col-span-3 flex flex-col w-full">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-1 gap-6">
          <NewsPromotionsFilterBar
            type={type as 'news' | 'promotions'}
            onFilter={handleFilter}
          />

          {banner_ads &&
            banner_ads.length > 0 &&
            banner_ads.map((item: TBannerAdsSchema, index: number) => (
              <Link key={index} href={item.link.url} target={item.link.target}>
                <CmsMedia
                  media={item.image}
                  className="w-full h-full !object-cover"
                  contentClassName="w-full h-full rounded-lg overflow-hidden"
                />
              </Link>
            ))}
        </div>
      </aside>
      <div className="md:col-span-8 xl:col-span-9 w-full ">
        <div className="mb-4">
          <NewsPromotionsSearchPanel
            sort={sort}
            onSort={handleSort}
            onFilter={handleFilter}
          />
        </div>
        {isLoading ? (
          <div className="w-full h-full flex justify-center">
            <Loader2 className="w-10 h-10 animate-spin" />
          </div>
        ) : (
          <div className="w-full h-full flex flex-col gap-y-4">
            {notFound ? (
              <EmptyContent title={t`No data`} />
            ) : (
              <>
                <div className="w-full grid grid-cols-1 md:grid-cols-2 gap-y-10 lg:grid-cols-3 md:gap-x-6">
                  {promotionList?.length > 0 &&
                    promotionList?.map((item: any, idx: number) => (
                      <PostCardLarge
                        key={idx}
                        type={
                          type === ECardType.Promotion
                            ? ECardType.Promotion
                            : ECardType.News
                        }
                        slug={item.slug}
                        title={item.title}
                        excerpt={item.description}
                        validUntil={item.valid_until}
                        category={item.category ? item.category.title : ''}
                        coverImage={item.image}
                        publishedAt={item.publishedAt}
                      />
                    ))}
                </div>

                {hasMore && (
                  <Button
                    className=" bg-[#FFCC00] rounded-md mx-auto w-[200px] h-[44px] hover:bg-[#FFCC00]/80"
                    onClick={handleLoadMore}
                  >
                    <Typography
                      variant="body2"
                      weight="semibold"
                      className="text-[#182230]"
                    >
                      Xem thêm
                    </Typography>
                  </Button>
                )}
              </>
            )}
          </div>
        )}
      </div>
    </section>
  )
}

export default NewsPromotionListing
