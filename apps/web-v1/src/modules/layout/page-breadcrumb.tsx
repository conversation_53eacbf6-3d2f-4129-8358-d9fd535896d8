'use client'

import { useLingui } from '@lingui/react/macro'
import { LayoutContainer } from '@ttplatform/core-page-builder/components'
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@ttplatform/ui/components'
import Link from 'next/link'

type TProps = {
  items: {
    title: string
    href?: string
  }[]
}
const PageBreadcrumb = ({ items }: TProps) => {
  const { i18n } = useLingui()
  const lastItem = items[items.length - 1]

  if (!items.length) return null

  return (
    <LayoutContainer>
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <Link
              href="/"
              className="text-black/50 transition-all duration-300 hover:text-red-800"
            >
              {i18n._('Home')}
            </Link>
          </BreadcrumbItem>
          <BreadcrumbSeparator />

          {items.slice(0, -1).map((item: any, index: number) => (
            <div key={index} className="flex items-center gap-2.5">
              <BreadcrumbItem>
                <Link
                  href={item.href}
                  className="text-black/50 transition-all duration-300 hover:text-red-800"
                >
                  {item.title}
                </Link>
              </BreadcrumbItem>
              {index !== items.length - 1 && <BreadcrumbSeparator />}
            </div>
          ))}
          <BreadcrumbItem>
            <BreadcrumbPage className="font-medium">
              {lastItem.title}
            </BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>
    </LayoutContainer>
  )
}

export default PageBreadcrumb
