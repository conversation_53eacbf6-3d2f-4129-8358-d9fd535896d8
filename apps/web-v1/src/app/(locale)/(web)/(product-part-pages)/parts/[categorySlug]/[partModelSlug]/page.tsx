import { Metadata } from 'next'
import Client<PERSON>lugHandler from '~/src/app/(locale)/client-slug-handler'
import { generateMetadataObject } from '~/src/libs/cms/shared/metadata'
import PageContent from '~/src/libs/cms/shared/page-content'
import fetchContentType from '~/src/libs/cms/strapi/fetchContentType'
import { apiRoute } from '~/src/libs/constants/cms'
import { getLocale } from '~/src/libs/data/cookies'
import { initLingui } from '~/src/localization/i18n-init-lingui'
import { PageBreadcrumb } from '~/src/modules/layout'
import PartDetailsHero from '~/src/modules/part/part-details-hero'
//---------------------------------------------------------------------------------
type TProps = {
  params: Promise<{ partModelSlug: string }>
}
//---------------------------------------------------------------------------------
export async function generateMetadata(): Promise<Metadata> {
  const locale = await getLocale()

  const pageData = await fetchContentType({
    contentType: apiRoute.partModels,
    params: {
      filters: {
        locale: locale,
      },
      populate: 'seo.metaImage',
    },
    spreadData: true,
  })

  const seo = pageData?.seo
  const metadata = generateMetadataObject(seo)
  return metadata
}

export default async function PartDetailsPage({ params }: TProps) {
  const locale = await getLocale()
  const i18n = initLingui(locale)
  const { partModelSlug } = await params

  const pageData = await fetchContentType({
    contentType: apiRoute.partModels,
    params: {
      filters: {
        slug: partModelSlug,
        locale: locale,
      },
    },
    spreadData: true,
  })
  console.log('🚀 ~ PartDetailsPage ~ pageData:', pageData)

  const localizedSlugs = pageData?.localizations?.reduce(
    (acc: Record<string, string>, localization: any) => {
      acc[localization.locale] = localization.slug
      return acc
    },
    { [locale]: pageData?.slug },
  )

  return (
    <>
      <ClientSlugHandler localizedSlugs={localizedSlugs} />
      <PageBreadcrumb
        items={[
          { title: i18n._('Parts'), href: '/parts' },
          { title: `${pageData?.name}: ${pageData?.part?.name}` },
        ]}
      />
      <PartDetailsHero pageData={pageData} />
      <PageContent pageData={pageData} />
    </>
  )
}
