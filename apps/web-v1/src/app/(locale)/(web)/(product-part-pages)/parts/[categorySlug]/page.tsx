import { Metadata } from 'next'
import Client<PERSON>lugHandler from '~/src/app/(locale)/client-slug-handler'
import { generateMetadataObject } from '~/src/libs/cms/shared/metadata'
import PageContent from '~/src/libs/cms/shared/page-content'
import fetchContentType from '~/src/libs/cms/strapi/fetchContentType'
import { apiRoute } from '~/src/libs/constants/cms'
import { getLocale } from '~/src/libs/data/cookies'
import { initLingui } from '~/src/localization/i18n-init-lingui'
import { PageBreadcrumb } from '~/src/modules/layout'
import PartCategoryHero from '~/src/modules/part/part-category-hero'
import PartList from '~/src/modules/part/part-list'
//---------------------------------------------------------------------------------
type TProps = {
  params: Promise<{ categorySlug: string }>
}
//---------------------------------------------------------------------------------
export async function generateMetadata(): Promise<Metadata> {
  const locale = await getLocale()

  const pageData = await fetchContentType({
    contentType: apiRoute.partCategories,
    params: {
      filters: {
        locale: locale,
      },
      populate: 'seo.metaImage',
    },
    spreadData: true,
  })

  const seo = pageData?.seo
  const metadata = generateMetadataObject(seo)
  return metadata
}

export default async function PartCategoryPage({ params }: TProps) {
  const locale = await getLocale()
  const i18n = initLingui(locale)
  const { categorySlug } = await params

  const pageData = await fetchContentType({
    contentType: apiRoute.partCategories,
    params: {
      filters: {
        slug: categorySlug,
        locale: locale,
      },
    },
    spreadData: true,
  })

  const localizedSlugs = pageData?.localizations?.reduce(
    (acc: Record<string, string>, localization: any) => {
      acc[localization.locale] = localization.slug
      return acc
    },
    { [locale]: pageData?.slug },
  )

  return (
    <>
      <ClientSlugHandler localizedSlugs={localizedSlugs} />
      <PageBreadcrumb
        items={[
          { title: i18n._('Parts'), href: '/parts' },
          { title: pageData?.title },
        ]}
      />
      <PartCategoryHero
        heading={pageData?.title}
        sub_heading={pageData?.description}
        buttons={pageData?.buttons}
        image={pageData?.image}
      />
      <PartList
        categorySlug={pageData?.slug}
        locale={locale}
        insightsData={pageData?.insights}
      />
      <PageContent pageData={pageData} />
    </>
  )
}
