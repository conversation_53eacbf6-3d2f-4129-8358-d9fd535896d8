import ProductCategoriesView from '~/src/modules/product/product-categories-view'

import { Metadata } from 'next'

import ClientSlugHandler from '~/src/app/(locale)/client-slug-handler'
import { generateMetadataObject } from '~/src/libs/cms/shared/metadata'
import PageContent from '~/src/libs/cms/shared/page-content'
import fetchContentType from '~/src/libs/cms/strapi/fetchContentType'
import { apiRoute } from '~/src/libs/constants/cms'
import { getLocale } from '~/src/libs/data/cookies'

export async function generateMetadata(): Promise<Metadata> {
  const locale = await getLocale()

  const pageData = await fetchContentType({
    contentType: apiRoute.pageProductCategory,
    params: {
      filters: {
        locale,
      },
      populate: 'seo.metaImage',
    },
    spreadData: true,
  })

  const seo = pageData?.seo
  const metadata = generateMetadataObject(seo)
  return metadata
}

export default async function ProductCategoriesPage() {
  const locale = await getLocale()

  const pageData = await fetchContentType({
    contentType: apiRoute.pageProductCategory,
    params: {
      filters: { locale },
    },
    spreadData: true,
  })

  // Fetch product categories data
  const productCategoriesData = await fetchContentType({
    contentType: apiRoute.productCategories,
    params: {
      sort: 'rank:asc',
      populate: {
        image: {
          fields: ['*'],
        },
      },
    },
    spreadData: false,
  })

  const localizedSlugs = pageData?.localizations?.reduce(
    (acc: Record<string, string>, localization: any) => {
      acc[localization.locale] = ''
      return acc
    },
    { [locale]: '' },
  )

  return (
    <>
      <ClientSlugHandler localizedSlugs={localizedSlugs} />
      <ProductCategoriesView productCategories={productCategoriesData?.data} />
      <PageContent pageData={pageData} />
    </>
  )
}
