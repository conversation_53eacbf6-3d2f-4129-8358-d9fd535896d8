import ProductDetailsView from '@/modules/product/product-details-view'
import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import ClientSlugHandler from '~/src/app/(locale)/client-slug-handler'
import { generateMetadataObject } from '~/src/libs/cms/shared/metadata'
import PageContent from '~/src/libs/cms/shared/page-content'
import fetchContentType from '~/src/libs/cms/strapi/fetchContentType'
import { apiRoute } from '~/src/libs/constants/cms'
import { getLocale } from '~/src/libs/data/cookies'
import { initLingui } from '~/src/localization/i18n-init-lingui'
import { PageBreadcrumb } from '~/src/modules/layout'

interface ModelPageProps {
  params: Promise<{ slug: string; model: string }>
}

export async function generateMetadata({ params }: ModelPageProps): Promise<Metadata> {
  const locale = await getLocale()
  const { slug, model } = await params

  // Fetch model data for metadata
  const modelData = await fetchContentType({
    contentType: 'product-models', // Adjust based on your Strapi schema
    params: {
      filters: {
        slug: model,
        locale: locale,
      },
      populate: 'seo.metaImage',
    },
    spreadData: true,
  })

  const pageData = await fetchContentType({
    contentType: apiRoute.pageProductSingle,
    params: {
      filters: {
        locale,
      },
      populate: 'seo.metaImage',
    },
    spreadData: true,
  })

  const seo = modelData?.seo || pageData?.seo
  const metadata = generateMetadataObject(seo)
  
  return {
    ...metadata,
    title: modelData?.name || modelData?.title || metadata.title,
    description: modelData?.description || metadata.description,
  }
}

export default async function ModelDetailsPage({ params }: ModelPageProps) {
  const locale = await getLocale()
  const i18n = initLingui(locale)
  const { slug, model } = await params

  // Fetch product data for breadcrumb
  const productData = await fetchContentType({
    contentType: apiRoute.products,
    params: {
      filters: { locale, slug },
    },
    spreadData: true,
  })

  // Fetch model data
  const modelData = await fetchContentType({
    contentType: 'product-models', // Adjust based on your Strapi schema
    params: {
      filters: { 
        slug: model,
        locale: locale,
      },
      populate: '*',
    },
    spreadData: true,
  })

  // If model doesn't exist, return 404
  if (!modelData) {
    notFound()
  }

  const pageData = await fetchContentType({
    contentType: apiRoute.pageProductSingle,
    params: {
      filters: { locale },
    },
    spreadData: true,
  })

  const localizedSlugs = pageData?.localizations?.reduce(
    (acc: Record<string, string>, localization: any) => {
      acc[localization.locale] = `${slug}/${model}`
      return acc
    },
    { [locale]: `${slug}/${model}` },
  )

  return (
    <>
      <ClientSlugHandler localizedSlugs={localizedSlugs} />
      <PageBreadcrumb
        items={[
          { title: i18n._('Products'), href: '/products' },
          { title: productData?.name || productData?.title, href: `/products/${slug}` },
          { title: modelData?.name || modelData?.title },
        ]}
      />
      <ProductDetailsView productData={modelData} />
      <PageContent pageData={pageData} />
    </>
  )
}

// Generate static params for known models (optional - for better performance)
export async function generateStaticParams() {
  try {
    const models = await fetchContentType({
      contentType: 'product-models',
      params: {
        pagination: {
          pageSize: 100,
        },
        fields: ['slug'],
        populate: ['product'], // To get parent product slug
      },
    })

    return models?.data?.map((model: any) => ({
      slug: model.product?.slug || 'default',
      model: model.slug,
    })) || []
  } catch (error) {
    console.error('Error generating static params for models:', error)
    return []
  }
}
