import { redirect } from 'next/navigation'
import fetchContentType from '~/src/libs/cms/strapi/fetchContentType'
import { getLocale } from '~/src/libs/data/cookies'

export default async function ProductsPage() {
  const locale = await getLocale()

  // Fetch first available category to redirect to
  const categories = await fetchContentType({
    contentType: 'product-types',
    params: {
      filters: { locale: locale },
      pagination: {
        pageSize: 1,
      },
      sort: ['name:asc'],
      fields: ['slug'],
    },
  })

  // Redirect to first category or fallback
  const firstCategory = categories?.data?.[0]?.slug || 'may-xuc-dao-mini'
  redirect(`/${firstCategory}`)
}
