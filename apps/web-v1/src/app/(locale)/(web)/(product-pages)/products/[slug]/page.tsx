import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import ClientSlugHandler from '~/src/app/(locale)/client-slug-handler'
import { generateMetadataObject } from '~/src/libs/cms/shared/metadata'
import PageContent from '~/src/libs/cms/shared/page-content'
import fetchContentType from '~/src/libs/cms/strapi/fetchContentType'
import { apiRoute } from '~/src/libs/constants/cms'
import { getLocale } from '~/src/libs/data/cookies'
import { initLingui } from '~/src/localization/i18n-init-lingui'
import { PageBreadcrumb } from '~/src/modules/layout'
import ProductsView from '~/src/modules/product/products-view'

export async function generateMetadata(): Promise<Metadata> {
  const locale = await getLocale()

  const pageData = await fetchContentType({
    contentType: apiRoute.pageProductSingle,
    params: {
      filters: {
        locale,
      },
      populate: 'seo.metaImage',
    },
    spreadData: true,
  })

  const seo = pageData?.seo
  const metadata = generateMetadataObject(seo)
  return metadata
}

export default async function ProductModelsPage(props: {
  params: Promise<{ slug: string }>
}) {
  const params = await props.params
  const slug = params.slug

  const locale = await getLocale()
  const i18n = initLingui(locale)

  // Fetch product data to validate it exists
  const productData = await fetchContentType({
    contentType: apiRoute.products,
    params: {
      filters: { locale, slug },
      populate: ['product_models'], // Populate models
    },
    spreadData: true,
  })

  // If product doesn't exist, return 404
  if (!productData) {
    notFound()
  }

  const pageData = await fetchContentType({
    contentType: apiRoute.pageProductSingle,
    params: {
      filters: { locale },
    },
    spreadData: true,
  })

  const localizedSlugs = pageData?.localizations?.reduce(
    (acc: Record<string, string>, localization: any) => {
      acc[localization.locale] = slug
      return acc
    },
    { [locale]: slug },
  )

  return (
    <>
      <ClientSlugHandler localizedSlugs={localizedSlugs} />
      <PageBreadcrumb
        items={[
          { title: i18n._('Products'), href: '/products' },
          { title: productData?.name || productData?.title },
        ]}
      />
      <ProductsView productSlug={slug} productData={productData} />
      <PageContent pageData={pageData} />
    </>
  )
}
