import ProductDetailsView from '@/modules/product/product-details-view'
import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import ClientSlugHandler from '~/src/app/(locale)/client-slug-handler'
import { generateMetadataObject } from '~/src/libs/cms/shared/metadata'
import PageContent from '~/src/libs/cms/shared/page-content'
import fetchContentType from '~/src/libs/cms/strapi/fetchContentType'
import { apiRoute } from '~/src/libs/constants/cms'
import { getLocale } from '~/src/libs/data/cookies'
import { initLingui } from '~/src/localization/i18n-init-lingui'
import { PageBreadcrumb } from '~/src/modules/layout'
import ProductsView from '~/src/modules/product/products-view'

export async function generateMetadata(): Promise<Metadata> {
  const locale = await getLocale()

  const pageData = await fetchContentType({
    contentType: apiRoute.pageProductSingle,
    params: {
      filters: {
        locale,
      },
      populate: 'seo.metaImage',
    },
    spreadData: true,
  })

  const seo = pageData?.seo
  const metadata = generateMetadataObject(seo)
  return metadata
}

export default async function ProductSlugPage(props: {
  params: Promise<{ slug: string }>
}) {
  const params = await props.params
  const slug = params.slug

  const locale = await getLocale()
  const i18n = initLingui(locale)

  const productExists = await fetchContentType({
    contentType: apiRoute.products,
    params: {
      filters: { locale, slug },
      fields: ['id', 'slug', 'name'],
      populate: false,
    },
    spreadData: true,
  })

  if (productExists) {
    const productData = await fetchContentType({
      contentType: apiRoute.products,
      params: {
        filters: { locale, slug },
        populate: ['product_models'],
      },
      spreadData: true,
    })

    const pageData = await fetchContentType({
      contentType: apiRoute.pageProductSingle,
      params: {
        filters: { locale },
      },
      spreadData: true,
    })

    const localizedSlugs = pageData?.localizations?.reduce(
      (acc: Record<string, string>, localization: any) => {
        acc[localization.locale] = slug
        return acc
      },
      { [locale]: slug },
    )

    return (
      <>
        <ClientSlugHandler localizedSlugs={localizedSlugs} />
        <PageBreadcrumb
          items={[
            { title: i18n._('Products'), href: '/product-categories' },
            { title: productData?.name || productData?.title },
          ]}
        />
        <ProductsView />
        <PageContent pageData={pageData} />
      </>
    )
  }

  const modelExists = await fetchContentType({
    contentType: 'product-models',
    params: {
      filters: { locale, slug },
      fields: ['id', 'slug', 'name'],
      populate: false,
    },
    spreadData: true,
  })

  if (modelExists) {
    const modelData = await fetchContentType({
      contentType: 'product-models',
      params: {
        filters: { locale, slug },
        populate: ['product'],
      },
      spreadData: true,
    })
    const pageData = await fetchContentType({
      contentType: apiRoute.pageProductSingle,
      params: {
        filters: { locale },
      },
      spreadData: true,
    })

    const localizedSlugs = pageData?.localizations?.reduce(
      (acc: Record<string, string>, localization: any) => {
        acc[localization.locale] = slug
        return acc
      },
      { [locale]: slug },
    )

    return (
      <>
        <ClientSlugHandler localizedSlugs={localizedSlugs} />
        <PageBreadcrumb
          items={[
            { title: i18n._('Products'), href: '/product-categories' },
            {
              title: modelData.product?.name || modelData.product?.title,
              href: `/products/${modelData.product?.slug}`
            },
            { title: modelData?.name || modelData?.title },
          ]}
        />
        <ProductDetailsView productData={modelData} />
        <PageContent pageData={pageData} />
      </>
    )
  }

  // return 404
  notFound()
}
