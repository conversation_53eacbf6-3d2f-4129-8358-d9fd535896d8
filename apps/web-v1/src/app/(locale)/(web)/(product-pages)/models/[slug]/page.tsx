import ProductDetailsView from '@/modules/product/product-details-view'
import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import ClientSlugHandler from '~/src/app/(locale)/client-slug-handler'
import { generateMetadataObject } from '~/src/libs/cms/shared/metadata'
import PageContent from '~/src/libs/cms/shared/page-content'
import fetchContentType from '~/src/libs/cms/strapi/fetchContentType'
import { apiRoute } from '~/src/libs/constants/cms'
import { getLocale } from '~/src/libs/data/cookies'
import { initLingui } from '~/src/localization/i18n-init-lingui'
import { PageBreadcrumb } from '~/src/modules/layout'

interface ModelPageProps {
  params: Promise<{ slug: string }>
}

export async function generateMetadata({ params }: ModelPageProps): Promise<Metadata> {
  const locale = await getLocale()
  const { slug } = await params

  // Fetch model data for metadata
  const modelData = await fetchContentType({
    contentType: 'product-models',
    params: {
      filters: {
        slug: slug,
        locale: locale,
      },
      populate: ['seo.metaImage', 'product'],
    },
    spreadData: true,
  })

  const pageData = await fetchContentType({
    contentType: apiRoute.pageProductSingle,
    params: {
      filters: {
        locale,
      },
      populate: 'seo.metaImage',
    },
    spreadData: true,
  })

  const seo = modelData?.seo || pageData?.seo
  const metadata = generateMetadataObject(seo)

  return {
    ...metadata,
    title: modelData?.name || modelData?.title || metadata.title,
    description: modelData?.description || metadata.description,
  }
}

export default async function ModelDetailsPage({ params }: ModelPageProps) {
  const locale = await getLocale()
  const i18n = initLingui(locale)
  const { slug } = await params

  // Fetch model data with product info
  const modelData = await fetchContentType({
    contentType: 'product-models',
    params: {
      filters: {
        slug: slug,
        locale: locale,
      },
      populate: ['product'], // Populate parent product for breadcrumb
    },
    spreadData: true,
  })

  // If model doesn't exist, return 404
  if (!modelData) {
    notFound()
  }

  const pageData = await fetchContentType({
    contentType: apiRoute.pageProductSingle,
    params: {
      filters: { locale },
    },
    spreadData: true,
  })

  const localizedSlugs = pageData?.localizations?.reduce(
    (acc: Record<string, string>, localization: any) => {
      acc[localization.locale] = slug
      return acc
    },
    { [locale]: slug },
  )

  return (
    <>
      <ClientSlugHandler localizedSlugs={localizedSlugs} />
      <PageBreadcrumb
        items={[
          { title: i18n._('Products'), href: '/product-categories' },
          {
            title: modelData.product?.name || modelData.product?.title,
            href: `/products/${modelData.product?.slug}`
          },
          { title: modelData?.name || modelData?.title },
        ]}
      />
      <ProductDetailsView productData={modelData} />
      <PageContent pageData={pageData} />
    </>
  )
}

// Generate static params for better performance
export async function generateStaticParams() {
  try {
    const models = await fetchContentType({
      contentType: 'product-models',
      params: {
        pagination: {
          pageSize: 100, // Adjust based on your needs
        },
        fields: ['slug'],
      },
    })

    return models?.data?.map((model: any) => ({
      slug: model.slug,
    })) || []
  } catch (error) {
    console.error('Error generating static params for models:', error)
    return []
  }
}
