import '@/styles/web.css'

import {
  BCommitment,
  BContactBox,
} from '@ttplatform/core-page-builder/components'
import React from 'react'

export async function generateMetadata() {
  return {
    title: '<PERSON><PERSON> tùng chính hãng CAT | Đặt hàng dễ dàng | PTC',
    description:
      'Tìm kiếm và đặt mua phụ tùng máy CAT ch<PERSON>h hãng n<PERSON>h chóng, dễ dàng. <PERSON>h mục đầy đủ, hỗ trợ tận tâm từ PTC.',
  }
}

type TProps = {
  children: React.ReactNode
  params: Promise<{ locale: string }>
}

export default async function PartsLayout({ children }: TProps) {
  return (
    <>
      {children}
      <BContactBox />
      <BCommitment />
    </>
  )
}
