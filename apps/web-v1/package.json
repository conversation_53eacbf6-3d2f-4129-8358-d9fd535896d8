{"name": "web-v1", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack --port 3001", "prebuild": "npx @lingui/cli extract && npx @lingui/cli compile", "build": "next build", "start": "next start", "test": "vitest run", "test:watch": "vitest --watch", "check": "npx @biomejs/biome check --write", "format": "npx @biomejs/biome format --write", "extract": "npx @lingui/cli extract --clean", "compile": "npx @lingui/cli compile", "lingui:extract": "npx @lingui/cli extract --clean", "lingui:compile": "npx @lingui/cli compile", "generate-search-assets": "npx tsx -r dotenv/config scripts/generate-search-assets.ts"}, "dependencies": {"@lingui/core": "^5.3.2", "@lingui/react": "^5.3.2", "@lingui/swc-plugin": "^5.5.2", "@next/font": "^14.2.15", "@tanstack/react-form": "^1.3.3", "@ttplatform/common": "*", "@ttplatform/core-page-builder": "*", "@ttplatform/internationalization": "*", "@ttplatform/theme": "*", "@ttplatform/ui": "*", "axios": "^1.10.0", "dompurify": "^3.2.5", "fuse.js": "^7.1.0", "gsap": "^3.12.7", "js-cookie": "^3.0.5", "jwt-decode": "^4.0.0", "lenis": "^1.2.3", "lucide-react": "^0.503.0", "next": "15.3.0", "next-auth": "^5.0.0-beta.25", "node-fetch": "^3.3.2", "prettier-plugin-tailwindcss": "^0.6.11", "qs": "^6.14.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-wrap-balancer": "^1.1.1", "swr": "^2.3.3", "tsx": "^4.20.3", "zod": "^3.24.2", "zustand": "^5.0.3"}, "devDependencies": {"@lingui/loader": "^5.3.2", "@tailwindcss/postcss": "^4", "@ttplatform/config-biome": "*", "@ttplatform/config-eslint": "*", "@ttplatform/config-typescript": "*", "@ttplatform/config-vitest": "*", "@types/js-cookie": "^3.0.6", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "tailwindcss": "^4", "typescript": "^5.8.3", "vitest": "^3.1.1"}}