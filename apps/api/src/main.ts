import { Logger, ValidationPipe } from '@nestjs/common'
import { NestFactory } from '@nestjs/core'
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger'
import { apiReference } from '@scalar/nestjs-api-reference'
import { AppModule } from './app.module'
declare const module: any

const logger = new Logger('PTC Platform API')
const port = process.env.PORT ?? 8080

async function bootstrap() {
  const app = await NestFactory.create(AppModule)

  app.enableCors()
  const config = new DocumentBuilder()
    .setTitle('PTC Platform API')
    .setDescription('The PTC Platform API')
    .setVersion('1.0')
    .addTag('Phu Thai CAT')
    .build()
  const document = SwaggerModule.createDocument(app, config)
  app.use(
    '/docs',
    apiReference({
      spec: {
        content: document,
      },
    }),
  )
  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
      whitelist: true,
    }),
  )

  await app.listen(port)
  logger.log(`Server is running on port ${port}`)
  logger.log(`http://localhost:${port}`)

  if (module.hot) {
    module.hot.accept()
    module.hot.dispose(() => app.close())
  }
}

bootstrap()
