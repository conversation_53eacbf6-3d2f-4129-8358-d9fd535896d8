// import * as <PERSON><PERSON> from 'joi';
// export interface EnvironmentVariables {
//   NODE_ENV: string;
//   PORT: number;
//   DATABASE_URL: string;
// }

// export const environmentVariablesSchema = Joi.object<EnvironmentVariables>({
//   NODE_ENV: Joi.string().valid('development', 'production', 'test').required(),
//   PORT: Joi.number().required(),
//   DATABASE_URL: Joi.string().required(),
// });
