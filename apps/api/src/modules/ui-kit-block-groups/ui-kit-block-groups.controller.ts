import { Controller, Get, Param, Query } from '@nestjs/common'
import { ApiQuery } from '@nestjs/swagger'
import { fResponse } from 'prisma/utils/format-response'
import { Public } from '../auth/decorators/public.decorators'
import { UiKitBlockGroupsService } from './ui-kit-block-groups.service'

@Controller('ui-kit/block-groups')
export class UiKitBlockGroupsController {
  constructor(
    private readonly uiKitBlockGroupsService: UiKitBlockGroupsService,
  ) {}

  // @Post()
  // create(@Body() createUiKitBlockGroupDto: CreateUiKitBlockGroupDto) {
  //   return this.uiKitBlockGroupsService.create(createUiKitBlockGroupDto);
  // }

  @Public()
  @Get()
  @ApiQuery({ name: 'includeBlocks', type: Boolean, required: false })
  @ApiQuery({ name: 'skip', type: Number, required: false })
  @ApiQuery({ name: 'take', type: Number, required: false })
  @ApiQuery({ name: 'cursor', type: String, required: false })
  @ApiQuery({ name: 'where', type: String, required: false })
  @ApiQuery({ name: 'orderBy', type: String, required: false })
  async findAll(
    @Query('includeBlocks') includeBlocks = false,
    @Query('skip') skip = 0,
    @Query('take') take = 10,
    @Query('cursor') cursor = undefined,
    @Query('where') where = undefined,
    @Query('orderBy') orderBy = undefined,
  ) {
    const params = {
      includeBlocks,
      skip,
      take,
      cursor,
      where,
      orderBy,
    }

    const validParams = Object.fromEntries(
      Object.entries(params).filter(Boolean),
    )

    const { data, metadata } =
      await this.uiKitBlockGroupsService.findAll(validParams)

    return fResponse({ data, metadata })
  }

  @Public()
  @Get(':id')
  async findOne(@Param('id') id: string) {
    const data = await this.uiKitBlockGroupsService.findOne(id)
    return fResponse({ data, message: 'UI Kit Block Group was found!' })
  }

  // @Patch(':id')
  // update(
  //   @Param('id') id: string,
  //   @Body() updateUiKitBlockGroupDto: UpdateUiKitBlockGroupDto,
  // ) {
  //   return this.uiKitBlockGroupsService.update(+id, updateUiKitBlockGroupDto);
  // }

  // @Delete(':id')
  // remove(@Param('id') id: string) {
  //   return this.uiKitBlockGroupsService.remove(+id);
  // }
}
