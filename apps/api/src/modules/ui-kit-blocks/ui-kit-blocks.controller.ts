import { Controller, Get, Param, Query } from '@nestjs/common'
import { ApiQuery } from '@nestjs/swagger'
import { fResponse } from 'prisma/utils/format-response'
import { Public } from '../auth/decorators/public.decorators'
import { UiKitBlocksService } from './ui-kit-blocks.service'

@Controller('ui-kit/blocks')
export class UiKitBlocksController {
  constructor(private readonly uiKitBlocksService: UiKitBlocksService) {}

  // @Post()
  // create(@Body() createUiKitBlockDto: CreateUiKitBlockDto) {
  //   return this.uiKitBlocksService.create(createUiKitBlockDto);
  // }

  @Public()
  @Get()
  @ApiQuery({ name: 'includeGroup', type: Boolean, required: false })
  @ApiQuery({ name: 'skip', type: Number, required: false })
  @ApiQuery({ name: 'take', type: Number, required: false })
  @ApiQuery({ name: 'cursor', type: String, required: false })
  @ApiQuery({ name: 'where', type: String, required: false })
  @ApiQuery({ name: 'orderBy', type: String, required: false })
  async findAll(
    @Query('includeGroup') includeGroup = false,
    @Query('skip') skip = 0,
    @Query('take') take = 10,
    @Query('cursor') cursor = undefined,
    @Query('where') where = undefined,
    @Query('orderBy') orderBy = undefined,
  ) {
    const params = {
      includeGroup: Boolean(includeGroup?.toString() === 'true'),
      skip,
      take,
      cursor,
      where,
      orderBy,
    }

    const { data, metadata } = await this.uiKitBlocksService.findAll(params)

    return fResponse({ data, metadata })
  }

  @Public()
  @Get(':id')
  async findOne(@Param('id') id: string) {
    const data = await this.uiKitBlocksService.findOne(id)
    return fResponse({ data, message: 'UI Kit Block was found!' })
  }

  // @Patch(':id')
  // update(
  //   @Param('id') id: string,
  //   @Body() updateUiKitBlockDto: UpdateUiKitBlockDto,
  // ) {
  //   return this.uiKitBlocksService.update(id, updateUiKitBlockDto);
  // }

  // @Delete(':id')
  // remove(@Param('id') id: string) {
  //   return this.uiKitBlocksService.remove(id);
  // }
}
