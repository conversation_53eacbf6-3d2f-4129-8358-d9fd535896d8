import { Injectable, NotFoundException } from '@nestjs/common'
import { Prisma } from '@prisma/client'
import { paginator } from 'prisma/utils/paginator'
import { PrismaService } from '../persistence/prisma/prisma.service'
import { CreateUiKitBlockDto } from './dto/create-ui-kit-block.dto'
import { UpdateUiKitBlockDto } from './dto/update-ui-kit-block.dto'

@Injectable()
export class UiKitBlocksService {
  constructor(private readonly prisma: PrismaService) {}

  create(createUiKitBlockDto: CreateUiKitBlockDto) {
    const { ui_kit_block_group_id, ...rest } = createUiKitBlockDto
    return this.prisma.uiKitBlock.create({
      data: {
        ...rest,
        ui_kit_block_group: {
          connect: { id: ui_kit_block_group_id },
        },
      },
    })
  }

  findAll(
    params: {
      includeGroup?: boolean
      skip?: number
      take?: number
      cursor?: Prisma.UiKitBlockWhereUniqueInput
      where?: Prisma.UiKitBlockWhereInput
      orderBy?: Prisma.UiKitBlockOrderByWithRelationInput
    } = {},
  ) {
    const {
      includeGroup,
      skip,
      take = 10,
      cursor,
      where,
      orderBy,
    } = params || {}

    const include = {
      ui_kit_block_group: Boolean(includeGroup?.toString() === 'true'),
    }

    const page = skip ? skip / take : 1

    return paginator(
      this.prisma.uiKitBlock,
      {
        include,
        skip,
        take,
        cursor,
        where,
        orderBy,
      },
      {
        page,
        take,
      },
    )
  }

  async findOne(id: string) {
    const item = await this.prisma.uiKitBlock.findUnique({ where: { id } })

    if (!item) {
      throw new NotFoundException('UiKitBlock not found')
    }
    return item
  }

  update(id: string, updateUiKitBlockDto: UpdateUiKitBlockDto) {
    return this.prisma.uiKitBlock.update({
      where: { id },
      data: updateUiKitBlockDto,
    })
  }

  softDelete(id: string) {
    return this.prisma.uiKitBlock.update({
      where: { id },
      data: { deleted_at: new Date() },
    })
  }

  remove(id: string) {
    return this.prisma.uiKitBlock.delete({ where: { id } })
  }
}
