import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  NotFoundException,
  Param,
  Patch,
  Post,
  Query,
  Request,
  UseGuards,
} from '@nestjs/common'
import { ApiBody, ApiHeaders, ApiQuery } from '@nestjs/swagger'
import { CreateSiteSchema } from '@ttplatform/common'
import { fResponse } from 'prisma/utils/format-response'
import { JwtAuthGuard } from 'src/modules/auth/guards/jwt-auth'
import { UserService } from '../user/user.service'
import { CreateSiteDto } from './dto/create-site.dto'
import { UpdateSiteDto } from './dto/update-site.dto'
import { SitesService } from './sites.service'
@Controller('sites')
export class SitesController {
  constructor(
    private readonly sitesService: SitesService,
    private readonly userService: UserService,
  ) {}

  @UseGuards(JwtAuthGuard)
  @Post()
  @ApiHeaders([
    {
      name: 'Authorization',
      description: 'Bearer token',
      required: true,
    },
  ])
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        name: { type: 'string' },
        status: { type: 'string' },
        metadata: { type: 'object' },
      },
    },
  })
  async create(@Body() createSiteDto: CreateSiteSchema, @Request() req) {
    try {
      const user_id = req.user.user_id
      const user = await this.userService.findById(user_id)

      if (!user) {
        throw new NotFoundException('Authentication is invalid!')
      }

      const payload = {
        ...createSiteDto,
        user_id: user_id!,
        created_by: user_id,
        updated_by: user_id,
      } as CreateSiteDto

      const item = await this.sitesService.create(payload)
      if (!item) {
        throw new NotFoundException('Site not found')
      }
      return fResponse({ data: item, message: 'Site was created!' })
    } catch (error: any) {
      throw new BadRequestException(error.message)
    }
  }

  @UseGuards(JwtAuthGuard)
  @Get()
  @ApiHeaders([
    {
      name: 'Authorization',
      description: 'Bearer token',
      required: true,
    },
  ])
  @ApiQuery({ name: 'skip', type: Number, required: false })
  @ApiQuery({ name: 'take', type: Number, required: false })
  @ApiQuery({ name: 'cursor', type: String, required: false })
  @ApiQuery({ name: 'orderBy', type: String, required: false })
  async findAll(
    @Request() req,
    @Query('skip') skip = 0,
    @Query('take') take = 10,
    @Query('cursor') cursor = undefined,
    @Query('orderBy') orderBy = undefined,
  ) {
    try {
      const user_id = req.user.user_id
      const { data, metadata } = await this.sitesService.findAll({
        skip,
        take,
        cursor,
        orderBy,
        where: {
          user_id: user_id,
        },
      })

      if (!data) {
        throw new NotFoundException('Sites not found')
      }
      return fResponse({ data, metadata })
    } catch (error: any) {
      throw new BadRequestException(error.message)
    }
  }

  @UseGuards(JwtAuthGuard)
  @Get(':id')
  @ApiHeaders([
    {
      name: 'Authorization',
      description: 'Bearer token',
      required: true,
    },
  ])
  async findOne(@Param('id') id: string, @Request() req) {
    try {
      const user_id = req.user.user_id

      const item = await this.sitesService.findOne(id, {
        user_id,
      })

      return fResponse({ data: item, message: 'Site is found!' })
    } catch (error: any) {
      throw new BadRequestException(error.message)
    }
  }

  @UseGuards(JwtAuthGuard)
  @Patch(':id')
  @ApiHeaders([
    {
      name: 'Authorization',
      description: 'Bearer token',
      required: true,
    },
  ])
  async update(
    @Param('id') id: string,
    @Body() updateSiteDto: UpdateSiteDto,
    @Request() req,
  ) {
    try {
      const user_id = req.user.user_id

      const item = await this.sitesService.update(id, updateSiteDto, {
        user_id,
      })
      if (!item) {
        throw new NotFoundException('Site not found')
      }
      return fResponse({ data: item, message: 'Site was updated!' })
    } catch (error: any) {
      throw new BadRequestException(error.message)
    }
  }

  @UseGuards(JwtAuthGuard)
  @Delete(':id')
  @ApiHeaders([
    {
      name: 'Authorization',
      description: 'Bearer token',
      required: true,
    },
  ])
  async remove(@Param('id') id: string, @Request() req) {
    try {
      const user_id = req.user.user_id

      const item = await this.sitesService.remove(id, { user_id })
      if (!item) {
        throw new NotFoundException('Site not found')
      }
      return fResponse({ data: null, message: 'Site was deleted!' })
    } catch (error: any) {
      throw new BadRequestException(error.message)
    }
  }

  @UseGuards(JwtAuthGuard)
  @Delete(':id')
  @ApiHeaders([
    {
      name: 'Authorization',
      description: 'Bearer token',
      required: true,
    },
  ])
  async softDelete(
    @Param('id') id: string,
    @Request() req,
    @Query('isSoft') isSoft = false,
  ) {
    try {
      const user_id = req.user.user_id

      if (isSoft) {
        await this.sitesService.softDelete(id, { user_id })
      } else {
        await this.sitesService.remove(id, { user_id })
      }

      return fResponse({
        data: null,
        message: `Site ${id} ${isSoft ? 'soft deleted' : 'deleted'} successfully`,
      })
    } catch (error: any) {
      throw new BadRequestException(error.message)
    }
  }
}
