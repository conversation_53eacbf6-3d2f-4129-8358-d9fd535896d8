import { Module } from '@nestjs/common'
import { ConfigModule } from '@nestjs/config'
import { APP_GUARD } from '@nestjs/core'
import { JwtModule } from '@nestjs/jwt'

import { AuthController } from './auth.controller'
import { LocalStrategy } from './strategies/local.stategy'

import { PrismaService } from '../persistence/prisma/prisma.service'
import { UserService } from '../user/user.service'
import { AuthService } from './auth.service'

import googleConfig from './config/google.config'
import jwtConfig from './config/jwt.config'
import refreshConfig from './config/refresh.config'
import { JwtAuthGuard } from './guards'
import { JwtStrategy } from './strategies/jwt.stategy'
import { RefreshStrategy } from './strategies/refresh.stategy'
@Module({
  imports: [
    JwtModule.registerAsync(jwtConfig.asProvider()),
    ConfigModule.forFeature(jwtConfig),
    ConfigModule.forFeature(refreshConfig),
    ConfigModule.forFeature(googleConfig),
  ],
  controllers: [AuthController],
  providers: [
    AuthService,
    UserService,
    PrismaService,
    LocalStrategy,
    RefreshStrategy,
    JwtStrategy,
    {
      provide: APP_GUARD,
      useClass: JwtAuthGuard,
    },
  ],
})
export class AuthModule {}
