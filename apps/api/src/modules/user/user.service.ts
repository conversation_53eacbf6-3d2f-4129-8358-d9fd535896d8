import { Injectable } from '@nestjs/common'
import { hash } from 'argon2'
import { PrismaService } from '../persistence/prisma/prisma.service'
import { CreateUserDTO } from './dto'
@Injectable()
export class UserService {
  constructor(private prisma: PrismaService) {}

  async create(createUserDto: CreateUserDTO) {
    const { name, email, password } = createUserDto
    const hashedPassword = await hash(password!)
    const user = await this.prisma.user.create({
      data: { name, email, password: hashedPassword },
    })
    const account = await this.prisma.account.create({
      data: {
        user_id: user.id,
        type: 'credentials',
        provider: 'local',
        provider_account_id: user.id,
        token_type: 'Bearer',
      },
    })
    return { user, account }
  }

  async findByEmail(email: string) {
    return this.prisma.user.findUnique({ where: { email } })
  }

  async findById(id: string) {
    return this.prisma.user.findUnique({ where: { id } })
  }

  async updateRefreshToken(
    id: string,
    accessToken: string,
    hashedRefreshToken: string,
    provider: string,
  ) {
    return this.prisma.account.update({
      where: {
        provider_provider_account_id: { provider, provider_account_id: id },
      },
      data: { refresh_token: hashedRefreshToken, access_token: accessToken },
    })
  }

  async findToken(id: string, access_token: string) {
    return this.prisma.account.findFirst({
      where: {
        access_token,
        user_id: id,
      },
    })
  }
  async findAccount(id: string, provider: string) {
    // This method finds an account by provider and provider_account_id
    // The provider_provider_account_id is a compound unique index in the Account model
    // that ensures uniqueness across provider + provider_account_id combinations
    return this.prisma.account.findUnique({
      where: {
        provider_provider_account_id: { provider, provider_account_id: id },
      },
    })
  }
}
