import { Controller, Get } from '@nestjs/common'
import {
  HealthCheck,
  HealthCheckService,
  HttpHealthIndicator,
  PrismaHealthIndicator,
} from '@nestjs/terminus'
import { PrismaClient } from '@prisma/client'
import { Public } from 'src/modules/auth/decorators/public.decorators'

const prismaClient = new PrismaClient()

const apiUrl = `${process.env.APP_URL || 'http://localhost:8080'}`
const docsUrl = `${apiUrl}/docs`

@Controller('health')
export class HealthController {
  constructor(
    private health: HealthCheckService,
    private http: HttpHealthIndicator,
    private db: PrismaHealthIndicator,
  ) {}

  @Public()
  @Get()
  @HealthCheck()
  check() {
    return this.health.check([
      () => this.http.pingCheck('app-docs', docsUrl),
      () => this.http.pingCheck('app-api', apiUrl),
      () => this.db.pingCheck('database', prismaClient), // Added Prisma health check
    ])
  }
}
