import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
} from '@nestjs/common'
import { ApiBody, ApiQuery } from '@nestjs/swagger'
import { fResponse } from 'prisma/utils/format-response'
import { Public } from '../auth/decorators/public.decorators'
import { CreatePageDto } from './dto/create-page.dto'
import { UpdatePageDto } from './dto/update-page.dto'
import { PagesService } from './pages.service'
@Controller('pages')
export class PagesController {
  constructor(private readonly pagesService: PagesService) {}

  @Public() // TODO: FIXME Remove this once we have a proper auth system
  @Post()
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        title: { type: 'string' },
        slug: { type: 'string' },
        site_id: { type: 'string' },
        content: { type: 'object' },
        metadata: { type: 'object' },
        is_default: { type: 'boolean' },
      },
    },
  })
  async create(@Body() createPageDto: CreatePageDto) {
    try {
      const data = await this.pagesService.create(createPageDto)
      return fResponse({ data, message: 'Page was created!' })
    } catch (error: any) {
      throw new BadRequestException(error.message)
    }
  }

  @Public()
  @Get()
  @ApiQuery({ name: 'skip', type: Number, required: false })
  @ApiQuery({ name: 'take', type: Number, required: false })
  @ApiQuery({ name: 'cursor', type: String, required: false })
  @ApiQuery({ name: 'where', type: String, required: false })
  @ApiQuery({ name: 'orderBy', type: String, required: false })
  async findAll(
    @Query('skip') skip = 0,
    @Query('take') take = 10,
    @Query('cursor') cursor = undefined,
    @Query('where') where = undefined,
    @Query('orderBy') orderBy = undefined,
  ) {
    try {
      const { data, metadata } = await this.pagesService.findAll({
        skip,
        take,
        cursor,
        where,
        orderBy,
      })
      return fResponse({ data, metadata })
    } catch (error: any) {
      throw new BadRequestException(error.message)
    }
  }

  @Public() // TODO: FIXME Remove this once we have a proper auth system
  @Get(':id')
  async findOne(@Param('id') id: string) {
    try {
      const data = await this.pagesService.findOne(id)
      return fResponse({ data, message: 'Page is found!' })
    } catch (error: any) {
      throw new BadRequestException(error.message)
    }
  }

  @Public() // TODO: FIXME Remove this once we have a proper auth system
  @Patch(':id')
  async update(@Param('id') id: string, @Body() updatePageDto: UpdatePageDto) {
    try {
      const data = await this.pagesService.update(id, updatePageDto)
      return fResponse({ data, message: 'Page was updated!' })
    } catch (error: any) {
      throw new BadRequestException(error.message)
    }
  }

  @Public() // TODO: FIXME Remove this once we have a proper auth system
  @Delete(':id')
  async remove(@Param('id') id: string) {
    try {
      await this.pagesService.remove(id)
      return fResponse({ data: null, message: 'Page was deleted!' })
    } catch (error: any) {
      throw new BadRequestException(error.message)
    }
  }

  @Public() // TODO: FIXME Remove this once we have a proper auth system
  @Delete(':id')
  async softDelete(@Param('id') id: string, @Query('isSoft') isSoft = false) {
    try {
      if (isSoft) {
        await this.pagesService.softDelete(id)
      } else {
        await this.pagesService.remove(id)
      }
      return fResponse({ data: null, message: 'Page was deleted!' })
    } catch (error: any) {
      throw new BadRequestException(error.message)
    }
  }
}
