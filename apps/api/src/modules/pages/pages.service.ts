import { Injectable } from '@nestjs/common'
import { Prisma } from '@prisma/client'
import { paginator } from 'prisma/utils/paginator'
import { PrismaService } from 'src/modules/persistence/prisma/prisma.service'
import { CreatePageDto } from './dto/create-page.dto'
import { UpdatePageDto } from './dto/update-page.dto'

@Injectable()
export class PagesService {
  constructor(private readonly prisma: PrismaService) {}

  create(createPageDto: CreatePageDto) {
    return this.prisma.cmsPage.create({
      data: {
        ...createPageDto,
        site_id: createPageDto.site_id,
      },
    })
  }

  findAll(
    params: {
      skip?: number
      take?: number
      cursor?: Prisma.CmsPageWhereUniqueInput
      where?: Prisma.CmsPageWhereInput
      orderBy?: Prisma.CmsPageOrderByWithRelationInput
    } = {},
  ) {
    const { skip, take = 10, cursor, where, orderBy } = params || {}

    const page = skip ? skip / take : 1

    return paginator(
      this.prisma.cmsPage,
      {
        skip,
        take,
        cursor,
        where,
        orderBy,
      },
      {
        page,
        take,
      },
    )
  }

  findOne(id: string) {
    return this.prisma.cmsPage.findUnique({ where: { id } })
  }

  update(id: string, updatePageDto: UpdatePageDto) {
    return this.prisma.cmsPage.update({
      where: { id },
      data: updatePageDto,
    })
  }

  softDelete(id: string) {
    return this.prisma.cmsPage.update({
      where: { id },
      data: { deleted_at: new Date() },
    })
  }

  remove(id: string) {
    return this.prisma.cmsPage.delete({ where: { id } })
  }
}
