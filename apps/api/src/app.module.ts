import { Module } from '@nestjs/common'
import { ConfigModule } from '@nestjs/config'
import { AppController } from './app.controller'
import { AppService } from './app.service'

import { HttpModule } from '@nestjs/axios'
import { TerminusModule } from '@nestjs/terminus'

import {
  AuthModule,
  DomainsModule,
  HealthModule,
  PagesModule,
  PersistenceModule,
  SitesModule,
  UiKitBlockGroupsModule,
  UiKitBlocksModule,
  UserModule,
} from './modules'
@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    // Health
    TerminusModule,
    HttpModule,
    // Auth
    UserModule,
    PersistenceModule,
    AuthModule,
    // CMS
    SitesModule,
    PagesModule,
    DomainsModule,
    HealthModule,
    // CMS - UI Kits
    UiKitBlocksModule,
    UiKitBlockGroupsModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
