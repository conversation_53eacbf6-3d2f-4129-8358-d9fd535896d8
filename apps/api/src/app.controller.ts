import { Controller, Get } from '@nestjs/common'
import { ApiOperation, ApiResponse } from '@nestjs/swagger'
import { AppService } from './app.service'
import { Public } from './modules/auth/decorators/public.decorators'
@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {
    this.appService = new AppService()
  }

  @Public()
  @Get()
  @ApiOperation({ summary: 'Get hello world' })
  @ApiResponse({ status: 200, description: '' })
  getHello(): Record<string, string> {
    return this.appService.getHello()
  }
}
