// notification.prisma

//////////////////////
// 🔔 Notifications
//////////////////////

enum NotificationType {
  INFO
  SUCCESS
  WARNING
  ERROR
}

enum NotificationStatus {
  PENDING
  SENT
  FAILED
  DELIVERED
  READ
}

enum NotificationChannel {
  EMAIL
  SMS
  PUSH
  IN_APP
}

enum NotificationProviderType {
  EMAIL
  SMS
  PUSH
  ZNS
}

model Notification {
  id              String               @id @default(uuid())
  title           String
  content         String
  type            NotificationType     @default(INFO)
  status          NotificationStatus   @default(PENDING)
  channel         NotificationChannel  @default(EMAIL)
  recipient_id    String
  recipient_email String?
  recipient_phone String?
  metadata        Json?
  scheduled_for   DateTime?
  sent_at         DateTime?
  read_at         DateTime?
  provider_id     String
  created_at      DateTime             @default(now())
  updated_at      DateTime             @updatedAt
  deleted_at      DateTime?
  provider        NotificationProvider @relation(fields: [provider_id], references: [id], onDelete: Cascade)

  @@index([recipient_id])
  @@index([created_at])
  @@index([deleted_at])
  @@index([metadata], type: Gin)
}

model NotificationProvider {
  id            String                   @id @default(uuid())
  name          String
  type          NotificationProviderType @default(EMAIL)
  credentials   Json?
  is_enabled    Boolean                  @default(true)
  created_at    DateTime                 @default(now())
  updated_at    DateTime                 @updatedAt
  deleted_at    DateTime?
  notifications Notification[]

  @@index([deleted_at])
}
