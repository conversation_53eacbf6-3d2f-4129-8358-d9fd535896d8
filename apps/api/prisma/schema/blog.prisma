// blog.prisma

/// ///////////////////
/// ///////////////////
enum BlogStatus {
  DRAFT
  PUBLISHED
  ARCHIVED
  SCHEDULED
}

//////////////////////
// 📝 Blog
//////////////////////
model BlogPost {
  id           String        @id @default(uuid())
  title        String
  slug         String        @unique
  content      Json?
  excerpt      String?
  status       BlogStatus    @default(DRAFT)
  metadata     Json?
  tags         String[]
  cover_image  String?
  author_id    String?
  published_at DateTime?
  created_at   DateTime      @default(now())
  updated_at   DateTime      @updatedAt
  deleted_at   DateTime?
  comments     BlogComment[]

  @@index([deleted_at])
  @@index([author_id])
  @@index([created_at])
  @@index([metadata], type: Gin)
}

model BlogComment {
  id         String    @id @default(uuid())
  content    String
  post_id    String
  author_id  String?
  parent_id  String?
  mpath      String
  level      Int       @default(0)
  is_hidden  Boolean   @default(false)
  created_at DateTime  @default(now())
  updated_at DateTime  @updatedAt
  deleted_at DateTime?
  post       BlogPost  @relation(fields: [post_id], references: [id], onDelete: Cascade)

  @@index([post_id])
  @@index([mpath])
  @@index([deleted_at])
}

model BlogCategory {
  id          String    @id @default(uuid())
  name        String
  slug        String    @unique
  description String?
  parent_id   String?
  mpath       String
  level       Int       @default(0)
  is_active   Boolean   @default(true)
  rank        Int       @default(0)
  metadata    Json?
  created_at  DateTime  @default(now())
  updated_at  DateTime  @updatedAt
  deleted_at  DateTime?

  @@index([mpath])
  @@index([deleted_at])
  @@index([metadata], type: Gin)
}

model Hashtag {
  id          String    @id @default(uuid())
  name        String    @unique
  slug        String    @unique
  description String?
  count       Int       @default(0)
  is_active   Boolean   @default(true)
  metadata    Json?
  created_at  DateTime  @default(now())
  updated_at  DateTime  @updatedAt
  deleted_at  DateTime?

  @@index([name])
  @@index([deleted_at])
  @@index([metadata], type: Gin)
}
