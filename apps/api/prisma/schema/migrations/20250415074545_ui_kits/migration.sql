-- CreateTable
CREATE TABLE "UiBrandKit" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "colors" TEXT[],
    "trending" BOOLEAN NOT NULL DEFAULT false,
    "default" B<PERSON><PERSON>EAN NOT NULL DEFAULT false,
    "metadata" JSONB,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" TIMESTAMP(3),

    CONSTRAINT "UiBrandKit_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "UiKitBlockGroup" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "group_types" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "rank" INTEGER,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "UiKitBlockGroup_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "UiKitBlock" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "block_type" TEXT NOT NULL,
    "brief" TEXT,
    "description" TEXT,
    "icon" TEXT,
    "metadata" JSONB,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "deleted_at" TIMESTAMP(3),
    "ui_kit_block_group_id" TEXT,

    CONSTRAINT "UiKitBlock_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "UiKitBlock" ADD CONSTRAINT "UiKitBlock_ui_kit_block_group_id_fkey" FOREIGN KEY ("ui_kit_block_group_id") REFERENCES "UiKitBlockGroup"("id") ON DELETE SET NULL ON UPDATE CASCADE;
