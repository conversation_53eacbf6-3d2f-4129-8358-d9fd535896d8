// ui-kit.prisma
/**
 * UI Kit
 * UI Kit is a collection of assets that are used to create a UI.
 * It includes blocks of content that are used to create a UI. like: hero, section, footer, etc.
 */

// enum UiKitBlockType {
//   TEXT
//   LINKS
//   IMAGES
//   SPACER
//   MEDIA_KIT
//   SOUND_CLOUD
//   APPLE_MUSIC
//   BANDCAMP
//   SPOTIFY
//   TIKTOK
//   TIKTOK_SHOP
//   TWITCH
//   VIMEO
//   YOUTUBE
//   X_TWITTER
//   INSTAGRAM_FEED
//   VIDEO
//   BUY_ME_A_COFFEE
//   KOFI
//   APPOINTMENT
//   FAN_REQUEST
//   STORE
//   SUPPORT
//   MAILCHIMP
//   SMS_SIGNUP
//   EMAIL_SIGNUP
//   EMAIL_SMS
//   FRIEND
//   FOLLOWER_COUNT
//   TOUR_AND_EVENTS
//   MUSIC
// }

// enum UiKitBlockGroupType {
//   BLOCK
//   SECTION
// }

//////////////////////////////////
//  K<PERSON> BLOCK GROUP
//////////////////////////////////

model UiKitBlockGroup {
  id          String       @id @default(uuid())
  name        String
  description String?
  group_types String[]     @default([]) // BLOCK, SECTION
  rank        Int?
  blocks      UiKitBlock[]
  created_at  DateTime     @default(now())
  updated_at  DateTime     @default(now())
}

//////////////////////////////////
//  KIT BLOCK
//////////////////////////////////

model UiKitBlock {
  id                    String           @id @default(uuid())
  name                  String
  block_type            String
  brief                 String?
  description           String?
  icon                  String?
  metadata              Json?
  created_at            DateTime         @default(now())
  updated_at            DateTime         @default(now())
  deleted_at            DateTime?
  ui_kit_block_group    UiKitBlockGroup? @relation(fields: [ui_kit_block_group_id], references: [id])
  ui_kit_block_group_id String?
}
