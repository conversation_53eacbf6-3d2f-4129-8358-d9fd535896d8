import { PrismaClient } from '@prisma/client'
import { seedBlocks, seedDomain, seedMedia } from './cms.seed'
import {
  seedBrandKits,
  seedUIKitBlockGroups,
  seedUIKitBlocks,
} from './ui-kit-blocks.seed'
import { seedUsers } from './user.seed'

const prisma = new PrismaClient()

async function main() {
  try {
    console.log('🌱 1. Seeding Users...')
    await seedUsers()
    console.log('🌱 1. Seeding Users completed!\n\n')

    console.log('🌱 2. Seeding CMS...')
    await seedDomain()
    await seedBlocks()
    await seedMedia()
    console.log('🌱 2. Seeding CMS completed!\n\n')

    console.log('🌱 3. Seeding UI Kit...')
    await seedBrandKits()
    await seedUIKitBlockGroups()
    await seedUIKitBlocks()
    console.log('🌱 3. Seeding UI Kit completed!\n\n')

    console.log('🌱 🚀 ~ All seeding completed!\n')
  } catch (error) {
    console.error(error)
    process.exit(1)
  } finally {
    await prisma.$disconnect()
  }
}

main()
