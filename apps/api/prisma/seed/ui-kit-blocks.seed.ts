import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

//////////////////////
// 🌱 Brand Kit
//////////////////////

const _brandKits = [
  {
    name: 'Tropical Green',
    description: 'Tropical Green',
    colors: ['#F2F7F5', '#FAAE2B', '#00473E'],
    trending: false,
    default: false,
  },
  {
    name: 'Emerald Sea',
    description: 'Emerald Sea',
    colors: ['#004643', '#4D797F', '#FFFFFE'],
    trending: false,
    default: false,
  },
  {
    name: 'Earthy Elegance',
    description: 'Earthy Elegance',
    colors: ['#55423D', '#C36F54', '#FFFFFE'],
    trending: false,
    default: false,
  },
  {
    name: 'Midnight Sun',
    description: 'Midnight Sun',
    colors: ['#0F0E17', '#FF8906', '#FFFFFE'],
    trending: false,
    default: false,
  },
  {
    name: 'Twilight Blue',
    description: 'Twilight Blue',
    colors: ['#232946', '#49577C', '#FFFFFE'],
    trending: false,
    default: false,
  },
  {
    name: 'Classic Contrast',
    description: 'Classic Contrast',
    colors: ['#FFFFFE', '#FFD803', '#272343'],
    trending: false,
    default: false,
  },
  {
    name: 'Galaxy Night',
    description: 'Galaxy Night',
    colors: ['#16161A', '#7F5AF0', '#FFFFFE'],
    trending: false,
    default: false,
  },
  {
    name: 'Oceanic Wave',
    description: 'Oceanic Wave',
    colors: ['#FFFFFE', '#00EBC7', '#00214D'],
    trending: false,
    default: false,
  },
  {
    name: 'Silver Dawn',
    description: 'Silver Dawn',
    colors: ['#EFF0F3', '#FF8E3C', '#0D0D0D'],
    trending: false,
    default: false,
  },
  {
    name: 'Golden Sand',
    description: 'Golden Sand',
    colors: ['#F9F4EF', '#8C7851', '#020826'],
    trending: false,
    default: false,
  },
  {
    name: 'Misty Harbor',
    description: 'Misty Harbor',
    colors: ['#F8F5F2', '#078080', '#232323'],
    trending: false,
    default: false,
  },
  {
    name: 'Sky High',
    description: 'Sky High',
    colors: ['#FFFFFE', '#3DA9FC', '#094067'],
    trending: false,
    default: false,
  },
  {
    name: 'Blossom Pink',
    description: 'Blossom Pink',
    colors: ['#FEC7D7', '#F582AE', '#0E172C'],
    trending: false,
    default: false,
  },
  {
    name: 'Royal Purple',
    description: 'Royal Purple',
    colors: ['#FFFFFE', '#A195DD', '#2B2C34'],
    trending: false,
    default: false,
  },
  {
    name: 'Soft Blush',
    description: 'Soft Blush',
    colors: ['#FAEEE7', '#FF8BA7', '#33272A'],
    trending: false,
    default: false,
  },
  {
    name: 'Basic Grayscale',
    description: 'Basic Grayscale',
    colors: ['#FFFFFF', '#EEEEEE', '#000000'],
    trending: false,
    default: false,
  },
  {
    name: 'Sunny Pastel',
    description: 'Sunny Pastel',
    colors: ['#FEF6E4', '#f582ae', '#001858'],
    trending: false,
    default: false,
  },
  {
    name: 'Urban Chic',
    description: 'Urban Chic',
    colors: ['#181818', '#fffffe', '#4fc4cf'],
    trending: false,
    default: false,
  },
]

export async function seedBrandKits() {
  console.log('\t🌱 Seeding brand kit...')

  for (const brandKit of _brandKits) {
    const existingBrandKit = await prisma.uiBrandKit.findFirst({
      where: { name: brandKit.name },
    })

    if (existingBrandKit) {
      console.log(`\t❌ Brand kit already exists: ${brandKit.name}`)
      continue
    }

    await prisma.uiBrandKit.create({ data: brandKit })
  }
}

/////////////////////////////////
// 🌱 UI Kit Block Groups
/////////////////////////////////

const _uiKitBlockGroups = [
  {
    name: 'Integrations',
    description: 'Integrations block for connecting with other services',
    group_types: ['BLOCK'],
  },
  {
    name: 'Audience building',
    description: 'Audience building block for building your audience',
    group_types: ['BLOCK'],
  },
  {
    name: 'Layout',
    description: 'Layout block for organizing the page',
    group_types: ['BLOCK', 'SECTION'],
  },
  {
    name: 'Monetization',
    description: 'Monetization block for monetizing your content',
    group_types: ['BLOCK'],
  },
  {
    name: 'Content',
    description: 'Content block for creating content',
    group_types: ['BLOCK', 'SECTION'],
  },
  {
    name: 'Media',
    description: 'Media',
    group_types: ['BLOCK', 'SECTION'],
  },
  {
    name: 'Ecommerce and Marketing',
    description: 'Social',
    group_types: ['SECTION'],
  },
]

export async function seedUIKitBlockGroups() {
  console.log('\t🌱 Seeding UI kit block group...')

  for (const uiKitBlockGroup of _uiKitBlockGroups) {
    const existingUIKitBlockGroup = await prisma.uiKitBlockGroup.findFirst({
      where: { name: uiKitBlockGroup.name },
    })

    if (existingUIKitBlockGroup) {
      console.log(
        `\t❌ UI kit block group already exists: ${uiKitBlockGroup.name}`,
      )
      continue
    }

    await prisma.uiKitBlockGroup.create({
      data: uiKitBlockGroup,
    })
  }
}

////////////////////////////
// 🌱 UI Kit Blocks
////////////////////////////

const _uiKitBlocks = [
  {
    name: 'Text',
    brief: 'Headline',
    description: 'Add headlines and descriptions',
    icon: 'https://pub-f04d991f504d49319db6937c4f8e1156.r2.dev/text.webp',
    block_type: 'TEXT',
    category_name: 'Layout',
  },
  {
    name: 'Images',
    brief: 'Images',
    description: 'Add images to your content',
    icon: 'https://pub-f04d991f504d49319db6937c4f8e1156.r2.dev/images.webp',
    block_type: 'IMAGES',
    category_name: 'Layout',
  },
  {
    name: 'Space and dividers',
    brief: 'Space and dividers',
    description: 'Add space or dividers between your blocks',
    icon: 'https://pub-f04d991f504d49319db6937c4f8e1156.r2.dev/spacer.webp',
    block_type: 'SPACER',
    category_name: 'Layout',
  },
  {
    name: 'Links',
    brief: 'Links',
    description: 'A link or group of links',
    icon: 'https://pub-f04d991f504d49319db6937c4f8e1156.r2.dev/links.webp',
    block_type: 'LINKS',
    category_name: 'Content',
  },
  {
    name: 'Instagram Feed',
    description: 'Showcase your recent posts',
    icon: 'https://pub-f04d991f504d49319db6937c4f8e1156.r2.dev/instagram_feed.webp',
    block_type: 'INSTAGRAM_FEED',
    category_name: 'Content',
  },
  {
    name: 'TikTok',
    description: 'Add your TikTok videos',
    icon: 'https://pub-f04d991f504d49319db6937c4f8e1156.r2.dev/tiktok_video.webp',
    block_type: 'TIKTOK',
    category_name: 'Content',
  },
  {
    name: 'Twitch',
    description: 'Add your Twitch videos',
    icon: 'https://pub-f04d991f504d49319db6937c4f8e1156.r2.dev/twitch_video.webp',
    block_type: 'TWITCH',
    category_name: 'Content',
  },
  {
    name: 'X/Twitter',
    description: 'Add a tweet',
    icon: 'https://pub-f04d991f504d49319db6937c4f8e1156.r2.dev/x_twitter.webp',
    block_type: 'X_TWITTER',
    category_name: 'Content',
  },
  {
    name: 'Vimeo',
    description: 'Add your Vimeo videos',
    icon: 'https://pub-f04d991f504d49319db6937c4f8e1156.r2.dev/vimeo_video.webp',
    block_type: 'VIMEO',
    category_name: 'Content',
  },
  {
    name: 'Spotify',
    description: 'Add a Spotify song or playlist',
    icon: 'https://pub-f04d991f504d49319db6937c4f8e1156.r2.dev/spotify_music.webp',
    block_type: 'SPOTIFY',
    category_name: 'Content',
  },
  {
    name: 'Apple Music',
    description: 'Add an Apple Music song or playlist',
    icon: 'https://pub-f04d991f504d49319db6937c4f8e1156.r2.dev/apple_music.webp',
    block_type: 'APPLE_MUSIC',
    category_name: 'Content',
  },
  {
    name: 'Bandcamp',
    description: 'Add a Bandcamp song or playlist',
    icon: 'https://pub-f04d991f504d49319db6937c4f8e1156.r2.dev/bandcamp_music.webp',
    block_type: 'BANDCAMP',
    category_name: 'Content',
  },
  {
    name: 'SoundCloud',
    description: 'Add a SoundCloud song or playlist',
    icon: 'https://pub-f04d991f504d49319db6937c4f8e1156.r2.dev/soundcloud_music.webp',
    block_type: 'SOUND_CLOUD',
    category_name: 'Content',
  },
  {
    name: 'Music',
    description: 'Add any music',
    icon: 'https://pub-f04d991f504d49319db6937c4f8e1156.r2.dev/music.webp',
    block_type: 'MUSIC',
    category_name: 'Content',
  },
  {
    name: 'Videos',
    description: 'Add your videos',
    icon: 'https://pub-f04d991f504d49319db6937c4f8e1156.r2.dev/video.webp',
    block_type: 'VIDEO',
    category_name: 'Content',
  },
  {
    name: 'Store',
    description: 'Sell your products',
    icon: 'https://pub-f04d991f504d49319db6937c4f8e1156.r2.dev/store.webp',
    block_type: 'STORE',
    category_name: 'Monetization',
  },
  {
    name: 'Media Kit',
    description: 'Your résumé for brand deals and sponsorships',
    icon: 'https://pub-f04d991f504d49319db6937c4f8e1156.r2.dev/media_kit.webp',
    block_type: 'MEDIA_KIT',
    category_name: 'Monetization',
  },
  {
    name: 'Support',
    description: 'Accept support from your followers',
    icon: 'https://pub-f04d991f504d49319db6937c4f8e1156.r2.dev/support-block.webp',
    block_type: 'SUPPORT',
    category_name: 'Monetization',
  },
  {
    name: 'Appointments',
    description: 'Make money from calendar bookings',
    icon: 'https://pub-f04d991f504d49319db6937c4f8e1156.r2.dev/booking-block.webp',
    block_type: 'APPOINTMENT',
    category_name: 'Monetization',
  },
  {
    name: 'Fan Request',
    description: 'Make money from paid request',
    icon: 'https://pub-f04d991f504d49319db6937c4f8e1156.r2.dev/requests-block.webp',
    block_type: 'FAN_REQUEST',
    category_name: 'Monetization',
  },
  {
    name: 'TikTok shopping',
    description: 'Sell from your TikTok videos',
    icon: 'https://pub-f04d991f504d49319db6937c4f8e1156.r2.dev/tiktok_shop.webp',
    block_type: 'TIKTOK_SHOP',
    category_name: 'Monetization',
  },
  {
    name: 'Email & SMS',
    description: 'Collect emails and phone numbers',
    icon: 'https://pub-f04d991f504d49319db6937c4f8e1156.r2.dev/email-block-sms.webp',
    block_type: 'EMAIL_SMS',
    category_name: 'Audience building',
  },
  {
    name: 'Tour and Events',
    description: 'Embed BandsInTown and Seated events',
    icon: 'https://pub-f04d991f504d49319db6937c4f8e1156.r2.dev/tours-and-events-block.webp',
    block_type: 'TOUR_AND_EVENTS',
    category_name: 'Audience building',
  },
  {
    name: 'Email Signup',
    description: 'Collect emails',
    icon: 'https://pub-f04d991f504d49319db6937c4f8e1156.r2.dev/collect_email.webp',
    block_type: 'EMAIL_SIGNUP',
    category_name: 'Audience building',
  },
  {
    name: 'SMS Signup',
    description: 'Collect phone numbers',
    icon: 'https://pub-f04d991f504d49319db6937c4f8e1156.r2.dev/email-block-sms.webp',
    block_type: 'SMS_SIGNUP',
    category_name: 'Audience building',
  },
  {
    name: 'Friends',
    description: 'Link to your friends on APP and social',
    icon: 'https://pub-f04d991f504d49319db6937c4f8e1156.r2.dev/friends.webp',
    block_type: 'FRIEND',
    category_name: 'Audience building',
  },
  {
    name: 'Mailchimp',
    description: 'Connect your Mailchimp account',
    icon: 'https://pub-f04d991f504d49319db6937c4f8e1156.r2.dev/collect_email_mailchimp.webp',
    block_type: 'MAILCHIMP',
    category_name: 'Audience building',
  },
  {
    name: 'Follower Count',
    description: 'Show your follower count',
    icon: 'https://pub-f04d991f504d49319db6937c4f8e1156.r2.dev/follower_count.webp',
    block_type: 'FOLLOWER_COUNT',
    category_name: 'Audience building',
  },
  {
    name: 'Buy Me a Coffee',
    description: 'Display your Buy Me a Coffee donation panel',
    icon: 'https://pub-f04d991f504d49319db6937c4f8e1156.r2.dev/buy_me_a_coffee.webp',
    block_type: 'BUY_ME_A_COFFEE',
    category_name: 'Integrations',
  },
  {
    name: 'Ko-fi',
    description: 'Accept donation and memberships',
    icon: 'https://pub-f04d991f504d49319db6937c4f8e1156.r2.dev/ko-fi.webp',
    block_type: 'KOFI',
    category_name: 'Integrations',
  },
]

const _uiKitBlockSites = [
  {
    name: 'Hero',
    description: 'Display a prominent headline with a call to action',
    icon: 'https://pub-f04d991f504d49319db6937c4f8e1156.r2.dev/hero-block.webp',
    block_type: 'HERO',
    category_name: 'Content',
  },
  {
    name: 'Content',
    description: 'Add multi-column text and images',
    icon: 'https://pub-f04d991f504d49319db6937c4f8e1156.r2.dev/text.webp',
    block_type: 'CONTENT',
    category_name: 'Content',
  },
  // {
  //   name: 'Store',
  //   description: 'Sell your products',
  //   icon: 'https://pub-f04d991f504d49319db6937c4f8e1156.r2.dev/store.webp',
  //   block_type: 'STORE',
  //   category_name: 'Ecommerce and Marketing',
  // },
  {
    name: 'Email',
    description: 'Collect emails',
    icon: 'https://pub-f04d991f504d49319db6937c4f8e1156.r2.dev/collect_email.webp',
    block_type: 'EMAIL',
    category_name: 'Ecommerce and Marketing',
  },
  {
    name: 'Image & Video',
    description: 'Add gallery of images and videos',
    icon: 'https://pub-f04d991f504d49319db6937c4f8e1156.r2.dev/images.webp',
    block_type: 'IMAGE_VIDEO',
    category_name: 'Media',
  },
  // {
  //   name: 'Instagram Feed',
  //   description: 'Showcase your recent Instagram posts',
  //   icon: 'https://pub-f04d991f504d49319db6937c4f8e1156.r2.dev/instagram_feed.webp',
  //   block_type: 'INSTAGRAM_FEED',
  //   category_name: 'Media',
  // },
  {
    name: 'Youtube',
    description: 'Add your Youtube videos',
    icon: 'https://pub-f04d991f504d49319db6937c4f8e1156.r2.dev/youtube_video.webp',
    block_type: 'YOUTUBE',
    category_name: 'Media',
  },
  // {
  //   name: 'TikTok',
  //   description: 'Add your TikTok videos',
  //   icon: 'https://pub-f04d991f504d49319db6937c4f8e1156.r2.dev/tiktok_video.webp',
  //   block_type: 'TIKTOK',
  //   category_name: 'Media',
  // },
  {
    name: 'Navigation',
    description: 'Add a navigation menu',
    icon: 'https://pub-f04d991f504d49319db6937c4f8e1156.r2.dev/navigation.webp',
    block_type: 'NAVIGATION',
    category_name: 'Layout',
  },
  {
    name: 'Footer',
    description: 'Add a footer',
    icon: 'https://pub-f04d991f504d49319db6937c4f8e1156.r2.dev/footer.webp',
    block_type: 'FOOTER',
    category_name: 'Layout',
  },
]

export async function seedUIKitBlocks() {
  console.log('\t🌱 Seeding UI kit block...')

  const _blocks = [..._uiKitBlocks, ..._uiKitBlockSites]

  for (const uiKitBlock of _blocks) {
    const existingUIKitBlock = await prisma.uiKitBlock.findFirst({
      where: { name: uiKitBlock.name },
    })

    if (existingUIKitBlock) {
      console.log(`\t❌ UI kit block already exists: ${uiKitBlock.name}`)
      continue
    }

    const group = await prisma.uiKitBlockGroup.findFirst({
      where: { name: uiKitBlock.category_name },
    })

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { category_name, ...rest } = uiKitBlock

    await prisma.uiKitBlock.create({
      data: {
        ...rest,
        ui_kit_block_group: {
          connect: {
            id: group?.id,
          },
        },
      },
    })
  }

  console.log('\t🌱 Seeding UI kit block...')
}
