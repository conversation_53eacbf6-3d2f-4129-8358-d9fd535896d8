// import { PrismaClient } from '@prisma/client';

// const prisma = new PrismaClient();

// ////////////////////////////////////////////////////////
// // 🌱 UI Kit Site Groups
// ////////////////////////////////////////////////////////

// const _uiKitSiteGroups = [
//   {
//     name: 'Media',
//     description: 'Media',
//     block: ['TikTok', 'Youtube', 'Instagram Feed', 'Image & Video'],
//     rank: 1,
//   },
//   {
//     name: 'Ecommerce and Marketing',
//     description: 'Social',
//     blocks: ['Email', 'Store'],
//     rank: 2,
//   },
//   {
//     name: 'Layout and Content',
//     description: 'Layout and Content',
//     blocks: ['Content', 'Hero'],
//     rank: 3,
//   },
// ];

// export async function seedUIKitSiteGroups() {
//   await prisma.uiKitSiteGroup.createMany({
//     data: _uiKitSiteGroups,
//   });

//   console.log('UI Kit Site Groups seeded');
// }

// ////////////////////////////////////////////////////////
// // 🌱 UI Kit Sites
// ////////////////////////////////////////////////////////

// const _uiKitSites = [
//   {
//     name: 'Hero',
//     description: 'Display a prominent headline with a call to action',
//     icon: 'https://pub-f04d991f504d49319db6937c4f8e1156.r2.dev/hero-block.webp',
//     block_type: 'HERO',
//     group: 'Layout and Content',
//     is_display: true,
//   },
//   {
//     name: 'Content',
//     description: 'Add multi-column text and images',
//     icon: 'https://pub-f04d991f504d49319db6937c4f8e1156.r2.dev/text.webp',
//     block_type: 'CONTENT',
//     group: 'Layout and Content',
//     is_display: true,
//   },
//   {
//     name: 'Store',
//     description: 'Sell your products',
//     icon: 'https://pub-f04d991f504d49319db6937c4f8e1156.r2.dev/store.webp',
//     block_type: 'STORE',
//     group: 'Ecommerce and Marketing',
//     is_display: true,
//   },
//   {
//     name: 'Email',
//     description: 'Collect emails',
//     icon: 'https://pub-f04d991f504d49319db6937c4f8e1156.r2.dev/collect_email.webp',
//     block_type: 'EMAIL',
//     group: 'Ecommerce and Marketing',
//     is_display: true,
//   },
//   {
//     name: 'Image & Video',
//     description: 'Add gallery of images and videos',
//     icon: 'https://pub-f04d991f504d49319db6937c4f8e1156.r2.dev/images.webp',
//     block_type: 'IMAGE_VIDEO',
//     group: 'Media',
//     is_display: true,
//   },
//   {
//     name: 'Instagram Feed',
//     description: 'Showcase your recent Instagram posts',
//     icon: 'https://pub-f04d991f504d49319db6937c4f8e1156.r2.dev/instagram_feed.webp',
//     block_type: 'INSTAGRAM_FEED',
//     group: 'Media',
//     is_display: true,
//   },
//   {
//     name: 'Youtube',
//     description: 'Add your Youtube videos',
//     icon: 'https://pub-f04d991f504d49319db6937c4f8e1156.r2.dev/youtube_video.webp',
//     block_type: 'YOUTUBE',
//     group: 'Media',
//     is_display: true,
//   },
//   {
//     name: 'TikTok',
//     description: 'Add your TikTok videos',
//     icon: 'https://pub-f04d991f504d49319db6937c4f8e1156.r2.dev/tiktok_video.webp',
//     block_type: 'TIKTOK_VIDEO',
//     group: 'Media',
//     is_display: true,
//   },
//   {
//     name: 'Navigation',
//     description: 'Add a navigation menu',
//     icon: 'https://pub-f04d991f504d49319db6937c4f8e1156.r2.dev/navigation.webp',
//     block_type: 'NAVIGATION',
//     group: 'Layout and Content',
//     is_display: false,
//   },
//   {
//     name: 'Footer',
//     description: 'Add a footer',
//     icon: 'https://pub-f04d991f504d49319db6937c4f8e1156.r2.dev/footer.webp',
//     block_type: 'FOOTER',
//     group: 'Layout and Content',
//     is_display: false,
//   },
// ];

// export async function seedUIKitSites() {
//   await prisma.uiKitSite.createMany({
//     data: _uiKitSites,
//   });

//   console.log('UI Kit Sites seeded');
// }
