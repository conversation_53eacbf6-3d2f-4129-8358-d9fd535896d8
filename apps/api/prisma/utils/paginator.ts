export interface PaginatedResult<T> {
  data: T[]
  metadata: {
    total: number // total items
    page: number // current page
    take: number // per page
    last_page: number // last page
    prev: number | null // previous page
    next: number | null // next page
  }
}

export type PaginateOptions = {
  page?: number | string
  take?: number | string
}

export type PaginateFunction<T, K> = (
  model: any,
  args?: K,
  options?: PaginateOptions,
) => Promise<PaginatedResult<T>>

const defaultOptions: PaginateOptions = {
  page: 1,
  take: 10,
}

export const paginator = async <T>(
  model: any,
  args: any,
  options: PaginateOptions = defaultOptions,
): Promise<PaginatedResult<T>> => {
  const page = Math.max(
    Number(options.page) || Number(defaultOptions.page) || 1,
    1,
  )

  const perPage = Math.max(
    Number(options.take) || Number(defaultOptions.take) || 10,
    1,
  )

  const skip = (page - 1) * perPage

  const [total, data] = await Promise.all([
    model.count({ where: args.where }),
    model.findMany({
      ...args,
      take: perPage,
      skip,
    }),
  ])

  const lastPage = Math.ceil(total / perPage)

  return {
    data,
    metadata: {
      total,
      page,
      take: perPage,
      last_page: lastPage,
      prev: page > 1 ? page - 1 : null,
      next: page < lastPage ? page + 1 : null,
    },
  }
}
