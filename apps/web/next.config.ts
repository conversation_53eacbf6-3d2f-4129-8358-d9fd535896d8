import type { NextConfig } from 'next'

/** @type {import('next').NextConfig} */
const nextConfig: NextConfig = {
  // i18n: {
  //   defaultLocale: 'en',
  //   locales: ['en', 'vi'],
  // },
  images: {
    remotePatterns: [
      {
        hostname: '**',
      },
    ],
  },
  turbopack: {
    rules: {
      '*.po': {
        loaders: ['@lingui/loader'],
        as: '*.js',
      },
    },
  },
  experimental: {
    swcPlugins: [['@lingui/swc-plugin', {}]],
  },
  webpack: (config) => {
    config.module.rules.push({
      test: /\.po$/,
      use: {
        loader: '@lingui/loader',
      },
    })

    return config
  },
}

export default nextConfig
