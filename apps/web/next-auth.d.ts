import { UserRole } from '@prisma/client'

import { DefaultSession } from 'next-auth'

export type ExtendedUser = DefaultSession['user'] & {
  id: string
  role: UserRole
  isTwoFactorEnabled: boolean
  isOAuth: boolean
  access_token: string
  refresh_token: string
}

// this process is know as module augmentation
declare module 'next-auth' {
  interface Session extends DefaultSession {
    user: ExtendedUser
    status: 'unauthenticated' | 'loading' | 'authenticated'
    error?: 'RefreshTokenError' | 'RefreshAccessTokenError'
  }
}
declare module 'next-auth/jwt' {
  interface JWT {
    access_token: string
    expires_at: number
    refresh_token?: string
    error?: 'RefreshTokenError' | 'RefreshAccessTokenError'
  }
}
