const locales = process.env.NEXT_PUBLIC_LANGUAGES?.split(',') || ['en', 'vi']
const defaultLocale = process.env.NEXT_PUBLIC_DEFAULT_LOCALE || 'en'

/** @type {import('@lingui/conf').LinguiConfig} */
const config = {
  locales,
  sourceLocale: defaultLocale,
  catalogs: [
    {
      path: '<rootDir>/src/translations/locales/{locale}/messages',
      include: ['src/'],
    },
  ],
  fallbackLocales: {
    default: defaultLocale,
  },
}
export default config
