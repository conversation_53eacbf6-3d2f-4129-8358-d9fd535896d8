'use client'

import { But<PERSON> } from '@ttplatform/ui/components'
import { ArrowLeft, Settings } from 'lucide-react'
import { useState } from 'react'

export function TopBarPanel() {
  const [url, setUrl] = useState('https://websites.design')
  console.log('🚀 ~ TopBarPanel ~ setUrl:', typeof setUrl)
  // const [isPublished, setIsPublished] = useState(false)

  return (
    <div className="flex items-center justify-between w-full h-16 px-4 bg-white border-b border-gray-200">
      <div className="flex items-center gap-4">
        <button className="p-1 hover:bg-gray-100 rounded-md">
          <ArrowLeft className="w-5 h-5 text-gray-600" />
        </button>
        <span className="font-medium text-gray-800">New Website</span>
        <div className="flex items-center gap-2 ml-2">
          <span className="text-sm text-gray-500">{url}</span>
          <Button
            variant="destructive"
            size="sm"
            className="h-7 px-3 text-xs bg-red-500 hover:bg-red-600"
          >
            EDIT URL
          </Button>
        </div>
      </div>
      <div className="flex items-center gap-3">
        <span className="text-sm text-gray-500">Unpublished</span>
        <button className="p-1 hover:bg-gray-100 rounded-md">
          <Settings className="w-5 h-5 text-gray-600" />
        </button>
        <Button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded">
          Publish
        </Button>
      </div>
    </div>
  )
}
