import { cn } from '@ttplatform/ui/lib'
import { Plus } from 'lucide-react'
import React from 'react'
import { PageAddIcon } from '../..'

interface CollapsedSidebarContentProps {
  isCollapsed: boolean
  selectedIcon: string | null
  setSelectedIcon: (icon: string) => void
  setIsCollapsed: React.Dispatch<React.SetStateAction<boolean>>
}

export const CollapsedSidebarContent: React.FC<
  CollapsedSidebarContentProps
> = ({ isCollapsed, selectedIcon, setSelectedIcon, setIsCollapsed }) => {
  const handleIconClick = (icon: string) => {
    setSelectedIcon(icon)
    setIsCollapsed(false)
  }

  return (
    <div
      className={cn(
        'flex flex-col items-center gap-6 p-3',
        `${isCollapsed ? '' : 'border-r'}`,
      )}
    >
      <button
        onClick={() => handleIconClick('pages')}
        className={cn(
          'p-2 rounded-md hover:bg-sidebar-accent',
          selectedIcon === 'pages' && 'bg-sidebar-accent',
        )}
      >
        <PageAddIcon />
      </button>
      <button
        onClick={() => handleIconClick('blocks')}
        className={cn(
          'p-2 rounded-md hover:bg-sidebar-accent',
          selectedIcon === 'blocks' && 'bg-sidebar-accent',
        )}
      >
        <Plus size={20} />
      </button>
    </div>
  )
}
