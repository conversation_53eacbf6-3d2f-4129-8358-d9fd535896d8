'use client'

import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@ttplatform/ui/components'
import { cn } from '@ttplatform/ui/lib'
import { Home } from 'lucide-react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { useState } from 'react'

export default function HomeSection() {
  const pathname = usePathname()
  const [isOpen, setIsOpen] = useState(false)

  const navLinks = [
    { href: '/', label: 'Đầu trang', special: true },
    { href: '/welcome', label: 'WELCOME' },
    { href: '/services', label: 'OUR SERVICES' },
    { href: '/about', label: 'ABOUT' },
    { href: '/clients', label: 'CLIENTS' },
    { href: '/projects', label: 'PROJECTS' },
    { href: '/promo', label: 'PROMOTIONAL' },
    { href: '/footer', label: 'Chân trang', special: true },
  ]

  return (
    <Collapsible open={isOpen} onOpenChange={setIsOpen}>
      <CollapsibleTrigger asChild>
        <button className="flex w-full items-center gap-2 py-2 px-3 text-sm font-medium rounded-md hover:bg-sidebar-accent transition-colors">
          <Home size={18} className="shrink-0" />
          <span className="flex-1">Trang (Home)</span>
          <span className="text-xs">{isOpen ? '▼' : '►'}</span>
        </button>
      </CollapsibleTrigger>
      <CollapsibleContent className="ml-7 mt-1 space-y-1">
        {navLinks.map(({ href, label, special }) => (
          <Link
            key={href}
            href={href}
            className={cn(
              'flex items-center gap-2 py-1.5 px-3 text-sm rounded-md hover:bg-sidebar-accent transition-colors',
              pathname === href
                ? 'text-sidebar-accent-foreground font-medium'
                : 'text-sidebar-foreground',
            )}
          >
            <span
              className={cn(
                'w-4 h-4 flex items-center justify-center text-xs',
                special && 'bg-green-100 text-green-800 rounded-sm',
              )}
            >
              {special ? '□' : '◻'}
            </span>
            <span>{label}</span>
          </Link>
        ))}
      </CollapsibleContent>
    </Collapsible>
  )
}
