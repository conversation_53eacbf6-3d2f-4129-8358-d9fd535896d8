'use client'
import { cn } from '@ttplatform/ui/lib'
import React, { useState } from 'react'
import { CollapsedSidebarContent } from './collapsed-sidebar-content'
import { ExpandedSidebarContent } from './expanded-sidebar-content'
import SidebarFooter from './sidebar-footer'
import { SidebarHeader } from './sidebar-header'
interface SidebarProps {
  isCollapsed?: boolean
  setIsCollapsed?: React.Dispatch<React.SetStateAction<boolean>>
}

export const Sidebar: React.FC<SidebarProps> = ({
  isCollapsed = false,
  setIsCollapsed,
}) => {
  const [selectedIcon, setSelectedIcon] = useState<string | null>('pages')

  return (
    <div
      className={cn(
        'h-screen bg-sidebar border-r border-sidebar-border transition-all duration-300 flex flex-col',
        isCollapsed ? 'w-16' : 'w-80',
      )}
    >
      <SidebarHeader
        isCollapsed={isCollapsed}
        setIsCollapsed={
          setIsCollapsed as React.Dispatch<React.SetStateAction<boolean>>
        }
      />

      {/* <SidebarSearch isCollapsed={isCollapsed} /> */}

      <div className="flex-1 flex overflow-auto">
        <CollapsedSidebarContent
          isCollapsed={isCollapsed}
          selectedIcon={selectedIcon}
          setSelectedIcon={setSelectedIcon}
          setIsCollapsed={
            setIsCollapsed as React.Dispatch<React.SetStateAction<boolean>>
          }
        />
        {!isCollapsed ? (
          <ExpandedSidebarContent
            selectedIcon={selectedIcon}
            setSelectedIcon={setSelectedIcon}
          />
        ) : null}
      </div>

      <SidebarFooter isCollapsed={isCollapsed} />
    </div>
  )
}
