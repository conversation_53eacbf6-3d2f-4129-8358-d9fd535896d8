import React from 'react'
import { BlocksSection } from './sidebar-sections/blocks-section'
import PagesSection from './sidebar-sections/pages-section'

interface ExpandedSidebarContentProps {
  selectedIcon: string | null
  setSelectedIcon: (icon: string | null) => void
}

export const ExpandedSidebarContent: React.FC<ExpandedSidebarContentProps> = ({
  selectedIcon,
  // biome-ignore lint/correctness/noUnusedVariables: <explanation>
  setSelectedIcon,
}) => {
  return (
    <div className="space-y-1 w-full px-2">
      {selectedIcon === 'pages' && <PagesSection />}
      {selectedIcon === 'blocks' && <BlocksSection />}
    </div>
  )
}
