import {
  BConsultingByCategory,
  BProductHeading,
} from '@ttplatform/core-page-builder/components'
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@ttplatform/ui/components'
import { CompareDetails } from './components/compare-details'
import CompareList from './components/compare-list'
//---------------------------------------------------------------------------------
const _LIST_PRODUCT_COMPARE = [
  {
    id: 1,
    name: '<PERSON><PERSON><PERSON> xúc đào thủy lực Cat 321 Gx',
    product_code: '321-GX',
    image: '/images/products/320-GX.png',
  },
  {
    id: 2,
    name: '<PERSON><PERSON><PERSON> xúc đào thủy lực Cat 322 Gx',
    product_code: '322-GX',
    image: '/images/products/320-GX.png',
  },
  {
    id: 3,
    name: '<PERSON><PERSON><PERSON> xúc đào thủy lực Cat 323 Gx',
    product_code: '323-GX',
    image: '/images/products/320-GX.png',
  },
  {
    id: 4,
    name: 'Máy xúc đào thủy lực Cat 324 Gx',
    product_code: '324-GX',
    image: '/images/products/320-GX.png',
  },
  {
    id: 5,
    name: 'Máy xúc đào thủy lực Cat 325 Gx',
    product_code: '325-GX',
    image: '/images/products/320-GX.png',
  },
  {
    id: 6,
    name: 'Máy xúc đào thủy lực Cat 326 Gx',
    product_code: '326-GX',
    image: '/images/products/320-GX.png',
  },
]

const _HEADING = {
  title: 'SO SÁNH SẢN PHẨM',
  buttons: [],
}
//---------------------------------------------------------------------------------
export default function CompareView() {
  const renderBreadcrumb = (
    <div className="w-full container max-w-screen-xl mx-auto px-4">
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/">Trang chủ</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink>Sản phẩm</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>SO SÁNH SẢN PHẨM</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>
    </div>
  )
  return (
    <div className="flex flex-col">
      <BProductHeading heading={_HEADING} breadcrumb={renderBreadcrumb} />

      <CompareList listProduct={_LIST_PRODUCT_COMPARE} />
      <CompareDetails />
      <BConsultingByCategory category="Máy đào thủy lực CAT" />
    </div>
  )
}
