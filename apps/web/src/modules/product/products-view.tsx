import {
  // BProductCategories,
  BProductHeading,
} from '@ttplatform/core-page-builder/components'
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@ttplatform/ui/components'
import { MessageCircle, Pen, Phone } from 'lucide-react'
//---------------------------------------------------------------------------------
const _HEADING = {
  title: ' THE CAT EQUIPMENT SETS THE STANDARD FOR OUR INDUSTRY',
  description:
    'Phu Thai Cat supplies new, used and rental Cat product including Machines, Power Systems, Marine and Forklift Truck. The Cat equipment product line, consisting of more than 300 machines, sets the standard for our industry. We plan to help you meet your needs with our equipment, with our distribution and product support system, and the continual introduction and updating of products.',
  buttons: [
    {
      icon: <Phone />,
      text: '1800 599 990',
      url: '#',
    },
    {
      icon: <Pen />,
      text: 'YÊU CẦU BÁO GIÁ',
      url: '#',
    },
    {
      icon: <MessageCircle />,
      text: 'CHAT NGAY',
      url: '#',
    },
  ],
  image: '/images/products/product-hero.png',
}
//---------------------------------------------------------------------------------
export default function ProductsView() {
  const renderBreadcrumb = (
    <div className="w-full container max-w-screen-xl mx-auto px-4">
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/">Trang chủ</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Sản phẩm</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>
    </div>
  )
  return (
    <>
      <BProductHeading heading={_HEADING} breadcrumb={renderBreadcrumb} />
      {/* <BProductCategories /> */}
    </>
  )
}
