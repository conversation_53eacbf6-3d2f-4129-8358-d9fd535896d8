'use client'

import { motion } from 'framer-motion'
import * as React from 'react'

import {
  DropdownMenu,
  DropdownMenuTrigger,
  SidebarMenu,
  SidebarMenuItem,
  useSidebar,
} from '@ttplatform/ui/components'

export function TeamSwitcher({
  teams,
}: {
  teams: {
    name: string
    expandedLogo: React.ReactNode
    collapsedLogo: React.ReactNode
    plan: string
  }[]
}) {
  const { state } = useSidebar()
  // const [activeTeam, setActiveTeam] = React.useState(teams[0]);
  const activeTeam = teams[0]

  if (!activeTeam) {
    return null
  }

  const logo =
    state === 'expanded' ? activeTeam.expandedLogo : activeTeam.collapsedLogo

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            {/* <SidebarMenuButton
              size="lg"
              className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
            > */}
            <motion.div
              className="text-sidebar-primary-foreground flex h-8 w-full rounded-lg bg-transparent"
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.3, ease: 'easeInOut' }}
              key={state}
            >
              {logo}
            </motion.div>
            {/* <div className="grid flex-1 text-left text-sm leading-tight">
                <span className="truncate font-medium">{activeTeam.name}</span>
                <span className="truncate text-xs">{activeTeam.plan}</span>
              </div> */}
            {/* <ChevronsUpDown className="ml-auto" /> */}
            {/* </SidebarMenuButton> */}
          </DropdownMenuTrigger>
          {/* <DropdownMenuContent
            className="w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg"
            align="start"
            side={isMobile ? "bottom" : "right"}
            sideOffset={4}
          >
            <DropdownMenuLabel className="text-muted-foreground text-xs">
              Teams
            </DropdownMenuLabel>
            {teams.map((team, index) => (
              <DropdownMenuItem
                key={team.name}
                onClick={() => setActiveTeam(team)}
                className="gap-2 p-2"
              >
                <div className="flex size-6 items-center justify-center rounded-md border">
                  {team.logo}
                </div>
                {team.name}
                <DropdownMenuShortcut>⌘{index + 1}</DropdownMenuShortcut>
              </DropdownMenuItem>
            ))}
            <DropdownMenuSeparator />
            <DropdownMenuItem className="gap-2 p-2">
              <div className="flex size-6 items-center justify-center rounded-md border bg-transparent">
                <Plus className="size-4" />
              </div>
              <div className="text-muted-foreground font-medium">Add team</div>
            </DropdownMenuItem>
          </DropdownMenuContent> */}
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  )
}
