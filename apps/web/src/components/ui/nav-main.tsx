'use client'

import React from 'react'

import { AnimatePresence, motion } from 'framer-motion'
import { ChevronRight, MoreHorizontalIcon } from 'lucide-react'

import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuAction,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  useSidebar,
} from '@ttplatform/ui/components'
import { cn } from '@ttplatform/ui/lib'
import { usePathname } from 'next/navigation'
import CustomLink from '../custom-link'

export type T_NavMain = {
  name?: string
  items: {
    title: string
    url: string
    icon?: React.ReactNode
    isActive?: boolean
    items?: {
      title: string
      url: string
      icon?: React.ReactNode
      isActive?: boolean
      items?: {
        title: string
        url: string
      }[]
    }[]
  }[]
}

const isActiveParent = (url: string, pathname: string) => {
  const locale = pathname.split('/')[1]
  let cleanPathname = pathname.replace(`/${locale}`, '')
  if (cleanPathname === '') cleanPathname = '/'
  const isHome = url === '/'
  if (isHome) return cleanPathname === url
  return cleanPathname.startsWith(url)
}

const isActiveChild = (url: string, pathname: string) => {
  const locale = pathname.split('/')[1]
  let cleanPathname = pathname.replace(`/${locale}`, '')
  if (cleanPathname === '') cleanPathname = '/'
  const isHome = url === '/'
  if (isHome) return cleanPathname === url
  return cleanPathname === url
}

const subMenuVariants = {
  open: {
    height: 'auto',
    opacity: 1,
    transition: {
      duration: 0.3,
      ease: 'easeInOut',
    },
  },
  closed: {
    height: 0,
    opacity: 0,
    transition: {
      duration: 0.3,
      ease: 'easeInOut',
    },
  },
}

const subItemVariants = {
  hidden: { opacity: 0, y: -5 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.2,
      ease: 'easeOut',
    },
  },
}

const barVariants = {
  active: {
    opacity: 1,
    scaleY: 1,
    transition: { duration: 0.2, ease: 'easeInOut' },
  },
  inactive: {
    opacity: 0,
    scaleY: 0,
    transition: { duration: 0.2, ease: 'easeInOut' },
  },
}

export function NavMain({ name, items }: T_NavMain) {
  const pathname = usePathname()
  const { isMobile } = useSidebar()

  const renderSingleLevel = (item: T_NavMain['items'][number]) => {
    const active = isActiveParent(item.url, pathname)
    return (
      <SidebarMenuItem>
        <SidebarMenuButton
          tooltip={item.title}
          className={cn(
            active
              ? 'rounded-md border border-gray-100 bg-gray-50 font-semibold text-gray-800'
              : 'text-gray-600 hover:bg-gray-100',
            'px-3',
          )}
          asChild
        >
          <CustomLink
            href={item.url || '#'}
            className="flex h-10 items-center gap-2"
          >
            {item.icon && item.icon}
            <span>{item.title}</span>
          </CustomLink>
        </SidebarMenuButton>
      </SidebarMenuItem>
    )
  }

  const renderDropdown = (item: T_NavMain['items'][number]) => {
    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <SidebarMenuAction className="data-[state=open]:bg-accent !mx-0 cursor-pointer rounded-sm !px-0">
            <MoreHorizontalIcon className="absolute right-0" />
            <span className="sr-only">More</span>
          </SidebarMenuAction>
        </DropdownMenuTrigger>
        <DropdownMenuContent
          className="w-fit rounded-lg p-3"
          side={isMobile ? 'bottom' : 'right'}
          align={isMobile ? 'end' : 'start'}
        >
          {item.items?.map((subItem) => (
            <DropdownMenuItem key={subItem.title} className="cursor-pointer">
              <span>{subItem.title}</span>
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    )
  }

  const renderMultiLevels = (item: any) => {
    if (!item.items?.length) return null

    return (
      <Collapsible
        key={item.title}
        asChild
        defaultOpen={isActiveParent(item.url, pathname)}
        className="group/collapsible"
      >
        <SidebarMenuItem>
          <CollapsibleTrigger asChild>
            <SidebarMenuButton
              tooltip={item.title}
              className={cn(
                isActiveParent(item.url, pathname) &&
                  'rounded-md border border-gray-100 bg-gray-50 font-medium text-gray-800',
                'h-10 px-3 text-gray-600 group-data-[state=open]/collapsible:pr-2',
              )}
            >
              {item.icon && item.icon}
              <span>{item.title}</span>
              <ChevronRight className="absolute right-3 transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
            </SidebarMenuButton>
          </CollapsibleTrigger>
          <CollapsibleContent>
            <AnimatePresence>
              <motion.div
                variants={subMenuVariants as any}
                initial="closed"
                animate="open"
                exit="closed"
              >
                <SidebarMenuSub className="!mr-0 overflow-hidden">
                  {item.items?.map((subItem: any, index: number) => (
                    <SidebarMenuSubItem
                      key={subItem.title}
                      className="relative"
                    >
                      <motion.div
                        variants={subItemVariants as any}
                        initial="hidden"
                        animate="visible"
                        transition={{ delay: index * 0.03 }}
                      >
                        <motion.div
                          className="bg-logo absolute top-1/2 -left-[11px] z-10 h-full w-[2px] -translate-y-1/2"
                          variants={barVariants as any}
                          initial="inactive"
                          animate={
                            isActiveChild(subItem.url, pathname)
                              ? 'active'
                              : 'inactive'
                          }
                        />
                        <SidebarMenuSubButton asChild>
                          <div>
                            <CustomLink
                              href={subItem.url}
                              className={cn(
                                isActiveChild(subItem.url, pathname) &&
                                  'text-logo flex h-6 items-center gap-2 font-medium',
                              )}
                            >
                              <span>{subItem.title}</span>
                            </CustomLink>
                            {subItem.items?.length
                              ? renderDropdown(subItem)
                              : null}
                          </div>
                        </SidebarMenuSubButton>
                      </motion.div>
                    </SidebarMenuSubItem>
                  ))}
                </SidebarMenuSub>
              </motion.div>
            </AnimatePresence>
          </CollapsibleContent>
        </SidebarMenuItem>
      </Collapsible>
    )
  }

  return (
    <SidebarGroup>
      <SidebarGroupLabel className="font-semibold text-gray-500 uppercase">
        {name}
      </SidebarGroupLabel>
      <SidebarMenu>
        {items.map((item, idx) => (
          <React.Fragment key={idx}>
            {item.items?.length
              ? renderMultiLevels(item)
              : renderSingleLevel(item)}
          </React.Fragment>
        ))}
      </SidebarMenu>
    </SidebarGroup>
  )
}
