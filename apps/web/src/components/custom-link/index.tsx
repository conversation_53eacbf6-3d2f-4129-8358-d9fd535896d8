'use client'

import { cn } from '@ttplatform/ui/lib'
import Link from 'next/link'
import { useParams } from 'next/navigation'
import React from 'react'

type TCustomLinkProps = {
  children?: React.ReactNode
  href: string
  className?: string
  onClick?: () => void
  passHref?: true
  locale?: string
  anchor?: boolean
  [x: string]: any
} & React.ComponentPropsWithoutRef<'a'>

const CustomLink = ({
  children,
  href,
  locale,
  passHref,
  anchor,
  ...props
}: TCustomLinkProps) => {
  const { locale: localeParam } = useParams()

  const onClick = () => {
    if (anchor) {
      const element = document.getElementById(href.replace('#', ''))
      if (element) {
        element.scrollIntoView({ behavior: 'smooth' })
      }
    }
  }

  if (href.startsWith('http')) {
    return (
      <a
        href={href}
        target="_blank"
        rel="noopener noreferrer"
        className={cn('cursor-pointer', props.className)}
        {...props}
      >
        {children}
      </a>
    )
  }

  if (anchor) {
    return (
      <div
        id={href.replace('#', '')}
        className={cn('cursor-pointer', props.className)}
        onClick={onClick}
      >
        {children}
      </div>
    )
  }

  return (
    <Link
      href={`/${locale || (localeParam as string)}${href}`}
      locale={locale || (localeParam as string)}
      passHref={passHref}
      className={cn('cursor-pointer', props.className)}
      {...props}
    >
      {children}
    </Link>
  )
}

export default CustomLink
