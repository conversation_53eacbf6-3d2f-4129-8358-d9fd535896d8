'use client'

import {
  Avatar,
  AvatarFallback,
  AvatarImage,
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
  Button,
  Separator,
  SidebarTrigger,
} from '@ttplatform/ui/components'
import { cn } from '@ttplatform/ui/lib'
import { BellRing, HelpCircle } from 'lucide-react'
import { useSession } from 'next-auth/react'
import React from 'react'

type T_TopBarProps = {
  title?: string
  items?: {
    label: string
    href?: string
    icon?: React.ReactNode
    isActive?: boolean
  }[]
}

export default function AppTopBar({
  // title = "AFFION",
  items = [],
}: T_TopBarProps) {
  const { data: session } = useSession()

  const user = session?.user

  const fullName = user?.name ?? 'Admin'

  return (
    <header className="flex h-18 items-center gap-2 border-b px-4">
      <div className="flex items-center gap-2">
        <SidebarTrigger />
        <Separator orientation="vertical" className="h-4" />
        {items?.length > 0 && (
          <Breadcrumb>
            <BreadcrumbList>
              {/* <BreadcrumbItem className="hidden md:block">
                <BreadcrumbLink href="#">{title}</BreadcrumbLink>
              </BreadcrumbItem> */}
              {items.map((item, index) => (
                <React.Fragment key={index}>
                  <BreadcrumbItem>
                    {item.isActive ? (
                      <BreadcrumbPage
                        className={`${items.length === 1 ? 'font-semibold text-gray-800' : ''} ${items.length >= 2 && index === 0 ? 'text-gray-600' : ''} `}
                      >
                        <span className="text-base">{item.label}</span>
                      </BreadcrumbPage>
                    ) : item.href ? (
                      <BreadcrumbLink
                        href={item.href}
                        className={`${items.length === 1 ? 'font-semibold text-gray-800' : ''} ${items.length >= 2 && index === 0 ? 'text-gray-600' : ''} text-xl`}
                      >
                        <span className="text-base">{item.label}</span>
                      </BreadcrumbLink>
                    ) : (
                      <span className="text-base">{item.label}</span>
                    )}
                  </BreadcrumbItem>
                  {index < items.length - 1 && (
                    <BreadcrumbSeparator className="hidden md:block">
                      <span className="text-gray-400">/</span>
                    </BreadcrumbSeparator>
                  )}
                </React.Fragment>
              ))}
            </BreadcrumbList>
          </Breadcrumb>
        )}
      </div>

      <div className="ml-auto flex h-10 items-center gap-x-4">
        <div className="flex items-center gap-x-3">
          <Button variant="outline" className="gap-2">
            <HelpCircle className="h-4 w-4" /> Need help
          </Button>

          <Button variant="outline" className={cn('relative size-10 p-0')}>
            <BellRing className="h-4 w-4" />
            <div className="absolute top-1 right-0.5 flex size-4 items-center justify-center rounded-full border border-white bg-red-500 text-[10px] text-white">
              8
            </div>
          </Button>

          <Avatar>
            <AvatarImage src={user?.image ?? ''} />
            <AvatarFallback>{fullName.slice(0, 2)}</AvatarFallback>
          </Avatar>
        </div>
      </div>
    </header>
  )
}
