'use client'

import * as React from 'react'

import {
  Sidebar,
  Sidebar<PERSON>ontent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
} from '@ttplatform/ui/components'
import { cn } from '@ttplatform/ui/lib'
import {
  NavDocuments,
  NavMain,
  NavUser,
  SearchBar,
  T_NavMain,
  TeamSwitcher,
} from '../ui'

import { data } from './data'

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  return (
    <Sidebar
      collapsible="icon"
      {...props}
      className={cn('p-0', props.className)}
    >
      <SidebarHeader className="flex h-[73px] justify-center border-b px-4 py-5">
        <TeamSwitcher teams={data.teams} />
      </SidebarHeader>
      <SidebarContent className="custom-scrollbar mt-2 px-2 py-2">
        <SearchBar />
        <NavMain name="Platform" items={data.navMain as T_NavMain['items']} />
        <NavMain
          name="Integration"
          items={data.integration as T_NavMain['items']}
        />
        <NavDocuments items={data.helper} />
      </SidebarContent>
      <SidebarFooter>
        <NavUser />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  )
}
