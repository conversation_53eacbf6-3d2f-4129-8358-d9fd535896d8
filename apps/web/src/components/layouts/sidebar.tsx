import { SidebarInset, SidebarProvider } from '@ttplatform/ui/components'
import { AppSidebar } from './app-sidebar'

type T_SidebarProps = {
  children: React.ReactNode
}

export default function Sidebar({ children }: T_SidebarProps) {
  return (
    <SidebarProvider className="bg-white">
      <AppSidebar variant="inset" />
      <SidebarInset className="border">
        <div className="flex flex-col h-full">
          {/* @ts-ignore */}
          {children}
        </div>
      </SidebarInset>
    </SidebarProvider>
  )
}
