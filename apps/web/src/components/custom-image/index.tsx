import { cn } from '@/utils'
import { AspectRatio } from '@ttplatform/ui/components'
import Image, { ImageProps } from 'next/image'

type TProps = ImageProps & {
  ratio?: number
  className?: string
  imgClassName?: string
}

const IMG_PLACEHOLDER = '/images/placeholder.jpeg'

const CustomImage = ({
  loading = 'lazy',
  src,
  alt,
  ratio = 16 / 9,
  className,
  imgClassName,
  ...props
}: TProps) => {
  const imgSrc = src || IMG_PLACEHOLDER

  return (
    <AspectRatio ratio={ratio} className={cn('bg-muted', className)}>
      <Image
        src={imgSrc}
        alt={alt}
        loading={loading}
        {...props}
        className={cn('object-cover rounded-sm', imgClassName)}
      />
    </AspectRatio>
  )
}

export default CustomImage
