import { <PERSON>ING<PERSON>_CONFIG } from '@/config-global'
import { NextRequest, NextResponse } from 'next/server'
const { locales, defaultLocale } = LINGUI_CONFIG

import { auth, signOut } from './utils/auth'

const LANDING_PATHS = ['', 'tin-tuc', 'khuyen-mai']

const AUTH_ROUTES = ['sign-in', 'sign-up', 'forgot-password', 'reset-password']

const PROTECTED_ROUTES = ['platform']

// Country-to-locale mapping
const countryToLocale: Record<string, string> = {
  VN: 'vi',
  US: 'en',
  JP: 'ja',
  // Add more mappings if needed
}

export default async function middleware(request: NextRequest) {
  const { nextUrl, headers } = request
  const pathname = nextUrl.pathname
  const segment = pathname.split('/')[1]

  if (LANDING_PATHS.includes(segment)) {
    return NextResponse.next()
  }
  const pathArray = pathname.split('/')

  // const cookieStore = await cookies();
  // const token = cookieStore.get(KEYS.keyAccessToken)?.value;

  const session = await auth()
  const token = session?.access_token
  const isRefreshTokenError = session?.error === 'RefreshTokenError'

  // Get locale from URL
  const localeLanguage = pathArray[1] || ''
  const isLocaleInUrl = locales.includes(localeLanguage)

  // Get geolocation country
  const country = headers.get('x-vercel-ip-country') || 'US'
  const detectedLocale = countryToLocale[country] || defaultLocale

  // Check auth status
  const isAuthRoute = AUTH_ROUTES.some((route) => pathArray.includes(route))
  const isProtectedRoute = PROTECTED_ROUTES.some((route) =>
    pathArray.includes(route),
  )

  // Redirect unauthenticated users
  if (isProtectedRoute && !token) {
    console.log('Redirecting to sign-in')
    return NextResponse.redirect(new URL(`/${detectedLocale}/sign-in`, nextUrl))
  }

  if (isProtectedRoute && isRefreshTokenError) {
    console.log('Redirecting to sign-in')
    await signOut()
    // return NextResponse.redirect(
    //   new URL(`/${detectedLocale}/sign-in`, nextUrl),
    // );
  }

  if (isAuthRoute && token) {
    return NextResponse.redirect(
      new URL(`/${detectedLocale}/platform`, nextUrl),
    )
  }

  // If the locale is missing in the URL, redirect to the detected locale
  if (!isLocaleInUrl) {
    return NextResponse.redirect(
      new URL(`/${detectedLocale}${nextUrl.pathname}`, nextUrl),
    )
  }

  return NextResponse.next()
}

export const config = {
  matcher: [
    // all routes except:
    // api, _next/static, public, _next/image, favicon.ico, and all subdomains
    '/((?!api|_next/static|public|_next/image|favicon.ico|.*\\..*).*)',
    // protected routes: /platform
    '/platform',
    // auth routes: /sign-in, /sign-up, /forgot-password, /reset-password
    '/sign-in',
    '/sign-up',
    '/forgot-password',
    '/reset-password',
  ],
}

// import { rootDomain, sessionKey } from '@/config-global';
// import { paths } from '@/routes/paths';
// import { cookies } from 'next/headers';
// import { NextRequest, NextResponse } from 'next/server';

// // 1. Specify protected and public routes
// const protectedRoutes = ['/platform'];
// const publicRoutes = ['/auth'];

// export async function middleware(req: NextRequest) {
//   const url = req.nextUrl;

//   // 2. Check if the current route is protected or public
//   const pathname = req.nextUrl.pathname;
//   const isProtectedRoute = protectedRoutes.includes(pathname);
//   const isPublicRoute = publicRoutes.includes(pathname);

//   // Get hostname of request (e.g. demo.vercel.pub, demo.localhost:1000)
//   const hostname = req.headers
//     .get('host')!
//     .replace('.localhost:8000', `.${rootDomain}`);

//   const searchParams = req.nextUrl.searchParams.toString();
//   // Get the pathname of the request (e.g. /, /about, /blog/first-post)
//   const path = `${url.pathname}${
//     searchParams.length > 0 ? `?${searchParams}` : ''
//   }`;

//   // 3. Decrypt the session from the cookie
//   const session = (await cookies()).get(sessionKey)?.value;

//   // 4. Redirect to /login if the user is not authenticated
//   if (isProtectedRoute && !session) {
//     return NextResponse.redirect(new URL(paths.auth.login, req.nextUrl));
//   }

//   // const { account } = await createSessionClient()

//   const account = {
//     id: '1',
//     email: '<EMAIL>',
//     name: 'Test User',
//   };

//   // 5. Redirect to / if the user is authenticated and the route is auth
//   if (isPublicRoute && account && pathname.startsWith('/auth')) {
//     return NextResponse.redirect(new URL('/', req.nextUrl));
//   }

//   if (hostname === rootDomain) {
//     return NextResponse.rewrite(
//       new URL(`/platform${path === '/' ? '' : path}`, req.url),
//     );
//   }

//   const isRootDomain = hostname === rootDomain;
//   // Skip rewrite for s3 subdomain
//   if (hostname.startsWith('s3.')) {
//     return NextResponse.next();
//   }
//   const rewritePath = isRootDomain
//     ? `/platform${path === '/' ? '' : path}`
//     : `/${hostname}${path}`;

//   return NextResponse.rewrite(new URL(rewritePath, req.url));
// }

// export const config = {
//   matcher: [
//     '/((?!api/|_next/|_static/|_vercel|images|assets|auth|[\\w-]+\\.\\w+).*)',
//   ],
// };
