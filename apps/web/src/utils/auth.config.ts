const secret = process.env.AUTH_SECRET
const debug = process.env.NODE_ENV === 'development'

const pages = {
  signIn: '/sign-in',
  signOut: '/sign-in',
  error: '/sign-in',
}

export const authConfig = {
  secret,
  debug,
  pages,
  session: {
    strategy: 'jwt',
    // maxAge: 24 * 60 * 60, // 1 Day
  },
  providers: [],
}

export type T_TokenReturn = {
  access_token: string
  refresh_token: string
  expires_at: number
  error?: string
}
