import { jwtDecode } from 'jwt-decode'
import NextAuth from 'next-auth'
import CredentialsProvider from 'next-auth/providers/credentials'
import { getRefreshToken, login } from '~/src/libs/data/auth'

import { authConfig } from './auth.config'

/**
 * JWT callback
 * @param data
 * @returns
 */
const jwtCallback = async ({
  token,
  account,
  user,
}: { token: any; account: any; user: any }) => {
  if (user?.access_token) {
    const decoded = jwtDecode(user.access_token)
    token.expires_at = decoded.exp
  }

  if (account && user) {
    // First-time login, save the `access_token`, its expiry and the `refresh_token`
    return {
      ...token,
      ...account,
      ...user,
      access_token: user.access_token,
      expires_at: token.expires_at,
      refresh_token: user.refresh_token,
    }
  } else if (Date.now() < token.expires_at * 1000) {
    // Subsequent logins, but the `access_token` is still valid
    return token
  } else {
    // Subsequent logins, but the `access_token` has expired, try to refresh it
    if (!token.refresh_token) throw new TypeError('Missing refresh_token')

    try {
      // The `token_endpoint` can be found in the provider's documentation. Or if they support OIDC,

      const tokensOrError = await getRefreshToken(token.refresh_token)

      if (!tokensOrError) throw tokensOrError

      const newTokens = tokensOrError as {
        access_token: string
        expires_at: number
        refresh_token?: string
      }

      return {
        ...token,
        access_token: newTokens.access_token,
        expires_at: Math.floor(Date.now() / 1000 + newTokens.expires_at),
        // Some providers only issue refresh tokens once, so preserve if we did not get a new one
        refresh_token: newTokens.refresh_token
          ? newTokens.refresh_token
          : token.refresh_token,
      }
    } catch (error) {
      console.error('Error refreshing access_token', error)
      // If we fail to refresh the token, return an error so we can handle it on the page
      token.error = 'RefreshTokenError'
      return token
    }
  }
}

const sessionCallback = async (data: any) => {
  const { session, token } = data || {}

  const user = session?.user

  if (token.sub && session.user) {
    session.user.id = token.sub
  }

  if (user) {
    session.user = user
    session.access_token = token.access_token
    session.refresh_token = token.refresh_token
    session.expires_at = new Date(token.expires_at * 1000)
  }

  session.error = token.error

  return session
}

const authOptions = {
  ...authConfig,
  // Configure one or more authentication providers
  providers: [
    CredentialsProvider({
      name: 'Credentials',
      credentials: {
        email: {
          label: 'email:',
          type: 'text',
        },
        password: {
          label: 'password:',
          type: 'password',
        },
      },
      async authorize(credentials) {
        try {
          const { email, password } = credentials ?? {}
          if (!email || !password) {
            throw new Error('Invalid credentials: Email or password is invalid')
          }

          const user = (await login({
            email: email as string,
            password: password as string,
          })) as any

          if (!user) {
            throw new Error('Invalid credentials: User not found')
          }

          return user
        } catch (error) {
          console.log(error)
          return null
        }
      },
    }),
  ],
  callbacks: {
    jwt: jwtCallback,
    session: sessionCallback,
  },
}

// @ts-ignore
export const { auth, handlers, signIn, signOut } = NextAuth(authOptions)
