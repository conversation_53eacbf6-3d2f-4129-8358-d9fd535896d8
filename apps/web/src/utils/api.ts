// libs/utils/api.ts

import { APP_CONFIG } from '~/src/config-global'

const baseApiUrl = APP_CONFIG.apiUrl

type FetchOptions = {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH' | 'OPTIONS'
  body?: unknown
  params?: Record<string, string>
  token?: string
  headers?: Record<string, string>
  withAuth?: boolean
  options?: {
    revalidate?: boolean
    tags?: string[]
  }
}

export async function fetcher<T>(
  url: string,
  {
    method = 'GET',
    body,
    params = {},
    headers = {},
    options = {},
  }: FetchOptions = {},
): Promise<{ data: T }> {
  try {
    const apiUrl = `${baseApiUrl}${url}`

    const fetchOptions: RequestInit = {
      method,
      headers: {
        'Content-Type': 'application/json',
        ...headers,
      },
      ...(body ? { body: JSON.stringify(body) } : {}),
      next: {
        revalidate: options?.revalidate ? 0 : false,
        tags: options?.tags ?? [],
      },
    }

    // Append query parameters to the URL if they exist
    const queryString = new URLSearchParams(params).toString()
    const finalUrl = queryString ? `${apiUrl}?${queryString}` : apiUrl

    const response = await fetch(finalUrl, fetchOptions)

    if (!response.ok) {
      const errorText = await response.text()
      throw new Error(
        `Fetch error: ${response.status} ${response.statusText} - ${errorText}`,
        { cause: response },
      )
    }

    return response.json() as Promise<{ data: T }>
  } catch (error) {
    console.error('🚀 ~ fetcher ~ error:', error)
    throw error
  }
}
