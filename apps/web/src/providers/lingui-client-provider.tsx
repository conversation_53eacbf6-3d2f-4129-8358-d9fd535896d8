'use client'

import { Messages, i18n } from '@lingui/core'
import { I18nProvider } from '@lingui/react'
import { useEffect } from 'react'

type Props = {
  children: React.ReactNode
  initialLocale: string
  initialMessages: Messages
}

const LinguiClientProvider = ({
  children,
  initialLocale,
  initialMessages,
}: Props) => {
  if (!i18n.locale || i18n.locale !== initialLocale) {
    i18n.load(initialLocale, initialMessages)
    i18n.activate(initialLocale)
  }

  useEffect(() => {
    if (i18n.locale !== initialLocale) {
      i18n.load(initialLocale, initialMessages)
      i18n.activate(initialLocale)
    }
  }, [initialLocale, initialMessages])

  return (
    <I18nProvider i18n={i18n}>
      {/* @ts-ignore */}
      {children}
    </I18nProvider>
  )
}

export default LinguiClientProvider
