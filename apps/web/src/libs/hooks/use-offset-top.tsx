import { useEffect, useState } from 'react'

// ----------------------------------------------------------------------

export default function useOffSetTop(top: number) {
  const [offsetTop, setOffSetTop] = useState(false)
  const isTop = top || 100

  const isBrowser = typeof window !== 'undefined' && window.document

  useEffect(() => {
    if (!isBrowser) return

    window.onscroll = () => {
      if (window.screenY > isTop) {
        setOffSetTop(true)
      } else {
        setOffSetTop(false)
      }
    }
    return () => {
      window.onscroll = null
    }
  }, [isTop, isBrowser])

  return offsetTop
}

// Usage
// const offset = useOffSetTop(100);
