import { fetcher } from '@/utils/api'

import type {
  I_UiKitBlockGroupListReturn,
  I_UiKitBlockGroupReturn,
  I_UiKitBlockListReturn,
  I_UiKitBlockReturn,
} from '@ttplatform/common'

////////////////////////////////////////////////////////////
// UI KIT BLOCK GROUPs
////////////////////////////////////////////////////////////

/**
 * Get list of ui-kit block groups
 * @queryParams
 *  - `page`: number - The page number to get
 *  - `perPage`: number - The number of ui-kit block groups to get per page
 *  - `sort`: string - The sort order to get the ui-kit block groups
 *  - `sortOrder`: string - The sort order to get the ui-kit block groups
 *  - `search`: string - The search query to get the ui-kit block groups
 *  - `status`: string - The status of the ui-kit block groups to get
 *
 * @path GET /ui-kits/block-groups
 * @returns I_UiKitBlockGroupListReturn
 * **/
export async function getUiKitBlockGroupList(): Promise<I_UiKitBlockGroupListReturn> {
  try {
    const response = await fetcher('/ui-kits/block-groups')
    return response as I_UiKitBlockGroupListReturn
  } catch (error) {
    console.error(error)
    throw error
  }
}

/**
 * Get ui-kit block group by id
 * @path GET /ui-kits/block-groups/:id
 * @returns I_UiKitBlockGroupReturn
 * **/
export async function getUiKitBlockGroupById(
  id: string,
): Promise<I_UiKitBlockGroupReturn> {
  try {
    const response = await fetcher(`/ui-kits/block-groups/${id}`)
    return response.data as I_UiKitBlockGroupReturn
  } catch (error) {
    console.error(error)
    throw error
  }
}

////////////////////////////////////////////////////////////
// UI KIT BLOCKs
////////////////////////////////////////////////////////////

/**
 * Get list of ui-kit blocks
 * @queryParams
 *  - `page`: number - The page number to get
 *  - `perPage`: number - The number of ui-kit blocks to get per page
 *  - `sort`: string - The sort order to get the ui-kit blocks
 *  - `sortOrder`: string - The sort order to get the ui-kit blocks
 *  - `search`: string - The search query to get the ui-kit blocks
 *  - `status`: string - The status of the ui-kit blocks to get
 *
 * @path GET /ui-kits/blocks
 * @returns I_UiKitBlockListReturn
 * **/
export async function getUiKitBlockList(): Promise<I_UiKitBlockListReturn> {
  try {
    const response = await fetcher('/ui-kits/blocks')
    return response as I_UiKitBlockListReturn
  } catch (error) {
    console.error(error)
    throw error
  }
}

/**
 * Get ui-kit block by id
 * @path GET /ui-kits/blocks/:id
 * @returns I_UiKitBlockReturn
 * **/
export async function getUiKitBlockById(
  id: string,
): Promise<I_UiKitBlockReturn> {
  try {
    const response = await fetcher(`/ui-kits/blocks/${id}`)
    return response.data as I_UiKitBlockReturn
  } catch (error) {
    console.error(error)
    throw error
  }
}
