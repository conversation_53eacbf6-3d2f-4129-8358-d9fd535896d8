export const authPaths = {
  login: '/sign-in',
  register: '/sign-up',
  forgotPassword: '/forgot-password',
  resetPassword: '/reset-password',
} as const

const globalManagementPaths = {
  root: '/global-management',
  analytics: '/global-management/analytics',
  settings: '/global-management/settings',
  configDuplication: '/global-management/config-duplication',
  usages: '/global-management/usages',
} as const

const integrationPaths = {
  webhooks: '/integration/webhooks',
  importExport: '/integration/import-export',
} as const

const helperPaths = {
  docs: '/docs',
  support: '/support',
} as const

const settingsPaths = {
  root: '/settings',
  profile: '/settings/profile',
  security: '/settings/security',
  notifications: '/settings/notifications',
  subscriptions: '/settings/subscriptions',
  myTeam: '/settings/my-team',
  helpCenter: '/settings/help-center',
  integrations: '/settings/integrations',
  translations: '/settings/translations',
} as const

const hooksPaths = {
  settings: '/hooks/settings',
  api: '/hooks/api',
  webhook: '/hooks/webhook',
} as const

export const paths = {
  auth: authPaths,
  app: {
    root: '/platform',
    cms: {
      root: '/platform/cms',
      websites: {
        root: '/platform/cms/websites',
        view: (id: string) => `/platform/cms/websites/${id}`,
      },
      posts: {
        root: '/platform/cms/posts',
        new: '/platform/cms/posts/new',
        edit: (id: string) => `/platform/cms/posts/${id}/edit`,
      },
      design: {
        root: '/platform/design',
        edit: (id: string) => `/platform/design/${id}`,
      },
    },
    members: {
      root: '/platform/members',
      new: '/platform/members/new',
    },
    globalManagement: globalManagementPaths,
    settings: settingsPaths,
    hooks: hooksPaths,
    integration: integrationPaths,
    helper: helperPaths,
  },
}
