import DataCenter from '~/src/modules/site-builder/components/pages/data-center'
import ElectricPowerBuilder from '~/src/modules/site-builder/components/pages/electric-power'

export default async function SegmentsPage({
  params,
}: {
  params: Promise<{ segments: string[] }>
}) {
  const { segments } = await params
  const firstSegment = segments[0]
  const secondSegment = segments[1]
  return (
    <>
      {firstSegment.toLowerCase() === 'electric-power' && !secondSegment && (
        <ElectricPowerBuilder />
      )}
      {secondSegment && secondSegment.toLowerCase() === 'data-center' && (
        <DataCenter />
      )}
    </>
  )
}
