import '@/styles/web.css'

import {
  BCommitment,
  BContactBox,
  BFooterWithSchema,
  BHeaderWithSchema,
  LandingPageHeader,
} from '@ttplatform/core-page-builder/components'
import React from 'react'

export async function generateMetadata() {
  return {
    title: 'News & Promotions | PTC',
    description: 'Latest news and promotions in your region',
  }
}

import { _global } from '../../../../../__mock__/_global'

export default function NewsPromotionsLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <>
      {/* <BHeaderWithSchema /> */}
      <LandingPageHeader /> {/* For temporary use */}
      {children}
      {/* <BContactBox /> */}
      {/* <BCommitment /> */}
      <BFooterWithSchema data={_global.footer as any} />
    </>
  )
}
