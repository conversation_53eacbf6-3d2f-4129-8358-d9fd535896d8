'use client'

import { t } from '@lingui/core/macro'
import { useForm } from '@tanstack/react-form'
import { Button, Checkbox, Input, Typography } from '@ttplatform/ui/components'
import { motion } from 'framer-motion'
import { Eye, EyeOff } from 'lucide-react'
import { signIn, useSession } from 'next-auth/react'
import { useParams, useRouter } from 'next/navigation'
import { useState } from 'react'
import { toast } from 'sonner'
import { z } from 'zod'
import CustomLink from '~/src/components/custom-link'
import { AppLogo } from '~/src/components/logo'
import { paths } from '~/src/routes/paths'

// Define form schema using zod
const formSchema = z.object({
  email: z.string().email(),
  password: z.string().min(8),
})

export default function Form() {
  const { status } = useSession()
  const { locale } = useParams()

  const router = useRouter()
  const loading = status == 'loading'

  const [isChecked, setIsChecked] = useState<boolean>(false)
  const [showPassword, setShowPassword] = useState(false)

  const handleCheckedChange = (checked: boolean) => {
    setIsChecked(checked === true)
  }

  // Initialize form with default values and validation schema
  const { Field, handleSubmit, Subscribe } = useForm({
    defaultValues: {
      email: '',
      password: '',
    },
    onSubmit: async ({ value }) => {
      console.table(value)

      try {
        const result = await signIn('credentials', {
          email: value.email,
          password: value.password,
          redirect: false,
          redirectTo: `/${locale}/platform`,
        })

        if (result?.error) {
          toast.error(t`Invalid credentials: email or password is invalid`)
          return
        }

        toast.success(t`Login Successful`)
        router.push(`/${locale}/platform`)
      } catch (error) {
        console.error('Login error:', error)
        toast.error('Login Failed', {
          description:
            error instanceof Error ? error.message : t`Invalid credentials`,
        })
        throw error
      }
    },
    validators: {
      onChange: formSchema,
    },
  })

  return (
    <motion.div
      initial={{ opacity: 0, y: 50 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, ease: 'easeOut' }}
      className="relative mx-auto w-full max-w-xl p-4"
    >
      <motion.div
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        transition={{ duration: 0.5, ease: 'easeOut' }}
        className="relative z-20 flex w-full flex-col items-center justify-center rounded-md p-8"
        style={{
          background: 'rgba(255, 255, 255, 1)',
          borderRadius: '16px',
          boxShadow: '0 4px 30px rgba(0, 0, 0, 0.03)',
          backdropFilter: 'blur(20px)',
          WebkitBackdropFilter: 'blur(20px)',
        }}
      >
        <div className="flex w-full max-w-6xl flex-col items-center gap-6 rounded-lg lg:flex-row">
          <motion.div
            className="mx-auto flex w-full max-w-lg flex-col items-center justify-center p-8"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
          >
            <div className="flex gap-4 pb-6">
              <Typography variant="h3" className="my-3">
                {t`Welcome back to`}{' '}
              </Typography>

              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5 }}
              >
                <AppLogo />
              </motion.div>
            </div>

            <Typography variant="body2" className="text-center">
              {t`Login with your email and password you have been created before, or you can create an account if you don’t have a`}
              <br />
              <strong>PTC Platform</strong> {t`account.`}
            </Typography>
            <form
              onSubmit={(e) => {
                e.preventDefault()
                handleSubmit()
              }}
              className="mt-4 w-full"
            >
              <div className="mb-4">
                <Field name="email">
                  {({ state, handleChange, handleBlur }) => (
                    <div>
                      <Typography variant="body2" className="mb-2 font-bold">
                        {t`Email`}
                      </Typography>
                      <Input
                        defaultValue={state.value}
                        onChange={(e) => handleChange(e.target.value)}
                        onBlur={handleBlur}
                        placeholder={t`Enter your email`}
                      />
                      {state.meta.errors?.length > 0 && (
                        <Typography variant="caption" className="text-red-500">
                          {state.meta.errors[0]?.message ||
                            String(state.meta.errors[0])}
                        </Typography>
                      )}
                    </div>
                  )}
                </Field>
              </div>
              <div className="mb-4">
                <Field name="password">
                  {({ state, handleChange, handleBlur }) => (
                    <div>
                      <Typography variant="body2" className="mb-2 font-bold">
                        {t`Password`}
                      </Typography>
                      <div className="relative">
                        <Input
                          type={showPassword ? 'text' : 'password'}
                          defaultValue={state.value}
                          onChange={(e) => handleChange(e.target.value)}
                          onBlur={handleBlur}
                          placeholder={t`Enter your password`}
                        />
                        <button
                          type="button"
                          className="absolute top-2.5 right-3 cursor-pointer text-gray-500"
                          onClick={() => setShowPassword(!showPassword)}
                        >
                          {showPassword ? (
                            <EyeOff size={20} />
                          ) : (
                            <Eye size={20} />
                          )}
                        </button>
                      </div>
                      {state.meta.errors?.length > 0 && (
                        <Typography variant="caption" className="text-red-500">
                          {state.meta.errors[0]?.message ||
                            String(state.meta.errors[0])}
                        </Typography>
                      )}
                    </div>
                  )}
                </Field>
              </div>
              <div className="mb-4 flex items-center justify-between">
                <label className="inline-flex items-center text-sm">
                  <Checkbox
                    checked={isChecked}
                    onCheckedChange={handleCheckedChange}
                  />
                  <Typography
                    variant="body2"
                    className={`ml-2 ${isChecked ? 'text-black' : 'text-gray-500'}`}
                  >
                    {t`Keep me sign-in`}
                  </Typography>
                </label>
                <CustomLink
                  className="text-primary hover:text-primary/50 text-sm underline"
                  href="#"
                >
                  {t`Forgot password?`}
                </CustomLink>
              </div>
              <Subscribe
                selector={(state) => [state.canSubmit, state.isValidating]}
              >
                {([canSubmit, isValidating]) => (
                  <Button
                    type="submit"
                    disabled={!canSubmit || isValidating}
                    onClick={handleSubmit}
                    className={`mt-4 w-full transition-all duration-300 ${!canSubmit && 'cursor-not-allowed bg-gray-300'}`}
                  >
                    {loading ? (
                      <div className="h-4.5 w-4.5 animate-spin rounded-full border-2 border-gray-600 border-t-transparent"></div>
                    ) : (
                      t`Login now`
                    )}
                  </Button>
                )}
              </Subscribe>

              {/* <div className="my-4 flex items-center">
              <div className="flex-grow border-t border-gray-300"></div>
              <Typography variant="body2" className="mx-4 text-gray-800">
                Or continue with
              </Typography>
              <div className="flex-grow border-t border-gray-300"></div>
            </div>

            <div className="grid w-full grid-cols-2 justify-between">
              <Button
                className="focus:shadow-outline mr-2 rounded border border-gray-300 bg-white px-4 py-2 font-bold text-gray-700 hover:bg-gray-100 focus:outline-none"
                type="button"
              >
                <Icon icon="logos:google-icon" width="32" height="32" />
                Google
              </Button>
              <Button
                className="focus:shadow-outline ml-2 w-full rounded border border-gray-300 bg-white px-4 py-2 font-bold text-gray-700 hover:bg-gray-100 focus:outline-none"
                type="button"
              >
                <Icon icon="logos:facebook" width="32" height="32" />
                Facebook
              </Button>
            </div> */}
              <Typography
                variant="body2"
                className="mt-4 text-center text-gray-500"
              >
                {t`Don’t have an account?`}{' '}
                <CustomLink href={paths.auth.register} className="underline">
                  {t`Create account`}
                </CustomLink>
              </Typography>
            </form>
          </motion.div>
        </div>
      </motion.div>
    </motion.div>
  )
}
