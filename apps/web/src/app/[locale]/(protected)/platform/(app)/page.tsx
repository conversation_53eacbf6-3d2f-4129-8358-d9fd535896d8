import { ComingSoon } from '@ttplatform/ui/templates'
import { HomeIcon } from 'lucide-react'
import { Metadata } from 'next'
import { AppInset } from '~/src/components/layouts'

export const metadata: Metadata = {
  title: 'Dashboard - PTC',
}

export default async function PlatformPage() {
  return (
    <AppInset
      breadcrumb={{
        items: [
          {
            label: 'PTC',
            href: '/',
            icon: <HomeIcon />,
          },
        ],
      }}
    >
      <ComingSoon />
    </AppInset>
  )
}
