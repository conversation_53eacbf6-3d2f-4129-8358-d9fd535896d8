import { TemplateWebsiteList } from '@/modules/cms'
import { Globe, HomeIcon } from 'lucide-react'
import { Metadata } from 'next'
import { AppInset } from '~/src/components/layouts'
import { paths } from '~/src/routes/paths'

export const metadata: Metadata = {
  title: 'Websites',
}

type TProps = {
  searchParams: Promise<{ page: string; take: string; query: string }>
}

export default async function PlatformPage({ searchParams }: TProps) {
  return (
    <AppInset
      breadcrumb={{
        items: [
          {
            label: 'PTC',
            href: paths.app.root,
            icon: <HomeIcon />,
          },
          {
            label: 'Websites',
            href: paths.app.cms.websites.root,
            icon: <Globe />,
            isActive: true,
          },
        ],
      }}
    >
      <TemplateWebsiteList searchParams={searchParams} />
    </AppInset>
  )
}
