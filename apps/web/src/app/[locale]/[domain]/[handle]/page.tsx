import { Metadata } from 'next'
import { Suspense } from 'react'

export const metadata: Metadata = {
  title: 'Custom Subdomain',
}

type Props = {
  params: Promise<{ locale: string; domain: string; handle: string }>
}

export default async function PageBioDetails({ params }: Props) {
  const { locale, domain, handle } = await params

  const domainDecoded = decodeURIComponent(domain)
  const handleDecoded = decodeURIComponent(handle)

  return (
    <Suspense fallback={<div>Loading...</div>}>
      <div className="flex flex-col gap-4 justify-center items-center h-svh bg-blue-50 p-4">
        <h1 className="font-bold text-2xl">Hello</h1>
        <p className="text-center">
          This is the page for subdomain: {locale}
          <br />
          <strong className="italic">{domainDecoded}</strong>
          <br />
          and handle:
          <br />
          <strong className="italic">{handleDecoded}</strong>
        </p>
      </div>
    </Suspense>
  )
}
