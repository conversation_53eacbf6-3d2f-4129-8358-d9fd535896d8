import { APP_CONFIG } from '~/src/config-global'
import { LandingPageNews } from '~/src/modules/site-builder/components'

export async function generateMetadata({
  params,
}: {
  params: Promise<{ slug: string }>
}) {
  const { slug } = await params
  const post = await fetch(
    `${APP_CONFIG.baseUrl}/api/posts/landing-news?page=1&limit=100`,
    {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    },
  )
  const data = await post.json()
  const postDetail = data.posts.find((item: any) => item.slug === slug)
  return {
    title: postDetail.title.text.toUpperCase(),
    description: postDetail.title.text,
  }
}

export default async function NewsDetailPage({
  params,
}: {
  params: Promise<{ slug: string }>
}) {
  const { slug } = await params
  return <LandingPageNews slug={slug} />
}
