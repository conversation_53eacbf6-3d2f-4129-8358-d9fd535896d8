import { nextJsConfig } from '@ttplatform/config-eslint/next-js'

import { dirname } from 'node:path'
import { fileURLToPath } from 'node:url'
import { FlatCompat } from '@eslint/eslintrc'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

const compat = new FlatCompat({
  baseDirectory: __dirname,
})

const eslintConfig = [
  ...compat.extends('next/core-web-vitals', 'next/typescript'),
  ...nextJsConfig,
]

eslintConfig.push({
  rules: {
    'react/react-in-jsx-scope': 'off',
    'react/prop-types': 'off', // Added rule to address potential type issues
    '@typescript-eslint/consistent-type-definitions': ['off', 'interface'], // Added rule to enforce consistent type definitions
    '@typescript-eslint/ban-ts-comment': 'off',
  },
})

export default eslintConfig
