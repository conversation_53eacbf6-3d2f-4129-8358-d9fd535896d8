{"$schema": "https://json.schemastore.org/tsconfig", "extends": "@ttplatform/config-typescript/nextjs.json", "compilerOptions": {"jsx": "preserve", "module": "esnext", "target": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "allowJs": true, "noEmit": true, "strict": true, "baseUrl": ".", "paths": {"~/*": ["./*"], "@/*": ["./src/*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "next.config.ts", "vitest.config.ts"], "exclude": ["node_modules"]}