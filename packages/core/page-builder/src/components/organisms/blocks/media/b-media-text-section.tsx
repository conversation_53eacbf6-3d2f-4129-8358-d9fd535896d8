'use client'

import { cn } from '@ttplatform/ui/lib'
import {
  EImagePosition,
  EMediaTextLayout,
  LAYOUT_GRID_CLASSES,
  T_MediaTextSectionSchema,
  getOptimizedImageUrl,
} from '../../../../libs'
import { CmsMedia } from '../../../atoms'
import {
  renderButtons,
  renderDescription,
  renderHeading,
  renderSubHeading,
} from '../../../molecules'

// Utility functions to check valid content
const hasValidHeading = (heading: any) => {
  return (
    heading && typeof heading.text === 'string' && heading.text.trim() !== ''
  )
}

const hasValidSubHeading = (sub_heading: any) => {
  return (
    sub_heading &&
    typeof sub_heading.text === 'string' &&
    sub_heading.text.trim() !== ''
  )
}

const hasValidDescription = (description: any) => {
  if (!description || !Array.isArray(description)) return false
  return description.some((paragraph) =>
    paragraph.children?.some((child: any) => child.text?.trim()),
  )
}

const hasValidButtons = (buttons: any) => {
  return (
    buttons &&
    Array.isArray(buttons) &&
    buttons.length > 0 &&
    buttons.some((button) => button.text?.trim())
  )
}

// Common helper functions
const createImageBlock = (
  image: any,
  className = 'object-cover aspect-[3/2] h-full',
) => {
  if (!image?.image) return null

  return (
    <div className="relative w-full h-full flex items-start justify-center">
      <div className="relative w-full h-full">
        <CmsMedia
          media={image.image}
          className={className}
          style={{
            borderRadius: image.border_radius
              ? `${image.border_radius}px`
              : undefined,
          }}
          alt={image.alt || image.image.alternativeText || image.image.name}
          format="medium"
          width={600}
          height={400}
          contentClassName="h-full w-full"
          hoverAnimation={false}
        />
      </div>
    </div>
  )
}

const createContentElements = (
  heading: any,
  sub_heading: any,
  description: any,
  buttons: any,
  container_styles: any,
  locale: string | undefined,
) => {
  const underlineColor =
    container_styles?.color?.toUpperCase() === '#FFCC00'
      ? 'bg-gray-800'
      : 'bg-primary'

  return (
    <>
      {hasValidHeading(heading) &&
        renderHeading(heading, {
          hasUnderline: true,
          underlineColor,
        })}
      {hasValidSubHeading(sub_heading) && renderSubHeading(sub_heading)}
      {hasValidDescription(description) &&
        renderDescription(description, { locale })}
      {hasValidButtons(buttons) && renderButtons(buttons || [])}
    </>
  )
}

const createContainerBgStyles = (
  container_styles: any,
  styles: any,
  image: any,
) => {
  return {
    backgroundColor: container_styles?.color,
    backgroundImage: container_styles?.image
      ? `url(${getOptimizedImageUrl(container_styles.image, 'large')})`
      : undefined,
    backgroundRepeat: container_styles?.repeat || 'no-repeat',
    backgroundSize: container_styles?.size || 'cover',
    opacity: container_styles?.opacity || 1,
    borderRadius:
      styles?.content_max_width === 'full_width'
        ? image?.border_radius
          ? `${image?.border_radius + 4}px`
          : '0px'
        : image?.border_radius
          ? `${image?.border_radius + 4}px`
          : '24px',
  }
}

const createMobileLayout = (
  heading: any,
  sub_heading: any,
  description: any,
  buttons: any,
  container_styles: any,
  styles: any,
  image: any,
  locale: string | undefined,
) => {
  const containerBgStyles = createContainerBgStyles(
    container_styles,
    styles,
    image,
  )

  const mobileImageBlock = image?.image && (
    <div className="p-3 relative w-full">
      <CmsMedia
        media={image.image}
        className="w-full object-cover aspect-[3/2]"
        style={{
          borderRadius: image.border_radius
            ? `${image.border_radius}px`
            : '24px',
        }}
        alt={image.alt || image.image.alternativeText || image.image.name}
        format="medium"
        width={600}
        height={400}
        hoverAnimation={false}
      />
    </div>
  )

  const mobileContentBlock = (
    <div className="w-full flex flex-col items-center justify-center p-4 sm:p-6">
      <div className="flex flex-col w-full max-w-lg gap-4 text-center">
        {createContentElements(heading, sub_heading, description, buttons, container_styles, locale)}
      </div>
    </div>
  )

  return (
    <div className="md:hidden">
      <div
        className="w-full flex flex-col overflow-hidden"
        style={containerBgStyles}
      >
        {mobileImageBlock}
        {mobileContentBlock}
      </div>
    </div>
  )
}

// Desktop layout functions
const createRegularDesktopLayout = (
  imageBlock: any,
  contentBlock: any,
  image_position: any,
  gridClass: string,
  styles: any,
  containerBgStyles: any,
) => {
  return (
    <div
      className={cn(
        'hidden md:grid w-full grid-cols-1',
        'overflow-hidden items-stretch h-full',
        gridClass,
      )}
      style={containerBgStyles}
    >
      {image_position === EImagePosition.LEFT ? (
        <>
          {imageBlock}
          {contentBlock}
        </>
      ) : (
        <>
          {contentBlock}
          {imageBlock}
        </>
      )}
    </div>
  )
}

const createFullWidthDesktopLayout = (
  imageBlock: any,
  contentBlock: any,
  image_position: any,
  containerBgStyles: any,
) => {
  return (
    <div
      className={cn(
        'relative',
        'overflow-hidden items-stretch h-full',
        '!gap-0 hidden md:block',
      )}
      style={containerBgStyles}
    >
      {image_position === EImagePosition.LEFT ? (
        <>
          <div className="absolute left-0 top-0 bottom-0 h-full w-1/2">
            {imageBlock}
          </div>
          <div className="h-full w-full mx-auto min-[1440px]:max-w-7xl 3xl:max-w-[1600px] flex items-end justify-end">
            <div className="w-1/2 min-h-[400px] xl:min-h-[600px] 3xl:min-h-[900px] flex items-center top-0 right-0 md:pl-8 lg:pl-10 xl:pl-12">
              {contentBlock}
            </div>
          </div>
        </>
      ) : (
        <>
          <div className="h-full w-full mx-auto min-[1440px]:max-w-7xl 3xl:max-w-[1600px]">
            <div className="w-1/2 min-h-[400px] xl:min-h-[600px] 3xl:min-h-[900px] flex items-center md:pr-8 lg:pr-10 xl:pr-12">
              {contentBlock}
            </div>
          </div>
          <div className="absolute right-0 top-0 bottom-0 h-full w-1/2">
            <div className="w-full h-full">{imageBlock}</div>
          </div>
        </>
      )}
    </div>
  )
}

const createSplitContentBlock = (
  heading: any,
  sub_heading: any,
  description: any,
  buttons: any,
  container_styles: any,
  styles: any,
  locale: string | undefined,
) => {
  const hasContent =
    hasValidHeading(heading) ||
    hasValidSubHeading(sub_heading) ||
    hasValidDescription(description) ||
    hasValidButtons(buttons)

  if (!hasContent) return null

  return (
    <div
      className={cn(
        'h-full flex flex-col items-center justify-center p-4 sm:py-10',
      )}
    >
      <div
        className={cn(
          'flex flex-col w-full gap-4 md:gap-6 xl:gap-10',
          styles?.content_max_width === 'full_width' ? '' : 'max-w-lg',
        )}
      >
        {createContentElements(heading, sub_heading, description, buttons, container_styles, locale)}
      </div>
    </div>
  )
}

const createLargeMediaContentBlock = (
  heading: any,
  sub_heading: any,
  description: any,
  buttons: any,
  container_styles: any,
  locale: string | undefined,
) => {
  const hasContent =
    hasValidHeading(heading) ||
    hasValidSubHeading(sub_heading) ||
    hasValidDescription(description) ||
    hasValidButtons(buttons)

  if (!hasContent) return null

  return (
    <div className="h-full flex flex-col items-start justify-between">
      <div className="flex flex-col w-full max-w-lg gap-4 md:gap-6 3xl:gap-10 rounded-lg">
        {createContentElements(heading, sub_heading, description, buttons, container_styles, locale)}
      </div>
    </div>
  )
}

// Main layout functions
function renderSplitLayout(props: T_MediaTextSectionSchema) {
  const {
    image_position,
    buttons,
    heading,
    styles,
    sub_heading,
    container_styles,
    description,
    image,
    locale,
  } = props

  const gridClass = `${LAYOUT_GRID_CLASSES[EMediaTextLayout.SPLIT]} gap-y-4 sm:gap-y-6 md:gap-y-8`
  const isFullWidth = styles?.content_max_width === 'full_width'

  const imageBlock = createImageBlock(
    image,
    isFullWidth ? 'object-cover h-full w-full' : '',
  )

  const contentBlock = createSplitContentBlock(
    heading,
    sub_heading,
    description,
    buttons,
    container_styles,
    styles,
    locale,
  )

  const containerBgStyles = createContainerBgStyles(container_styles, styles, image)
  const mobileLayout = createMobileLayout(
    heading,
    sub_heading,
    description,
    buttons,
    container_styles,
    styles,
    image,
    locale,
  )

  const desktopLayout = isFullWidth
    ? createFullWidthDesktopLayout(imageBlock, contentBlock, image_position, containerBgStyles)
    : createRegularDesktopLayout(imageBlock, contentBlock, image_position, gridClass, styles, containerBgStyles)

  return (
    <div className="split-layout">
      {mobileLayout}
      {desktopLayout}
    </div>
  )
}

function renderLargeMediaLayout(props: T_MediaTextSectionSchema) {
  const {
    image_position,
    buttons,
    heading,
    styles,
    sub_heading,
    container_styles,
    description,
    image,
    locale,
  } = props

  const largeMediaClasses = LAYOUT_GRID_CLASSES[EMediaTextLayout.LARGE_MEDIA]
  const baseGridClasses =
    typeof largeMediaClasses === 'object'
      ? largeMediaClasses[image_position as keyof typeof largeMediaClasses]
      : largeMediaClasses
  const gridClass = `${baseGridClasses} gap-4 sm:gap-6 lg:gap-8 2xl:gap-12 3xl:gap-24`

  const imageBlock = createImageBlock(image, 'object-cover aspect-[3/2] h-full')
  const contentBlock = createLargeMediaContentBlock(
    heading,
    sub_heading,
    description,
    buttons,
    container_styles,
    locale,
  )

  const containerBgStyles = createContainerBgStyles(container_styles, styles, image)
  const mobileLayout = createMobileLayout(
    heading,
    sub_heading,
    description,
    buttons,
    container_styles,
    styles,
    image,
    locale,
  )

  return (
    <div className="large-media-layout">
      {mobileLayout}
      <div
        className={cn(
          'hidden md:grid w-full grid-cols-1',
          gridClass,
          'p-6 md:p-8 xl:py-10 3xl:p-16',
          'overflow-hidden min-h-[400px] items-start',
        )}
        style={containerBgStyles}
      >
        {image_position === EImagePosition.LEFT ? (
          <>
            {imageBlock}
            {contentBlock}
          </>
        ) : (
          <>
            {contentBlock}
            {imageBlock}
          </>
        )}
      </div>
    </div>
  )
}

export function BMediaTextSection({
  layout = EMediaTextLayout.SPLIT,
  ...props
}: T_MediaTextSectionSchema) {
  return (
    <div className="b-media-text-section">
      {layout === EMediaTextLayout.SPLIT
        ? renderSplitLayout({ layout, ...props })
        : renderLargeMediaLayout({ layout, ...props })}
    </div>
  )
}
