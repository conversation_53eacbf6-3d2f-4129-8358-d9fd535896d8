'use client'

import { Typography } from '@ttplatform/ui/components'
import { cn } from '@ttplatform/ui/lib'
import Image from 'next/image'
import Link from 'next/link'
import React, { memo, useEffect, useState } from 'react'
import {
  TFooterSchema,
  TLinkSchema,
  TMediaSchema,
  TMediaSchemaRelated,
} from '../../../../libs/types'
import { CmsLogo, CmsMedia } from '../../../atoms'
import { LayoutContainer } from '../../../templates'

type TBrandSocialColumnProps = {
  locale?: string
  logo: TMediaSchemaRelated
  description: string
  attached_images: TMediaSchema[]
  social_media_links: TLinkSchema[]
}

const BrandSocialColumn = ({
  logo,
  description,
  attached_images,
  social_media_links,
}: TBrandSocialColumnProps) => {
  const renderDescription = (
    <Typography variant="body1" className="text-white text-sm sm:text-base">
      {description}
    </Typography>
  )

  const renderAttachedImages = (
    <div className="flex flex-wrap gap-4 sm:gap-6">
      {attached_images?.map((item, idx) => (
        <CmsMedia key={idx} media={item} className="w-fit h-10 sm:h-15" />
      ))}
    </div>
  )

  const renderSocialLinks = (
    <div className="flex gap-2 sm:gap-4">
      {social_media_links?.map((item: TLinkSchema, idx: number) => (
        <Link key={idx} href={item.url || '#'} aria-label={item.text}>
          {item.icon && (
            <CmsMedia
              media={item.icon}
              className="w-6 h-6 sm:w-8 sm:h-8 hover:text-primary transition-colors shrink-0"
            />
          )}
        </Link>
      ))}
    </div>
  )

  return (
    <div className="space-y-10 max-w-md">
      <CmsLogo logo={logo} className="h-10 sm:h-12 flex justify-start" />
      {description && renderDescription}

      {attached_images?.length > 0 ? renderAttachedImages : null}
      {social_media_links?.length > 0 ? renderSocialLinks : null}
    </div>
  )
}

const FooterLinkColumns = ({
  footer_nav,
}: {
  footer_nav: TFooterSchema['footer_nav']
}) => {
  const renderLinks = (links: TLinkSchema[]) => {
    return (
      <ul className="space-y-4 text-sm sm:text-base">
        {links?.map((item, idx) => (
          <li key={idx}>
            <Link
              href={item.url || '#'}
              className="transition-colors hover:text-primary"
            >
              <Typography variant="body1">{item.text}</Typography>
            </Link>
          </li>
        ))}
      </ul>
    )
  }

  const renderColumn = (column: TFooterSchema['footer_nav'][number]) => {
    return (
      <div className="space-y-5 custom-xl:flex-1 custom-xl:min-w-[200px]">
        <Typography
          variant="body1"
          className="text-yellow-400 font-bold tracking-wider text-base sm:text-lg whitespace-nowrap"
        >
          {column.title}
        </Typography>
        {renderLinks(column.links)}
      </div>
    )
  }

  return footer_nav?.map((column, idx) => (
    <React.Fragment key={idx}>{renderColumn(column)}</React.Fragment>
  ))
}

const FooterContactColumns = ({
  footer_contact,
}: {
  locale?: string
  footer_contact: TFooterSchema['footer_contact']
}) => {
  const { title, items } = footer_contact || {}

  return (
    <div className="space-y-5 custom-xl:flex-1 max-w-md">
      <Typography
        variant="body1"
        className="text-yellow-400 font-bold tracking-wider text-base sm:text-lg"
      >
        {title}
      </Typography>
      <ul className="space-y-4 text-sm sm:text-base">
        {items?.map((item, idx) => (
          <li key={idx}>
            <Link
              href={item.url || '#'}
              className="flex items-center gap-2 transition-colors hover:text-primary"
            >
              {item.icon && (
                <CmsMedia
                  media={item?.icon}
                  className="w-6 h-6 sm:w-8 sm:h-8 hover:text-primary transition-colors shrink-0"
                />
              )}
              <Typography variant="body1">
                <span className="font-bold">{item.title}:</span>{' '}
                {item.description}
              </Typography>
            </Link>
          </li>
        ))}
      </ul>
    </div>
  )
}

export const BFooter = ({ data }: TProps) => {
  const {
    logo,
    social_media_links,
    attached_images,
    description,
    footer_nav,
    footer_contact,
  } = data || {}

  const [screenWidth, setScreenWidth] = useState(
    typeof window !== 'undefined' ? window.innerWidth : 1280,
  )

  useEffect(() => {
    if (typeof window === 'undefined') return

    const handleResize = () => {
      setScreenWidth(window.innerWidth)
    }

    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  const isBelow1240 = screenWidth < 1240

  const renderColumns = (
    <>
      <FooterLinkColumns footer_nav={footer_nav} />
      <FooterContactColumns footer_contact={footer_contact} />
    </>
  )

  return (
    <div className="relative h-full">
      <Image
        src="/images/footer-bg.svg"
        alt="footer-background"
        className="w-full h-full absolute inset-0"
        width={600}
        height={400}
      />
      <LayoutContainer
        className={cn(
          'relative py-6 md:py-12 lg:py-16 text-white bg-cover bg-center',
          'max-w-full',
        )}
      >
        <div className="absolute inset-x-0 -top-px h-1.5 bg-gradient-to-r from-[#FF9900] via-[#FFD326] to-[#FFDE59]" />
        <div
          className={cn(
            'flex relative z-1',
            isBelow1240
              ? 'flex-col gap-6 sm:gap-10'
              : 'flex-row gap-8 justify-between',
          )}
        >
          <BrandSocialColumn
            logo={logo}
            social_media_links={social_media_links}
            attached_images={attached_images}
            description={description}
          />
          {isBelow1240 ? (
            <div className="flex flex-col md:flex-row gap-6 md:gap-20">
              {renderColumns}
            </div>
          ) : (
            renderColumns
          )}
        </div>
      </LayoutContainer>
    </div>
  )
}

type TProps = {
  data: TFooterSchema
  locale?: string
}

const BFooterWithSchema = ({ data, locale }: TProps) => {
  return <BFooter data={data} locale={locale} />
}
export default memo(BFooterWithSchema)
