import { Typography } from '@ttplatform/ui/components'
import { strapiImage } from '../../../../libs/utils/strapi-image'
import { MainButton } from '../../../atoms'
//---------------------------------------------------------------------------------

type HeadingProps = {
  heading: any
  breadcrumb: any
  fallbackImage?: string
}

//---------------------------------------------------------------------------------

export function BProductHeading({
  heading,
  breadcrumb,
  fallbackImage = '/images/products/product-hero.png',
}: HeadingProps) {
  const { title, description, buttons, image } = heading || {}

  // Convert string path to media object and handle URL
  const mediaImage =
    typeof image === 'string'
      ? {
        url: strapiImage(image),
        alternativeText: title,
      }
      : image

  return (
    <section>
      <div className="flex flex-col gap-10 py-10 md:py-16 px-4">
        {breadcrumb}
        <div className="w-full container max-w-screen-lg mx-auto">
          <div className="flex flex-col gap-10 items-center">
            <div className="flex flex-col gap-4 items-center text-center">
              <Typography
                variant={'h1'}
                className="relative pb-2 max-w-screen-md line-height-2"
              >
                {title}
                <span className="block mx-auto w-[68px] h-[8px] rounded-none mt-4 bg-gradient-to-r from-[#FFCC00] via-[#FFD326] to-[#FFDE59]" />
              </Typography>
              {description ? (
                <Typography variant={'body1'} className="max-w-screen-lg">
                  {description}
                </Typography>
              ) : null}
              {buttons.length ? (
                <div className="flex gap-x-6">
                  {buttons?.map((button: any, idx: number) => (
                    <MainButton
                      key={idx}
                      variant="secondary"
                      isDisabledIcon={true}
                      label={button?.text}
                      url={button?.url}
                      openInNewTab={button?.open_in_new_tab}
                      icon={button?.icon}
                    />
                  ))}
                </div>
              ) : null}
            </div>
          </div>
        </div>
        {image ? (
          <div className="w-full container max-w-screen-2xl mx-auto">
            <div className="relative w-full aspect-[16/9] rounded-2xl overflow-hidden">
              {/* <CmsMedia
                media={mediaImage}
                className="object-cover"
                fill
                priority
                hoverAnimation={false}
                fallbackImageUrl={fallbackImage}
              /> */}
            </div>
          </div>
        ) : null}
      </div>
    </section>
  )
}
