import { Typography } from '@ttplatform/ui/components'
import Image from 'next/image'
import { MainButton } from '../../../atoms'

//---------------------------------------------------------------------------------
const _CATEGORIES = [
  {
    title: 'MÁY CÔNG TRÌNH & KHAI MỎ',
    description:
      'Caterpillar có hơn 300 loại sản phẩm thể hiện sự tập trung của chúng tôi nhằm mang đến sự thành công cho khách hàng và đặt ra nhưng tiêu chuẩn cho cả ngành công nghiệp.',
    image: '/images/products/may-cong-trinh.png',
  },
  {
    title: 'XE NÂNG',
    description:
      'Caterpillar có hơn 300 loại sản phẩm thể hiện sự tập trung của chúng tôi nhằm mang đến sự thành công cho khách hàng và đặt ra nhưng tiêu chuẩn cho cả ngành công nghiệp.',
    image: '/images/products/xe-nang.png',
  },
  {
    title: 'MÁY CÔNG TRÌNH SEM',
    description:
      'Caterpillar có hơn 300 loại sản phẩm thể hiện sự tập trung của chúng tôi nhằm mang đến sự thành công cho khách hàng và đặt ra nhưng tiêu chuẩn cho cả ngành công nghiệp.',
    image: '/images/products/may-cong-trinh-sem.png',
  },
  {
    title: 'MÁY PHÁT ĐIỆN & ĐỘNG CƠ',
    description:
      'Caterpillar có hơn 300 loại sản phẩm thể hiện sự tập trung của chúng tôi nhằm mang đến sự thành công cho khách hàng và đặt ra nhưng tiêu chuẩn cho cả ngành công nghiệp.',
    image: '/images/products/may-phat-dien.png',
  },
  {
    title: 'DỊCH VU CHO THUÊ & MÁY QUA SỬ DỤNG',
    description:
      'Caterpillar có hơn 300 loại sản phẩm thể hiện sự tập trung của chúng tôi nhằm mang đến sự thành công cho khách hàng và đặt ra nhưng tiêu chuẩn cho cả ngành công nghiệp.',
    image: '/images/products/may-qua-su-dung.png',
  },
  {
    title: 'MÁY THỦY',
    description:
      'Caterpillar có hơn 300 loại sản phẩm thể hiện sự tập trung của chúng tôi nhằm mang đến sự thành công cho khách hàng và đặt ra nhưng tiêu chuẩn cho cả ngành công nghiệp.',
    image: '/images/products/may-thuy.png',
  },
  {
    title: 'THIẾT BỊ RỬA CÁT & NGHIỀN SÀNG',
    description:
      'Caterpillar có hơn 300 loại sản phẩm thể hiện sự tập trung của chúng tôi nhằm mang đến sự thành công cho khách hàng và đặt ra nhưng tiêu chuẩn cho cả ngành công nghiệp.',
    image: '/images/products/rua-cat-nghien-sang.png',
  },
]
//---------------------------------------------------------------------------------

export function BProductCategories() {
  return (
    <section>
      <div className="w-full container max-w-screen-xl mx-auto px-4">
        <div
          className="flex flex-col p-5 sm:p-10 lg:p-20 gap-10 md:gap-16 rounded-2xl bg-[#FAFAFA]"
          style={{
            boxShadow:
              '0px 4px 8px -2px rgba(16, 24, 40, 0.10), 0px 2px 4px -2px rgba(16, 24, 40, 0.06)',
          }}
        >
          {_CATEGORIES?.map((category, idx: number) => (
            <div
              key={idx}
              className="flex flex-col md:flex-row items-center gap-x-10 group md:odd:flex-row-reverse"
            >
              <div className="w-full md:w-1/2 flex flex-col gap-8">
                <Image
                  src={'/icons/icon-hexagon.svg'}
                  alt="category icon"
                  width={50}
                  height={40}
                />
                <div className="block md:hidden w-full h-auto aspect-[700/466] relative">
                  <div
                    className="w-full h-full absolute top-0 left-0 rounded-2xl group-hover:scale-105 md:group-hover:scale-110 ease-in-out transition-all duration-500"
                    style={{
                      backgroundImage: `url(${category?.image})`,
                      backgroundRepeat: 'no-repeat',
                      backgroundSize: 'cover',
                      backgroundPosition: 'center',
                    }}
                  ></div>
                </div>
                <div className="flex flex-col gap-2">
                  <Typography
                    variant={'h2'}
                    className="relative pb-4 text-gray-800 uppercase before:absolute before:bottom-0  before:content-[''] before:w-[80px] before:h-[8px] before:bg-[#FFCC00] before:left-0"
                  >
                    {category?.title}
                  </Typography>
                  <Typography variant={'body1'} className="text-gray-700 m-0">
                    {category?.description}
                  </Typography>
                </div>
                <MainButton
                  variant="secondary"
                  isDisabledIcon={true}
                  label={'Xem chi tiết'}
                  url={'#'}
                />
              </div>
              <div className="hidden md:block md:w-1/2 h-auto aspect-[700/466] relative">
                <div
                  className="w-full h-full absolute top-0 left-0 rounded-2xl group-hover:scale-110 ease-in-out transition-all duration-500"
                  style={{
                    backgroundImage: `url(${category?.image})`,
                    backgroundRepeat: 'no-repeat',
                    backgroundSize: 'cover',
                    backgroundPosition: 'center',
                  }}
                ></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
