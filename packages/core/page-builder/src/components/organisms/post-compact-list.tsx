import { t } from '@lingui/core/macro'
import { Typography } from '@ttplatform/ui/components'
import { cn } from '@ttplatform/ui/lib'
import { Loader2 } from 'lucide-react'
import React from 'react'
import { ECardType } from '../../libs'
import { PostCardCompact } from '../molecules'

export const PostCompactList: React.FC<{
  headerTitle: string
  postList: any[]
  className?: string
  type?: ECardType
  isLoading?: boolean
  error?: any
}> = ({ headerTitle, postList, className, type, isLoading, error }) => {
  const notFound = !isLoading && error && !postList.length
  return (
    <div
      className={cn(
        'bg-gray-50/80 shadow-sm rounded-lg  overflow-hidden',
        className,
      )}
    >
      <div className="p-6">
        <Typography variant="h4">{headerTitle}</Typography>
        <div className="mt-4 flex flex-col gap-6">
          {isLoading ? (
            <div className="w-full h-full flex justify-center">
              <Loader2 className="w-10 h-10 animate-spin" />
            </div>
          ) : (
            <>
              {notFound ? (
                <EmptyContent title={t`No data`} className="text-center" />
              ) : (
                <>
                  {postList.map((post) => (
                    <PostCardCompact
                      key={post.id}
                      card={post}
                      type={type || ECardType.News}
                    />
                  ))}
                </>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  )
}

const EmptyContent = ({
  title,
  className,
}: { title: string; className?: string }) => {
  return (
    <div
      className={cn(
        'flex flex-col items-center justify-center min-h-[300px] sm:min-h-[600px] text-center',
        className,
      )}
    >
      {title && <p className="text-gray-600 text-sm sm:text-lg">{title}</p>}
    </div>
  )
}
