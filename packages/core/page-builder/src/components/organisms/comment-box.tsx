'use client'
import { cn } from '@ttplatform/ui/lib'
import { MessageSquare } from 'lucide-react'
import { useState } from 'react'
import { formatDate } from '../../libs'
import CmsMedia from '../atoms/shared/cms-media'
import CommentInput from '../molecules/comment-input'
import LikeButton from '../molecules/like-button'

export interface CommentData {
  id: string
  author: {
    name: string
    avatarSrc?: string
  }
  content: string
  date: string // ISO string
  likes: number
  replies?: CommentData[]
  parentId?: string
}

interface CommentProps {
  comment: any
  level?: number
  onAddReply: (parentId: string, text: string) => void
}

export const CommentBox = ({
  comment,
  level = 0,
  onAddReply,
}: CommentProps) => {
  const [showReplyInput, setShowReplyInput] = useState(false)
  const maxLevel = 3
  const currentLevel = level > maxLevel ? maxLevel : level

  const handleAddReply = (text: string) => {
    onAddReply(comment?.documentId || '', text)
    setShowReplyInput(false)
  }

  return (
    <div
      className={`${currentLevel > 0 ? `ml-${currentLevel * 10} pl-16` : ''}`}
    >
      <div className="flex gap-3 mb-6">
        <div
          className={cn(
            'relative z-10 h-[50px] w-[56px] cursor-pointer overflow-hidden shrink-0',
          )}
          style={{
            clipPath:
              'polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%)',
          }}
        >
          {/* <Image
            src="https://i.pinimg.com/474x/80/c9/23/80c923e89c3d4a587e350232e9aae994.jpg"
            alt="profile"
            width={100}
            height={100}
            className="absolute top-1/2 left-1/2 z-20 h-[90px] w-[100px] -translate-x-1/2 -translate-y-1/2 object-cover"
            style={{ clipPath: 'inherit' }}
            onError={(e) => {
              e.currentTarget.src = '/fallback-thumbnail.jpg'
            }}
            loading="lazy"
          /> */}
          <div
            className="absolute top-1/2 left-1/2 z-20 h-[90px] w-[100px] -translate-x-1/2 -translate-y-1/3 object-cover"
            style={{ clipPath: 'inherit' }}
          >
            <CmsMedia
              media={comment.avatar}
              className="h-full w-full  "
              contentClassName="h-full"
            />
          </div>
        </div>
        <div className="flex-1 bg-white py-2 px-4 rounded-md">
          <div className="flex justify-between">
            <h4 className="font-medium text-gray-900">{comment.name}</h4>
            <span className="text-sm text-gray-400 font-medium">
              {formatDate(comment.createdAt || '')}
            </span>
          </div>
          <p className="text-gray-700 mt-1">{comment.content}</p>
          <div className="flex items-center gap-4 mt-2">
            <LikeButton initialCount={comment.count_like || 0} />
            <button
              className="text-sm text-yellow-500 flex items-center gap-1 hover:text-yellow-500"
              onClick={() => setShowReplyInput(!showReplyInput)}
            >
              <MessageSquare size={16} />
              <span>{comment.children?.length || 0}</span>
            </button>
          </div>

          {showReplyInput && (
            <div className="mt-3">
              <CommentInput
                onSubmit={handleAddReply}
                placeholder="Viết phản hồi..."
                buttonText="PHẢN HỒI"
              />
            </div>
          )}
        </div>
      </div>

      {comment.children && comment.children.length > 0 && (
        <div className="mt-2 space-y-4">
          {comment.children.map((reply: any) => (
            <CommentBox
              key={reply.documentId}
              comment={reply}
              level={currentLevel + 1}
              onAddReply={onAddReply}
            />
          ))}
        </div>
      )}
    </div>
  )
}
