'use client'
import { Button, Input } from '@ttplatform/ui/components'
import { cn } from '@ttplatform/ui/lib'
import { useState } from 'react'
import { CmsMedia } from '../atoms'

interface CommentInputProps {
  onSubmit: (text: string) => void
  placeholder?: string
  buttonText?: string
}

const CommentInput = ({
  onSubmit,
  placeholder = 'Viết bình luận...',
  buttonText = 'ĐĂNG BÌNH LUẬN',
}: CommentInputProps) => {
  const [comment, setComment] = useState('')

  const handleSubmit = () => {
    if (comment.trim()) {
      onSubmit(comment)
      setComment('')
    }
  }

  return (
    <div className="flex gap-3 items-start w-full">
      <div
        className={cn(
          'relative z-10 h-[50px] w-[56px] cursor-pointer overflow-hidden shrink-0',
        )}
        style={{
          clipPath:
            'polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%)',
        }}
      >
        {/* <Image
          src="https://i.pinimg.com/474x/80/c9/23/80c923e89c3d4a587e350232e9aae994.jpg"
          alt="profile"
          width={100}
          height={100}
          className="absolute top-1/2 left-1/2 z-20 h-[90px] w-[100px] -translate-x-1/2 -translate-y-1/2 object-cover"
          style={{ clipPath: 'inherit' }}
          onError={(e) => {
            e.currentTarget.src = '/images/no-image.png'
          }}
          loading="lazy"
        /> */}
        <div
          className="absolute top-1/2 left-1/2 z-20 h-[90px] w-[100px] -translate-x-1/2 -translate-y-1/2 object-cover"
          style={{ clipPath: 'inherit' }}
        >
          <CmsMedia
            media={undefined}
            className="h-full w-full !object-cover"
            contentClassName="h-full"
          />
        </div>
      </div>
      <div className="flex-1">
        <Input
          className="w-full border rounded-md p-3 h-12 bg-white focus:outline-none focus:ring-2 focus:ring-yellow-400 resize-none"
          placeholder={placeholder}
          value={comment}
          onChange={(e) => setComment(e.target.value)}
        />
      </div>
      <Button
        onClick={handleSubmit}
        className="bg-yellow-400 hover:bg-yellow-500 text-black font-semibold h-12"
      >
        {buttonText}
      </Button>
    </div>
  )
}

export default CommentInput
