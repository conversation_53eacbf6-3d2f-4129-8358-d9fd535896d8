import { SafeHTML, Typography } from '@ttplatform/ui/components'
import { cn } from '@ttplatform/ui/lib'
import { ReactNode } from 'react'
import { blocksToHtml } from '../../../libs/utils'
import { MainButton, SectionTitle } from '../../atoms'

/**
 * Ren<PERSON> heading from Strapi data
 */
export const renderHeading = (
  headingData: any,
  options: {
    fallbackText?: string
    hasUnderline?: boolean
    className?: string
    underlineColor?: string
  } = {},
): ReactNode => {
  if (!headingData?.text && !options.fallbackText) return null

  const text = headingData?.text || options.fallbackText || ''
  const styles = headingData?.styles || {}
  const variant = styles.variant || 'h2' // h1, h2, h3, h4, h5, h6, body1, etc.
  const color = styles.color || '#161924'
  const fontWeight = styles.font_weight || 'font-bold'
  const textAlign = styles.text_align || 'text-left'
  const lineHeight = styles.line_height || 1.2

  // Map text-align to SectionTitle align
  const getSectionTitleAlign = (textAlign: string) => {
    if (textAlign.includes('center')) return 'center'
    if (textAlign.includes('right')) return 'right'
    return 'left'
  }

  // For h1-h6 variants, use SectionTitle with underline
  if (['h1', 'h2', 'h3', 'h4', 'h5', 'h6'].includes(variant)) {
    return (
      <SectionTitle
        text={text}
        variant={variant as any}
        align={getSectionTitleAlign(textAlign)}
        underlineColor={options.underlineColor || 'bg-primary'}
        hasUnderline={options.hasUnderline ?? true}
        className={cn(fontWeight, options.className)}
        style={{
          color: color,
          lineHeight: lineHeight,
        }}
      />
    )
  }

  return (
    <Typography
      variant={variant as any}
      className={cn(fontWeight, textAlign, options.className)}
      style={{
        color: color,
        lineHeight: lineHeight,
      }}
      gutterBottom
    >
      {text}
    </Typography>
  )
}

/**
 * Render sub-heading from Strapi data
 */
export const renderSubHeading = (
  subHeadingData: any,
  options: {
    className?: string
  } = {},
): ReactNode => {
  if (!subHeadingData?.text) return null

  const styles = subHeadingData.styles || {}
  const variant = styles.variant || 'body1'
  const color = styles.color || '#2d303a'
  const fontWeight = styles.font_weight || 'font-normal'
  const textAlign = styles.text_align || 'text-justify'
  const lineHeight = styles.line_height || 1.5

  return (
    <Typography
      variant={variant as any}
      className={cn('mb-0', fontWeight, textAlign, options.className)}
      style={{
        color: color,
        lineHeight: lineHeight,
      }}
      gutterBottom
    >
      {subHeadingData.text}
    </Typography>
  )
}

/**
 * Render description from Strapi data
 */
export const renderDescription = (
  descriptionData: any,
  options: {
    locale?: string
    className?: string
  } = {},
): ReactNode => {
  if (!descriptionData) return null

  if (Array.isArray(descriptionData)) {
    return (
      <SafeHTML
        html={blocksToHtml(descriptionData, {
          responsive: true,
          className: 'strapi-description',
          locale: options.locale || 'vi',
        })}
        className={cn('text-base leading-relaxed', options.className)}
      />
    )
  }

  if (typeof descriptionData === 'string') {
    // Plain text
    return (
      <Typography
        variant="body2"
        className={cn('text-base leading-relaxed', options.className)}
      >
        {descriptionData}
      </Typography>
    )
  }

  // Object with text and styles
  if (descriptionData?.text) {
    const styles = descriptionData.styles || {}
    const variant = styles.variant || 'body2'
    const color = styles.color || '#344054'
    const fontWeight = styles.font_weight || 'font-normal'
    const textAlign = styles.text_align || 'text-justify'
    const lineHeight = styles.line_height || 1.5

    return (
      <Typography
        variant={variant as any}
        className={cn(fontWeight, textAlign, options.className)}
        style={{
          color: color,
          lineHeight: lineHeight,
        }}
      >
        {descriptionData.text}
      </Typography>
    )
  }

  return null
}

/**
 * Render buttons from Strapi data
 */
export const renderButtons = (
  buttonsData: any[],
  options: {
    containerClassName?: string
    justify?: 'start' | 'center' | 'end'
    type?: 'button' | 'submit' | 'reset'
    fallbackLabel?: string
  } = {},
): ReactNode => {
  if (!buttonsData || buttonsData.length === 0) return null

  const justifyClass = {
    start: 'justify-start',
    center: 'justify-center',
    end: 'justify-end',
  }[options.justify || 'start']

  return (
    <div
      className={cn(
        'flex gap-4 flex-wrap',
        justifyClass,
        options.containerClassName,
      )}
    >
      {buttonsData.map((button, index) => {
        const validVariants = [
          'primary',
          'secondary',
          'outline',
          'text',
          'icon',
        ]
        const variant = validVariants.includes(button.variant || '')
          ? button.variant
          : 'primary'

        return (
          <MainButton
            key={index}
            label={button.text || options.fallbackLabel || 'Get Started'}
            url={button.URL || '#'}
            variant={variant as any}
            target={button.target as any}
            type={options.type}
          />
        )
      })}
    </div>
  )
}
