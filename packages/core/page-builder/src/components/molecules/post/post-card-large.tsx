'use client'
import { t } from '@lingui/core/macro'
import { Card, CardContent, Typography } from '@ttplatform/ui/components'
import { cn } from '@ttplatform/ui/lib'
import { format } from 'date-fns'
import { motion } from 'framer-motion'
import Link from 'next/link'
import { CalendarIcon, CmsMedia, MainButton } from '../..'
import { ECardType, INewsCard, IPromotionCard, TPostCard } from '../../../libs'

const cardVariants = {
  initial: { opacity: 0, y: 20 },
  animate: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.5,
      when: 'beforeChildren',
      staggerChildren: 0.1,
    },
  },
  hover: { transition: { duration: 0.3 } },
}

const imageVariants = {
  initial: { scale: 1 },
  hover: { scale: 1.05, transition: { duration: 0.3 } },
}

const contentVariants = {
  initial: { opacity: 0 },
  animate: { opacity: 1 },
}

export const PostCardLarge = (props: TPostCard | any) => {
  const { coverImage, title, excerpt, type, slug, category } = props

  const getCardUrl = () => {
    if (!slug) return '#'

    switch (type) {
      case ECardType.Promotion:
        return `/promotions/${slug}`
      default:
        return `/news/${slug}`
    }
  }

  const renderMetadata = () => {
    switch (type) {
      case ECardType.Promotion: {
        const promotion = props as IPromotionCard
        return (
          <div className="flex flex-col text-base font-semibold">
            <div className="flex font-medium text-sm text-gray-600">
              <CalendarIcon className="mr-2" />
              <span>{t`Valid until`}</span>
            </div>
            <span className="mt-1 text-base text-red-700">
              {promotion.validUntil
                ? format(new Date(promotion?.validUntil), 'dd/MM/yyyy')
                : 'Liên hệ để biết thêm chi tiết'}
            </span>
          </div>
        )
      }

      case ECardType.News: {
        const NewsProps = props as INewsCard
        return (
          <div className="flex flex-col text-base gap-2 font-semibold">
            {category && (
              <Typography variant="body1" className="text-orange-400">
                {category}
              </Typography>
            )}
            <div className="flex text-red-700 items-center">
              <CalendarIcon className="mr-2" />
              <span className="text-red-700">
                {NewsProps.publishedAt
                  ? format(new Date(NewsProps.publishedAt), 'dd MMMM yyyy')
                  : ''}
              </span>
            </div>
          </div>
        )
      }
      // case ECardType.LandingNews: {
      //   const NewsProps = props as ILandingNewsCard
      //   return (
      //     <div className="flex flex-col text-base gap-2 font-semibold">
      //       <Typography variant="body1" className="text-orange-400">
      //         {(NewsProps.topic &&
      //           (Array.isArray(NewsProps.topic)
      //             ? NewsProps.topic[0]
      //             : NewsProps.topic)) ||
      //           'Category'}
      //       </Typography>
      //       <div className="flex text-red-700 items-center">
      //         <CalendarIcon className="mr-2" />
      //         <span className="text-red-700">
      //           {NewsProps.publishedAt
      //             ? format(new Date(NewsProps.publishedAt), 'dd/MM/yyyy')
      //             : ''}
      //         </span>
      //       </div>
      //     </div>
      //   )
      // }

      default:
        return null
    }
  }

  return (
    <Link href={getCardUrl()}>
      <motion.div
        variants={cardVariants}
        initial="initial"
        animate="animate"
        whileHover="hover"
        className="h-full w-full"
      >
        <Card className="overflow-hidden h-full w-full pt-0 border-none shadow-lg gap-0 py-0">
          <div className="relative w-full aspect-[3/2] overflow-hidden">
            <motion.div variants={imageVariants} className="absolute inset-0">
              {/* <Image
                src={coverImage || '/placeholder.svg'}
                alt={title}
                fill
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                className="object-cover"
                priority={false}
              /> */}
              <CmsMedia
                media={coverImage}
                alt={title}
                className="w-full h-full !object-cover"
                contentClassName="w-full h-full"
              />
            </motion.div>
          </div>
          <div
            className="w-full h-1"
            style={{
              backgroundImage:
                'linear-gradient(270deg, rgb(255, 222, 89) 0%, rgb(255, 211, 38) 16%, rgb(255, 204, 0) 42%)',
            }}
          />
          <CardContent className="p-4 md:p-6 flex flex-col">
            <Typography
              variant="h6"
              className={cn(
                'font-bold text-xl mb-2 line-clamp-2 uppercase',
                type === ECardType.LandingNews && 'min-h-[56px]',
              )}
            >
              {title}
            </Typography>
            <Typography
              variant="body1"
              className="text-gray-600 mb-4 text-base 3xl:text-lg line-clamp-2"
            >
              {excerpt}
            </Typography>
            <div className="flex justify-between items-start mt-auto">
              {renderMetadata()}
              <motion.div
                variants={contentVariants}
                transition={{ delay: 0.4 }}
              >
                <MainButton />
              </motion.div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </Link>
  )
}
