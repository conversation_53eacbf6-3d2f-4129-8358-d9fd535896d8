'use client'
import { Typography } from '@ttplatform/ui/components'
import { useRef, useState } from 'react'
import type { Swiper as SwiperType } from 'swiper'
import { Swiper, SwiperSlide } from 'swiper/react'
import { IBackground, THeadingSchema } from '../../../libs/types'
import CmsMedia from './cms-media'
import MainButton from './main-button'

interface SlideItemProps {
  background?: IBackground
  content?: THeadingSchema
  locale?: string
}

const SlideItem = ({ background, content }: SlideItemProps) => {
  return (
    <div className="group relative w-full aspect-[2/3] overflow-hidden rounded-xl">
      <CmsMedia
        media={background?.image}
        className="object-cover rounded-xl transition-transform duration-300 w-full h-full group-hover:scale-105"
        fill
        priority
      />
      <div
        style={{
          background:
            'linear-gradient(180deg, rgba(34, 34, 34, 0.00) 0%, #222 100%)',
        }}
        className="absolute bottom-0 z-10 w-full h-1/2"
      ></div>
      <div className="absolute inset-0 flex flex-col justify-end p-8 rounded-xl z-20">
        <Typography
          variant={content?.heading?.styles?.variant as any}
          style={{ color: content?.heading?.styles?.color }}
          className="line-clamp-2 md:line-clamp-none"
        >
          {content?.heading?.text}
        </Typography>
        <div className="mt-3 h-0 w-17 bg-yellow-400 group-hover:h-1.5 "></div>
        <div className="sm:overflow-y-auto scrollbar-hide">
          <Typography
            className="hidden my-5 max-w-md line-clamp-3 lg:line-clamp-11 sm:block sm:opacity-0 sm:h-0 sm:my-0 sm:group-hover:opacity-100 sm:group-hover:h-auto sm:group-hover:my-5 transition-all duration-300"
            variant={content?.sub_heading?.styles?.variant as any}
            style={{ color: content?.sub_heading?.styles?.color }}
          >
            {content?.sub_heading?.text}
          </Typography>
          <div className="block sm:opacity-0 sm:h-0 sm:group-hover:opacity-100 sm:group-hover:h-auto transition-all duration-300">
            {content?.buttons?.map((button: any, index: number) => (
              <div className="flex flex-col gap-4" key={index}>
                <MainButton
                  className="w-full md:w-[200px]"
                  label={button.text}
                  url={button.URL || ''}
                />
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}

interface CarouselProps {
  slides: SlideItemProps[]
  locale?: string
}

export function ExpandCardSlider({ slides }: CarouselProps) {
  const [isEnd, setIsEnd] = useState(false)
  const swiperRef = useRef<SwiperType | null>(null)

  const handleSwiperInit = (swiper: SwiperType) => {
    swiperRef.current = swiper
  }

  return (
    <div className="relative w-full overflow-hidden">
      <Swiper
        onSwiper={handleSwiperInit}
        spaceBetween={24}
        slidesPerView={1.5}
        speed={600}
        pagination={{ clickable: true }}
        breakpoints={{
          640: {
            slidesPerView: 2,
            spaceBetween: 20,
            slidesOffsetBefore: 8,
          },
          1024: {
            slidesPerView: 2.5,
            spaceBetween: 24,
          },
          1280: {
            slidesPerView: 3,
            spaceBetween: 28,
          },
        }}
        className="w-full"
        onSlideChange={(swiper) => setIsEnd(swiper.isEnd)}
        onReachEnd={() => setIsEnd(true)}
        onFromEdge={() => setIsEnd(false)}
      >
        {slides.map((slide, index) => (
          <SwiperSlide key={index}>
            <SlideItem background={slide.background} content={slide.content} />
          </SwiperSlide>
        ))}
      </Swiper>
      <div
        className={`absolute top-0 right-0 h-full w-[150px] sm:w-[200px] lg:w-[300px] pointer-events-none z-10 transition-opacity duration-300 ${
          isEnd ? 'opacity-0' : 'opacity-100'
        }`}
        style={{
          background:
            'linear-gradient(to left, rgba(255, 255, 255, 0.7), transparent)',
        }}
      />
    </div>
  )
}

export default ExpandCardSlider
