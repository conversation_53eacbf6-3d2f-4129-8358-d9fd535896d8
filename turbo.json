{"$schema": "https://turbo.build/schema.json", "ui": "stream", "globalEnv": ["NODE_ENV", "DATABASE_URL", "PORT", "NODE_ENV", "JWT_SECRET", "JWT_EXPIRES_IN", "REFRESH_TOKEN_SECRET", "REFRESH_TOKEN_EXPIRES_IN", "AUTH_GOOGLE_CLIENT_ID", "AUTH_GOOGLE_CLIENT_SECRET", "AUTH_GOOGLE_REDIRECT_URI", "AUTH_GOOGLE_SCOPE", "NEXT_PUBLIC_BASE_URL", "NEXT_PUBLIC_API_URL", "NEXT_PUBLIC_SESSION_KEY", "NEXT_PUBLIC_ROOT_DOMAIN", "NEXTAUTH_URL", "NEXTAUTH_SECRET", "NEXT_PUBLIC_LANGUAGES", "NEXT_PUBLIC_DEFAULT_LOCALE", "NEXT_PUBLIC_AUTH_GOOGLE_CLIENT_ID", "NEXT_PUBLIC_AUTH_GOOGLE_CLIENT_SECRET", "NEXT_PUBLIC_AUTH_FACEBOOK_CLIENT_ID", "NEXT_PUBLIC_AUTH_FACEBOOK_CLIENT_SECRET", "NEXT_PUBLIC_ANALYTIC_API_URL", "NEXT_PUBLIC_ANALYTIC_API_KEY"], "globalDependencies": ["tsconfig.json"], "tasks": {"build": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", ".env*"], "outputs": ["dist/**", ".next/**", "!.next/cache/**"]}, "cms:prebuild": {"cache": false, "persistent": true}, "db:migrate:dev": {"cache": false, "persistent": true}, "db:migrate:deploy": {"cache": false}, "db:push": {"cache": false}, "db:seed": {"cache": false}, "db:studio": {"cache": false, "persistent": true}, "dev": {"cache": false, "persistent": true}, "generate": {"dependsOn": ["^generate"], "cache": false}, "lint": {"cache": false}, "lint:fix": {"cache": false}, "test": {"cache": false}, "check": {"cache": false}, "format": {"cache": false}, "lingui:extract": {"cache": false}, "lingui:compile": {"cache": false}, "generate-search-assets": {"cache": false}}}