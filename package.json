{"name": "ptc-platform", "version": "1.0.0", "description": "Phu Thai CAT Platform", "private": true, "author": "Techtown Development", "license": "MIT", "prisma": {"schema": "packages/database/prisma/schema.prisma", "seed": "tsx packages/database/src/seed.ts"}, "scripts": {"db:migrate:deploy": "turbo run db:migrate:deploy", "db:migrate:dev": "turbo run db:migrate:dev", "db:push": "turbo run db:push", "db:seed": "turbo run db:seed", "dev": "turbo run dev --filter=cms... --filter=web-v1... --filter=web", "cms:prebuild": "turbo run prebuild --filter=cms... --filter=web-v1... ", "build": "turbo run build", "generate": "turbo run generate", "lint": "turbo run lint", "lint:fix": "turbo lint:fix", "check": "turbo run check", "format": "turbo run format", "clean": "turbo clean && rm -rf node_modules", "lingui:extract": "turbo run lingui:extract", "lingui:compile": "turbo run lingui:compile", "changeset": "changeset", "version-packages": "changeset version", "release": "turbo build --filter=docs^... && changeset publish", "ui:add:component": "turbo ui:add --filter=@ttplatform/ui --", "prepare": "lefthook install", "storybook": "turbo dev --filter=@ttplatform/storybook", "build:storybook": "turbo build --filter=@ttplatform/storybook", "test": "turbo test", "test:build": "turbo test:with-build", "test:e2e": "turbo test:e2e", "test:e2e:watch": "turbo test:e2e -- --headed", "test:e2e:debug": "turbo test:e2e -- --debug", "test:e2e:build": "turbo test:e2e:with-build", "test:ui": "turbo test -- --ui", "test:cov": "turbo test:cov", "test:cov:ui": "turbo test:cov:ui"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@changesets/cli": "2.28.1", "@commitlint/cli": "^19.8.0", "@commitlint/config-conventional": "^19.8.0", "@vitest/ui": "3.1.1", "ajv": "^8.17.1", "lefthook": "1.11.8", "prisma": "^6.6.0", "tsx": "4.19.3", "turbo": "^2.5.4", "vitest": "3.1.1"}, "engines": {"node": ">=18"}, "dependencies": {"@prisma/client": "^6.6.0", "@tsparticles/engine": "^3.8.1", "@tsparticles/react": "^3.0.0", "@tsparticles/slim": "^3.8.1", "crypto": "^1.0.1", "passport-jwt": "^4.0.1", "react-dropzone": "^14.3.8", "react-fast-marquee": "^1.6.5"}, "packageManager": "bun@1.2.5", "workspaces": ["apps/*", "packages/*", "packages/*/*"], "trustedDependencies": ["@biomejs/biome", "esbuild", "lefthook"]}